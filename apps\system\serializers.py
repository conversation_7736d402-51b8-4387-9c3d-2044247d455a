from rest_framework import serializers
from .models import SystemConfig, NotifyModel


class SystemConfigSerializer(serializers.ModelSerializer):
    """系统配置序列化器"""
    class Meta:
        model = SystemConfig
        fields = '__all__'
        read_only_fields = ['created_by', 'created_at', 'updated_at']


class SystemSettingsSerializer(serializers.Serializer):
    """系统设置序列化器"""
    systemName = serializers.CharField(max_length=100)
    logo = serializers.CharField(max_length=255, allow_blank=True)
    copyright = serializers.CharField(max_length=100)
    timezone = serializers.Char<PERSON>ield(max_length=50)
    dateFormat = serializers.CharField(max_length=50)
    
    passwordPolicy = serializers.DictField(
        allow_empty=False
    )
    
    sessionTimeout = serializers.IntegerField(min_value=1)
    maxLoginAttempts = serializers.IntegerField(min_value=1)
    
    notifications = serializers.DictField(
        allow_empty=False
    )


class NotificationsSerializers(serializers.ModelSerializer):
    """
    系统通知序列化器
    """
    class Meta:
        model = NotifyModel
        fields = '__all__'
