<template>
  <div>
    <button 
      type="button" 
      class="flex text-sm bg-gray-800 rounded-full focus:ring-4 focus:ring-gray-300"
      @click="toggleDropdown"
    >
      <span class="sr-only">打开用户菜单</span>
      <svg class="w-8 h-8 rounded-full text-white" fill="currentColor" viewBox="0 0 24 24">
        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 3c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 14.2c-2.5 0-4.71-1.28-6-3.22.03-1.99 4-3.08 6-3.08 1.99 0 5.97 1.09 6 3.08-1.29 1.94-3.5 3.22-6 3.22z"/>
      </svg>
    </button>

    <div 
      v-show="isOpen"
      class="absolute right-4 z-50 mt-2 w-56 text-base list-none bg-white divide-y divide-gray-100 rounded shadow"
    >
      <div class="px-4 py-3">
        <span class="block text-sm text-gray-900">{{ authStore.user?.username }}</span>
        <span class="block text-sm text-gray-500 truncate">{{ authStore.user?.email }}</span>
        <span class="block text-xs text-gray-500 mt-1">
          角色: {{ getRoleName(authStore.user?.role) }}
        </span>
      </div>
      <ul class="py-1">
        <li>
          <a 
            href="#" 
            class="dropdown-item"
            @click="handleLogout"
          >
            退出登录
          </a>
        </li>
      </ul>
    </div>

    <!-- 点击外部关闭下拉菜单 -->
    <div 
      v-if="isOpen"
      class="fixed inset-0 z-40"
      @click="closeDropdown"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useAuthStore } from '~/stores/auth'
import { getRoleName } from '~/utils/roles'

const authStore = useAuthStore()
const isOpen = ref(false)

// 切换下拉菜单
const toggleDropdown = () => {
  isOpen.value = !isOpen.value
}

// 关闭下拉菜单
const closeDropdown = () => {
  isOpen.value = false
}

// 处理登出
const handleLogout = async () => {
  await authStore.logout()
}
</script>

<style scoped>
.dropdown-item {
  @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100;
}
</style>