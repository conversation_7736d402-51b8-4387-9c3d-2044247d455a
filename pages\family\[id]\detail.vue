<template>
  <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- 页面标题 -->
    <div class="mb-8">
      <div class="flex justify-between items-center">
        <div>
          <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent">
            {{ $t('family.detail.title') }}
          </h1>
          <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
            {{ $t('family.detail.subtitle') }}
          </p>
        </div>
        <NuxtLink to="/family"
          class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          <svg class="-ml-1 mr-2 h-5 w-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
          </svg>
          {{ $t('all.back') }}
        </NuxtLink>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="pending" class="flex justify-center items-center h-64">
      <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
    </div>

    <!-- 错误提示 -->
    <div v-else-if="error" class="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
      <div class="flex">
        <div class="flex-shrink-0">
          <svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd"
              d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
              clip-rule="evenodd" />
          </svg>
        </div>
        <div class="ml-3">
          <p class="text-sm text-red-700">{{ error }}</p>
        </div>
      </div>
    </div>

    <div v-else class="grid grid-cols-1 gap-6">
      <!-- 基本信息 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-6 text-gray-900 dark:text-gray-100">
          {{ $t('family.form.basic_information') }}
        </h2>

        <!-- Logo部分 -->
        <div class="mb-8">
          <div class="flex items-center mb-2">
            <img v-if="data?.logo_url" :src="data.logo_url" class="w-32 h-32 rounded-lg object-cover" :alt="data?.name">
            <div v-else class="w-32 h-32 rounded-lg bg-gray-200 dark:bg-gray-700 flex items-center justify-center">
              <Icon name="heroicons:photo" class="w-16 h-16 text-gray-500" />
            </div>
          </div>
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ $t('family.detail.virus_family_identification') }}
          </p>
        </div>

        <!-- 基本信息表格 -->
        <div class="grid grid-cols-2 gap-y-4 gap-x-8">
          <div class="flex items-center">
            <span :class="['text-gray-500 dark:text-gray-400', classes]">{{ $t('table.family_name') }}：</span>
            <span class="text-gray-900 dark:text-gray-100">{{ data?.name || '-' }}</span>
          </div>
          <div class="flex items-center">
            <span :class="['text-gray-500 dark:text-gray-400', classes]">{{ $t('table.first_appearance_time') }}：</span>
            <span class="text-gray-900 dark:text-gray-100">{{ formatDate(data?.first_seen_time) || '-' }}</span>
          </div>
          <div class="flex items-center">
            <span :class="['text-gray-500 dark:text-gray-400', classes]">{{ $t('table.encryption_algorithm') }}：</span>
            <span class="text-gray-900 dark:text-gray-100">{{ data?.encryption_algorithm || '-' }}</span>
          </div>
          <div class="flex items-center">
            <span :class="['text-gray-500 dark:text-gray-400', classes]">{{ $t('table.ransom_method') }}：</span>
            <span class="text-gray-900 dark:text-gray-100">{{ data?.ransom_method || '-' }}</span>
          </div>
          <div class="flex items-center">
            <span :class="['text-gray-500 dark:text-gray-400', classes]">{{ $t('table.infection_type') }}：</span>
            <span class="text-gray-900 dark:text-gray-100">{{ data?.infection_type || '-' }}</span>
          </div>
          <div class="flex items-center">
            <span :class="['text-gray-500 dark:text-gray-400', classes]">{{ $t('table.wallet_address') }}：</span>
            <span class="text-gray-900 dark:text-gray-100">{{ data?.wallet_address || '-' }}</span>
          </div>
          <div class="flex items-center">
            <span :class="['text-gray-500 dark:text-gray-400', classes]">{{ $t('table.public_decryptor') }}：</span>
            <span :class="[
              'px-2 py-1 text-xs font-medium rounded-full',
              data?.public_decrypt
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
            ]">
              {{ data?.public_decrypt ? $t('all.yes') : $t('all.no') }}
            </span>
          </div>
          <div class="flex items-center">
            <span :class="['text-gray-500 dark:text-gray-400', classes]">
              {{ $t('family.form.encryption_suffix') }}：
            </span>
            <span class="text-gray-900 dark:text-gray-100">{{ data?.encrypted_suffix || '-' }}</span>
          </div>
        </div>

        <!-- 攻击路线和IOC特征 -->
        <div class="mt-8 space-y-6">
          <div>
            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
              {{ $t('family.form.attack_route') }}
            </h3>
            <p class="text-gray-900 dark:text-gray-100 whitespace-pre-wrap">{{ data?.attack_vector || '-' }}</p>
          </div>
          <div>
            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
              {{ $t('family.form.family_profile') }}
            </h3>
            <p class="text-gray-900 dark:text-gray-100 whitespace-pre-wrap">{{ data?.description || '-' }}</p>
          </div>
          <div>
            <h3 class="text-sm font-medium text-gray-500 dark:text-gray-400 mb-2">
              {{ $t('family.form.ioc_information') }}
            </h3>
            <div class="text-gray-900 dark:text-gray-100" v-html="data?.ioc || '-'"></div>
          </div>
        </div>
      </div>

      <!-- 勒索信 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
          {{ $t('family.form.ransom_note') }}
        </h2>
        <div v-if="data?.ransom_notes?.length" class="space-y-4">
          <div v-for="(note, index) in data.ransom_notes" :key="index"
            class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div class="mb-2">
              <span class="text-gray-500 dark:text-gray-400">
                {{ $t('family.form.ransom_note_name') }}：</span>
              <span class="text-gray-900 dark:text-gray-100">{{ note.name }}</span>
              <span class="ml-4 text-sm text-gray-500">
                {{ $t('table.update_time') }}: {{ formatDate(note.updated_at) }}</span>
            </div>
            <div>
              <span class="text-gray-500 dark:text-gray-400">
                {{ $t('family.form.text_content') }}：</span>
              <p class="mt-1 text-gray-900 dark:text-gray-100 whitespace-pre-wrap">{{ note.content }}</p>
            </div>
          </div>
        </div>
        <div v-else class="text-gray-500 dark:text-gray-400">{{ $t('all.no_data') }}</div>
      </div>

      <!-- 勒索地址 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
          {{ $t('family.form.ransomware_address') }}
        </h2>
        <div v-if="data?.ransom_addresses?.length" class="space-y-4">
          <div v-for="(address, index) in data.ransom_addresses" :key="index"
            class="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
            <div class="mb-2">
              <span class="text-gray-500 dark:text-gray-400">
                {{ $t('family.form.address_option') }}：</span>
              <span class="text-gray-900 dark:text-gray-100">{{ address.address_option }}</span>
              <span class="ml-4 text-sm text-gray-500">
                {{ $t('table.update_time') }}: {{ formatDate(address.updated_at) }}</span>
            </div>
            <div>
              <span class="text-gray-500 dark:text-gray-400">{{ $t('family.detail.address') }}：</span>
              <p class="mt-1 text-gray-900 dark:text-gray-100 whitespace-pre-wrap">{{ address.content }}</p>
            </div>
          </div>
        </div>
        <div v-else class="text-gray-500 dark:text-gray-400">{{ $t('all.no_data') }}</div>
      </div>

      <!-- 常用工具 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
          {{ $t('family.form.common_tools') }}
        </h2>
        <div class="overflow-x-auto">
          <table v-if="data?.tools?.length" class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
              <tr>
                <th
                  class="min-w-36 px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {{ $t('family.form.table.tool_name') }}
                </th>
                <th
                  class="min-w-32 px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {{ $t('family.form.table.tool_type') }}
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {{ $t('family.form.table.tool_description') }}
                </th>
                <th
                  class="min-w-36 px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {{ $t('table.update_time') }}
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {{ $t('family.form.table.attachment') }}
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-for="(tool, index) in data.tools" :key="index">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {{ tool.name }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {{ tool.tool_type }}
                </td>
                <td class="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                  {{ tool.introduction }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {{ formatDate(tool.updated_at) }}
                </td>
                <td class="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                  <a v-if="tool.file_url" :href="tool.file_url" target="_blank"
                    class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                    {{ $t('family.detail.download_tool') }}
                  </a>
                  <span v-else class="text-gray-500">
                    {{ $t('family.detail.no_file') }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
          <div v-else class="text-gray-500 dark:text-gray-400">{{ $t('all.no_data') }}</div>
        </div>
      </div>

      <!-- 谈判记录 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
          {{ $t('family.form.negotiation_record') }}
        </h2>
        <div class="overflow-x-auto">
          <table v-if="data?.negotiations?.length" class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
              <tr>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {{ $t('family.form.table.date') }}
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {{ $t('family.form.table.initial_amount') }}
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {{ $t('family.form.table.final_delivery') }}
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {{ $t('family.form.table.whether_paid') }}
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {{ $t('family.detail.related_documents') }}
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-for="(negotiation, index) in data.negotiations" :key="index">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {{ formatDate(negotiation.date) }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {{ negotiation.initial_amount }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {{ negotiation.final_delivery_amount }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap">
                  <span :class="[
                    'px-2 py-1 text-xs font-medium rounded-full',
                    negotiation.is_pay
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                      : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
                  ]">
                    {{ negotiation.is_pay ? $t('all.yes') : $t('all.no') }}
                  </span>
                </td>
                <td class="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">
                  <a v-if="negotiation.file_url" :href="negotiation.file_url" target="_blank"
                    class="text-blue-600 hover:text-blue-800">
                    {{ $t('all.download') }}
                  </a>
                  <span v-else>-</span>
                </td>
              </tr>
            </tbody>
          </table>
          <div v-else class="text-gray-500 dark:text-gray-400">{{ $t('all.no_data') }}</div>
        </div>
      </div>

      <!-- 受害者信息 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
          {{ $t('family.form.victim_information') }}
        </h2>
        <div class="overflow-x-auto">
          <table v-if="data?.victims?.length" class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
              <tr>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {{ $t('family.form.table.victim') }}
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {{ $t('family.form.table.location') }}
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {{ $t('family.form.table.official_website') }}
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {{ $t('family.form.table.date') }}
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {{ $t('family.form.table.introduction') }}
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-for="victim in data.victims" :key="victim.id">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ victim.victim }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ victim.location }}
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  <a :href="victim.official_website" target="_blank"
                    class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                    {{ $t('family.detail.visit') }}
                  </a>
                </td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  {{ formatDate(victim.date) }}</td>
                <td class="px-6 py-4 text-sm text-gray-900 dark:text-gray-100">{{ victim.introduction }}</td>
              </tr>
            </tbody>
          </table>
          <div v-else class="text-gray-500 dark:text-gray-400">{{ $t('all.no_data') }}</div>
        </div>
      </div>

      <!-- 病毒样本 -->
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h2 class="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">
          {{ $t('family.form.virus_sample') }}
        </h2>
        <div class="overflow-x-auto">
          <table v-if="data?.virus_samples?.length" class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead>
              <tr>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {{ $t('family.form.table.sample_name') }}
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {{ $t('table.update_time') }}
                </th>
                <th
                  class="px-6 py-3 bg-gray-50 dark:bg-gray-700 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  {{ $t('family.form.table.sample_file') }}
                </th>
              </tr>
            </thead>
            <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              <tr v-for="sample in data.virus_samples" :key="sample.id">
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{ sample.name }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">{{
                  formatDate(sample.updated_at) }}</td>
                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100">
                  <a v-if="sample.file_url" :href="sample.file_url" target="_blank"
                    class="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300">
                    {{ $t('all.download') }}
                  </a>
                  <span v-else class="text-gray-500">
                    {{ $t('family.detail.no_file') }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
          <div v-else class="text-gray-500 dark:text-gray-400">{{ $t('all.no_data') }}</div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { useToast } from '~/composables/useToast'
import { familyApi } from '~/api/family'

const { locale } = useI18n()
const route = useRoute()
const toast = useToast()
const classes = computed(() => {
  return {
    'w-28': locale.value === 'zh',
    'w-48': locale.value === 'en'
  }
})

// 格式化日期的工具函数
const formatDate = (date) => {
  if (!date) return '-'
  const options = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
  }
  return new Intl.DateTimeFormat('zh-CN', options).format(new Date(date))
}

// 获取病毒家族详情
const { data, pending, error, refresh } = await useLazyAsyncData(
  'familyDetail',
  async () => {
    try {
      const response = await familyApi.getFamilyDetail(route.params.id)
      return response
    } catch (err) {
      console.error('Failed to fetch family detail:', err)
      toast.error('获取病毒家族详情失败')
      return null
    }
  },
  {
    server: false,
    lazy: true
  }
)
</script>