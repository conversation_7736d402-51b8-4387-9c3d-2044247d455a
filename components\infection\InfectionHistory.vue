<template>
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6">
    <div class="mb-6">
      <h2 class="text-lg font-medium dark:text-white">{{ $t('infection.infection_history') }}</h2>
    </div>

    <DataTable :columns="tableColumns" :data="infectionHistory" :loading="loading" :pagination="pagination"
      @page-change="handlePageChange">
      <!-- 感染时间列 -->
      <template #system_time="{ row }">
        <div class="flex items-center space-x-2">
          <span class="dark:text-gray-200">{{ formatTime(row.system_time) }}</span>
        </div>
      </template>

      <!-- IP地址列 -->
      <template #ip_address="{ row }">
        <div class="flex items-center">
          <Icon name="heroicons:globe-alt" class="w-4 h-4 mr-2 text-gray-400 dark:text-gray-500" />
          <span class="dark:text-gray-200">{{ row.ip_address }}</span>
        </div>
      </template>

      <!-- 主机名列 -->
      <template #hostname="{ row }">
        <span class="dark:text-gray-200">{{ row.hostname }}</span>
      </template>

      <!-- 用户名列 -->
      <template #username="{ row }">
        <span class="dark:text-gray-200">{{ row.username }}</span>
      </template>
    </DataTable>
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { useToast } from '~/composables/useToast'
import { formatTime } from '~/utils/format'
import { infectionApi } from '~/api/infection'
import DataTable from '~/components/common/DataTable.vue'

const { t } = useI18n()
const toast = useToast()
const props = defineProps({
  deviceId: {
    type: String,
    required: true
  },
  exerciseId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update:currentInfection'])

// 状态管理
const loading = ref(false)
const infectionHistory = ref([])
const total = ref(0)

// 表格列配置
const tableColumns = [
  { title: t('infection.infection_time'), key: 'system_time', slot: 'system_time', width: 180 },
  { title: t('infection.hostname'), key: 'hostname', slot: 'hostname', width: 150 },
  { title: t('table.username'), key: 'username', slot: 'username', width: 150 },
  { title: t('table.ip_address'), key: 'ip_address', slot: 'ip_address', width: 150 }
]

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)

// 计算属性
const pagination = computed(() => ({
  currentPage: currentPage.value,
  pageSize: pageSize.value,
  total: total.value
}))

// 获取感染历史记录
const fetchInfectionHistory = async (page = 1) => {
  try {
    loading.value = true
    const params = {
      page,
      page_size: pagination.value.pageSize,
      device_id: props.deviceId,
      exercise_id: props.exerciseId
    }
    const response = await infectionApi.getDeviceInfections(params)
    infectionHistory.value = response.results
    total.value = response.count

    // 设置当前感染信息
    if (response.results.length > 0) {
      emit('update:currentInfection', response.results[0])
    }
  } catch (error) {
    toast.error('获取感染历史失败')
    console.error('获取感染历史失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理分页
const handlePageChange = (page) => {
  currentPage.value = page
  fetchInfectionHistory(page)
}



// 监听分页变化
watch([currentPage], () => {
  fetchInfectionHistory(currentPage.value)
})

// 初始化
onMounted(() => {
  fetchInfectionHistory()
})
</script>