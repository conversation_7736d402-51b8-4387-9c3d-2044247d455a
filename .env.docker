# Docker Compose 环境变量配置文件
# 使用方法：docker-compose --env-file .env.docker up -d

# ===========================================
# 基础配置
# ===========================================

# 服务器IP地址（请根据实际情况修改）
SERVER_IP=*************

# 数据库配置
POSTGRES_DB=fls
POSTGRES_USER=fls
POSTGRES_PASSWORD=FW5255WA125p5Sa4pHbS6jw

# Redis配置
REDIS_PASSWORD=redis_e2SZWp6a8

# ===========================================
# Django后端配置
# ===========================================

# Django环境
DJANGO_ENV=product
DEBUG=False
SECRET_KEY=p8e7q4w9o2i5u6y3t0r1z8x7c4v5b6n9m2a3s4d5f6g7h8j9k0l

# 管理员账户
ADMIN_USERNAME=admin
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=Admin@123456

# 文件存储
FILE_STORAGE_BACKEND=local

# RPyC服务
RPYC_HOST=localhost
RPYC_PORT=18861

# ===========================================
# 前端配置
# ===========================================

# Nuxt.js配置
NODE_ENV=production
NUXT_SSR=false

# ===========================================
# pgAdmin配置
# ===========================================

PGADMIN_DEFAULT_EMAIL=<EMAIL>
PGADMIN_DEFAULT_PASSWORD=admin123

# ===========================================
# 可选配置（需要时取消注释）
# ===========================================

# 阿里云OSS配置
# ALIYUN_OSS_ACCESS_KEY_ID=your_access_key_id
# ALIYUN_OSS_ACCESS_KEY_SECRET=your_access_key_secret
# ALIYUN_OSS_BUCKET_NAME=your_bucket_name
# ALIYUN_OSS_ENDPOINT=https://oss-cn-qingdao.aliyuncs.com

# 阿里云短信配置
# ALIYUN_SMS_ACCESS_KEY_ID=your_sms_access_key_id
# ALIYUN_SMS_ACCESS_KEY_SECRET=your_sms_access_key_secret
# ALIYUN_SMS_SIGN_NAME=your_sign_name
# ALIYUN_SMS_TEMPLATE_CODE=your_template_code
# ALIYUN_SMS_REGION_ID=cn-hangzhou

# AI相关配置
# AI_AUTH_TYPE=jwt
# AI_ACCESS_TOKEN=your_access_token
# AI_BOT_ID=your_bot_id
# AI_ENDPOINT=https://dashscope.aliyuncs.com/compatible-mode
# AI_MODEL=qwen-max
# DASHSCOPE_API_KEY=your_api_key
