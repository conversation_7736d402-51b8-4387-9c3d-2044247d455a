from django.core.management.base import BaseCommand
from django_q.tasks import schedule
from django_q.models import Schedule


class Command(BaseCommand):
    help = '设置演练相关的定时任务'

    def handle(self, *args, **options):
        # 删除已存在的同步状态任务
        Schedule.objects.filter(name='sync_exercise_status').delete()
        
        # 创建每5分钟执行一次的状态同步任务
        task = schedule(
            'apps.exercise.tasks.sync_exercise_status',
            schedule_type=Schedule.MINUTES,
            minutes=5,  # 每5分钟执行一次
            name='sync_exercise_status'
        )
        
        self.stdout.write(
            self.style.SUCCESS(
                f'成功创建演练状态同步定时任务 (ID: {task.id})'
            )
        )
        
        self.stdout.write(
            self.style.SUCCESS(
                '定时任务设置完成！任务将每5分钟检查一次演练状态并自动更新已过期的演练。'
            )
        )
