<template>
  <div
    class="group relative bg-gradient-to-br border rounded-xl overflow-hidden hover:shadow-[0_8px_30px_rgb(0,0,0,0.12)] transition-all duration-300"
    :class="[
      record.infection_count > 0
        ? 'from-red-50/80 to-white dark:from-red-950/30 dark:to-gray-900 border-red-200/50 dark:border-red-800/30 hover:from-red-100/90 hover:to-white dark:hover:from-red-900/40 dark:hover:to-gray-900 hover:border-red-300/50 dark:hover:border-red-700/40'
        : 'from-gray-50 to-white dark:from-gray-800 dark:to-gray-900 border-gray-200 dark:border-gray-700 hover:from-blue-50 hover:to-white dark:hover:from-gray-800 dark:hover:to-gray-900 hover:border-blue-500/30'
    ]">
    <!-- 卡片装饰元素 -->
    <div
      class="absolute inset-0 bg-gradient-to-br to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"
      :class="[
        record.infection_count > 0
          ? 'from-red-100/40 dark:from-red-900/30'
          : 'from-blue-100/40 dark:from-blue-900/30'
      ]">
    </div>
    <div
      class="absolute top-3 right-3 text-blue-500/20 dark:text-blue-400/20 transform rotate-0 group-hover:rotate-12 transition-transform duration-300">
      <!-- windows 图标 -->
      <Icon v-if="isWindowsSystem(record.system_version)" name="simple-icons:windows" class="w-8 h-8" />
      <!-- 服务器 -->
      <Icon v-else name="heroicons:server-stack" class="w-8 h-8" />
    </div>

    <!-- 卡片内容 -->
    <div class="relative p-4">
      <!-- 主机信息区域 -->
      <div class="mb-4">
        <div class="flex items-center space-x-1.5 mb-1.5">
          <svg class="w-4 h-4 flex-shrink-0 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
              d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
          <h3 class="text-sm font-semibold text-gray-900 dark:text-white truncate">
            {{ record.hostname }}
          </h3>
        </div>
        <div class="flex items-center justify-between">
          <div class="flex items-center space-x-2 min-w-0 flex-1">
            <!-- 在线状态 -->
            <div class="flex items-center space-x-1.5 flex-shrink-0">
              <div :class="[
                'w-2 h-2 rounded-full',
                isDeviceOnline ? 'bg-green-500' : 'bg-red-500'
              ]"></div>
              <span :class="[
                'text-xs',
                isDeviceOnline ? 'text-green-500' : 'text-red-500'
              ]">{{ isDeviceOnline ? $t('all.online') : $t('all.offline') }}</span>
            </div>
            <!-- 设备ID -->
            <span class="text-sm text-gray-500 dark:text-gray-400 truncate min-w-0 flex-1"
              :title="`设备ID：${record.device_id}`">
              {{ record.device_id }}
            </span>
            <button v-if="record.infection_count > 0" @click.stop="handleCopy" class="flex-shrink-0 p-0.5 text-gray-400 hover:text-blue-500 dark:text-gray-500 dark:hover:text-blue-400
                     rounded transition-colors duration-200" title="复制设备ID">
              <svg class="w-3.5 h-3.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                  d="M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-1M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3" />
              </svg>
            </button>
          </div>
        </div>
      </div>

      <!-- 感染统计区域 -->
      <div class="mb-3 bg-gray-100/60 dark:bg-gray-800/60 rounded-lg px-3 py-2">
        <div class="flex items-center justify-between">
          <div>
            <div class="flex items-center space-x-2">
              <svg class="w-3.5 h-3.5 flex-shrink-0" :class="[
                record.infection_count > 0 ? 'text-red-500/70' : 'text-green-500/70'
              ]" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                  d="M16 8v8m-4-5v5M8 8v8m-5 0h18" />
              </svg>
              <div class="text-xs font-medium tracking-wide" :class="[
                record.infection_count > 0
                  ? 'text-red-600/90 dark:text-red-400/90'
                  : 'text-green-600/90 dark:text-green-400/90'
              ]">
                {{ record.infection_count > 0 ? $t('infection.infected') : $t('infection.not_infected') }}
              </div>
            </div>
            <div class="text-[10px] text-gray-500/80 dark:text-gray-400/80 mt-0.5 tracking-wide">
              {{ $t('infection.infection_status') }}
            </div>
          </div>
          <button @click.stop="handleView" :disabled="!(record.infection_count > 0)" :class="['inline-flex items-center space-x-1 px-2 py-1 text-xs font-medium rounded transition-colors duration-200',
            record.infection_count > 0 ? 'text-blue-600/90 dark:text-blue-400/90 hover:bg-blue-50 dark:hover:bg-blue-900/30' : 'cursor-not-allowed text-gray-400/90 dark:text-gray-500/90'
          ]">
            <span> {{ $t('table.details') }}</span>
            <svg class="w-3 h-3" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </div>
      </div>

      <!-- 详细信息网格 -->
      <div class="grid grid-cols-1 gap-2 text-xs">
        <!-- 系统版本信息 -->
        <div class="p-2 bg-gray-100/60 dark:bg-gray-800/60 rounded-lg">
          <div class="flex items-center space-x-1.5 mb-1">
            <svg class="w-3 h-3 flex-shrink-0 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                d="M9.75 13.5l3-3m0 0l3 3m-3-3v12m6.75-10.5h-12A2.25 2.25 0 004.5 7.5v12a2.25 2.25 0 002.25 2.25h12a2.25 2.25 0 002.25-2.25v-12A2.25 2.25 0 0018.75 4.5h-12z" />
            </svg>
            <span class="text-gray-500 dark:text-gray-400">{{ $t('infection.system_version') }}</span>
          </div>
          <div class="text-gray-700 dark:text-gray-200 font-medium truncate pl-4 h-4" :title="record.system_version || '-'">
            {{ record.system_version || '-' }}
          </div>
        </div>

        <!-- 用户信息 -->
        <div class="p-2 bg-gray-100/60 dark:bg-gray-800/60 rounded-lg">
          <div class="flex items-center space-x-1.5 mb-1">
            <svg class="w-3 h-3 flex-shrink-0 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
            <span class="text-gray-500 dark:text-gray-400">{{ $t('table.username') }}</span>
          </div>
          <div class="text-gray-700 dark:text-gray-200 font-medium truncate pl-4 h-4" :title="record.username || '-'">
            {{ record.username || '-' }}
          </div>
        </div>

        <!-- 网络信息组 -->
        <div class="grid grid-cols-2 gap-2">
          <!-- IP地址 -->
          <div class="p-2 bg-gray-100/60 dark:bg-gray-800/60 rounded-lg">
            <div class="flex items-center space-x-1.5 mb-1">
              <svg class="w-3 h-3 flex-shrink-0 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                  d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
              </svg>
              <span class="text-gray-500 dark:text-gray-400">{{ $t('table.ip_address') }}</span>
            </div>
            <div class="text-gray-700 dark:text-gray-200 font-medium truncate pl-4 h-4" :title="record.ip || '-'">
              {{ record.ip || '-' }}
            </div>
          </div>
          <!-- 位置信息 -->
          <div class="p-2 bg-gray-100/60 dark:bg-gray-800/60 rounded-lg">
            <div class="flex items-center space-x-1.5 mb-1">
              <svg class="w-3 h-3 flex-shrink-0 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                  d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                  d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              <span class="text-gray-500 dark:text-gray-400">{{ $t('infection.location') }}</span>
            </div>
            <div class="text-gray-700 dark:text-gray-200 font-medium truncate pl-4 h-4" :title="record.location || '-'">
              {{ record.location || '-' }}
            </div>
          </div>
        </div>

        <!-- 时间信息组 -->
        <div class="grid grid-cols-2 gap-2">
          <!-- 首次感染时间 -->
          <div class="p-2 bg-gray-100/60 dark:bg-gray-800/60 rounded-lg">
            <div class="flex items-center space-x-1.5 mb-1">
              <svg class="w-3 h-3 flex-shrink-0 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              <span class="text-gray-500 dark:text-gray-400">{{ $t('infection.first_infection') }}</span>
            </div>
            <div class="text-gray-700 dark:text-gray-200 font-medium truncate pl-4 h-4"
              :title="record.infection_count > 0 ? formatTime(record.first_seen) : '-'">
              {{ record.infection_count > 0 ? formatTime(record.first_seen) : '-' }}
            </div>
          </div>
          <!-- 最后感染时间 -->
          <div class="p-2 bg-gray-100/60 dark:bg-gray-800/60 rounded-lg">
            <div class="flex items-center space-x-1.5 mb-1">
              <svg class="w-3 h-3 flex-shrink-0 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span class="text-gray-500 dark:text-gray-400">{{ $t('infection.last_infection') }}</span>
            </div>
            <div class="text-gray-700 dark:text-gray-200 font-medium truncate pl-4 h-4"
              :title="record.infection_count > 0 ? formatTime(record.last_seen) : '-'">
              {{ record.infection_count > 0 ? formatTime(record.last_seen) : '-' }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { formatTime } from '~/utils/format'

const props = defineProps({
  record: {
    type: Object,
    required: true
  },
  isDeviceOnline: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['view', 'copy'])

// 判断是否为Windows系统
const isWindowsSystem = (systemVersion) => {
  return systemVersion?.toLowerCase().includes('windows')
}

// 处理复制事件
const handleCopy = () => {
  emit('copy', props.record.device_id)
}

// 处理查看详情事件
const handleView = () => {
  emit('view', props.record)
}
</script>

<style scoped>
/* 暗色模式下的渐变文本支持 */
.dark .bg-gradient-to-r {
  background-clip: text;
  -webkit-background-clip: text;
}
</style>