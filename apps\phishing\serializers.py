from rest_framework import serializers
from . import models
from drf_writable_nested import WritableNestedModelSerializer
from extensions import file_utils


class MailHeaderSerializer(serializers.ModelSerializer):
    """
    自定义邮件头序列化器
    """

    class Meta:
        model = models.MailHeaderModel
        fields = ["id", "x_custom_header", "gophish"]


class StrategySerializer(WritableNestedModelSerializer):
    """
    发送邮件策略序列化器
    """
    mail_headers = MailHeaderSerializer(label="邮件头配置", many=True, required=True)

    class Meta:
        model = models.StrategyModel
        fields = ["id", "name", "api_type", "sender_email", "email_server_address", "email_server_account",
                  "email_server_pwd", "is_ignore_certificate_errors", "mail_headers", "created_at", "port",
                  "status"]

    def create(self, validated_data):
        user = self.context["request"].user
        validated_data["created_by"] = user
        instance = super().create(validated_data)
        return instance


class EmailTemplateFileSerializer(serializers.ModelSerializer):
    """
    邮件模板附件序列化器
    """
    oss_url = file_utils.NoValidationFileField(source="email_file", required=False, allow_null=True, read_only=True)
    email_file = serializers.CharField(required=False, allow_null=True)

    class Meta:
        model = models.EmailTemplateFileModel
        fields = ["id", "virus", "email_file", "zip_name", "zip_password", "file_name", "file_size", "oss_url"]


class EmailTemplateFileListSerializer(serializers.ModelSerializer):
    """
    邮件模板附件列表序列化器（简化版）
    """
    class Meta:
        model = models.EmailTemplateFileModel
        fields = ["id", "file_name"]


class EmailTemplateListSerializer(serializers.ModelSerializer):
    """
    邮件模板列表序列化器
    """
    email_template_file = EmailTemplateFileListSerializer(many=True, read_only=True)

    class Meta:
        model = models.EmailTemplateModel
        fields = ["id", "name", "template_type", "email_subject", "created_at", "email_template_file"]


class EmailTemplateDetailSerializer(WritableNestedModelSerializer):
    """
    邮件模板详情序列化器
    """
    email_template_file = EmailTemplateFileSerializer(label="邮件模板附件", many=True, required=False)

    class Meta:
        model = models.EmailTemplateModel
        fields = ["id", "name", "template_type", "email_subject", "content", "email_template_file",
                  "is_track_user", "is_redirect_url",
                  "created_at", "email_template_file"]

    def create(self, validated_data):
        user = self.context["request"].user
        validated_data["created_by"] = user
        instance = super().create(validated_data)
        return instance


class PhishingPageSerializer(serializers.ModelSerializer):
    """
    钓鱼页面序列化器
    """

    class Meta:
        model = models.PhishingPageModel
        fields = ["id", "name", "template_type", "redirect_address", "request_url", "content",
                  "is_capture_submitted_data",
                  "is_capture_password", "created_at"]

    def create(self, validated_data):
        user = self.context["request"].user
        validated_data["created_by"] = user
        instance = super().create(validated_data)
        return instance


class EmailTaskSerializer(serializers.ModelSerializer):
    """
    邮件任务序列化器
    """
    email_template_name = serializers.CharField(source="email_template.name", label="邮件模板名称",
                                                read_only=True, default="")

    phishing_page_name = serializers.CharField(source="phishing_page.name", label="钓鱼页面名称",
                                               read_only=True, default="")

    strategy_name = serializers.CharField(source="strategy.name", label="发送策略名称",
                                          read_only=True, default="")

    class Meta:
        model = models.EmailTakModel
        fields = ["id", "name", "email_template", "phishing_page", "phishing_link", "strategy", "created_at",
                  "email_template_name", "phishing_page_name", "strategy_name"]

    def create(self, validated_data):
        user = self.context["request"].user
        validated_data["created_by"] = user
        instance = super().create(validated_data)
        return instance


class EmailLogSerializer(serializers.ModelSerializer):
    """
    邮件任务序列化器
    """

    class Meta:
        model = models.EmailLog
        fields = ["id", ]
