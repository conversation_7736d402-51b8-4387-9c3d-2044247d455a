from django.contrib import admin
from .models import MailHeaderModel, StrategyModel, EmailTemplateModel, EmailTemplateFileModel, PhishingPageModel, EmailTakModel, EmailLog


@admin.register(MailHeaderModel)
class MailHeaderAdmin(admin.ModelAdmin):
    list_display = ['x_custom_header', 'gophish', 'created_at']
    search_fields = ['x_custom_header', 'gophish']
    list_filter = ['created_at']


@admin.register(StrategyModel)
class StrategyAdmin(admin.ModelAdmin):
    list_display = ['name', 'api_type', 'sender_email', 'email_server_address', 'status', 'created_at']
    list_filter = ['api_type', 'status', 'created_at']
    search_fields = ['name', 'sender_email', 'email_server_address']
    filter_horizontal = ['mail_headers']


@admin.register(EmailTemplateModel)
class EmailTemplateAdmin(admin.ModelAdmin):
    list_display = ['name', 'email_subject', 'template_type', 'is_track_user', 'is_redirect_url', 'created_at']
    list_filter = ['name', 'is_track_user', 'is_redirect_url', 'created_at']
    search_fields = ['name', 'email_subject', 'content']
    filter_horizontal = ['email_template_file']


@admin.register(EmailTemplateFileModel)
class EmailTemplateFileAdmin(admin.ModelAdmin):
    list_display = ['virus', 'file_name', 'file_size', 'is_compress_attachments', 'created_at']
    list_filter = ['is_compress_attachments', 'created_at']
    search_fields = ['file_name', 'virus__name']


@admin.register(PhishingPageModel)
class PhishingPageAdmin(admin.ModelAdmin):
    list_display = ['name', 'redirect_address', 'template_type', 'request_url', 'is_capture_submitted_data', 'is_capture_password', 'created_at']
    list_filter = ['is_capture_submitted_data', 'is_capture_password', 'created_at']
    search_fields = ['name', 'redirect_address', 'request_url']


@admin.register(EmailTakModel)
class EmailTakAdmin(admin.ModelAdmin):
    list_display = ['name', 'email_template', 'phishing_page', 'strategy', 'created_at']
    list_filter = ['created_at']
    search_fields = ['name', 'email_template__name', 'phishing_page__name', 'strategy__name']


@admin.register(EmailLog)
class EmailLogAdmin(admin.ModelAdmin):
    list_display = ['email_task', 'recipient', 'status', 'retry_count', 'sent_at', 'created_at']
    list_filter = ['status', 'retry_count', 'sent_at', 'created_at']
    search_fields = ['email_task__name', 'recipient', 'error_message']
    readonly_fields = ['created_at', 'updated_at', 'retry_count', 'sent_at', 'task_id']
    ordering = ['-created_at']

    def has_add_permission(self, request):
        """禁止手动添加邮件发送记录"""
        return False

    def has_change_permission(self, request, obj=None):
        """禁止修改邮件发送记录"""
        return False
