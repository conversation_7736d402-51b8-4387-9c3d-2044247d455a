// 导入 useApi 函数
import { useApi } from '@/composables/useApi'

// 创建 api 实例
const api = useApi()

// 导出感染 API 接口
export const infectionApi = {
    // 获取感染统计信息
    getStatistics() {
        return api.get('/infections/statistics/')
    },

    // 获取设备列表
    getDevices(params) {
        return api.get('/devices/', { params })
    },

    //获取设备的感染历史记录
    getDeviceInfections(params) {
        return api.get(`/records/`, { params })
    },

    // 获取感染记录列表
    getInfections() {
        return api.get('/infections/')
    },

    // 创建感染记录
    createInfection(data) {
        return api.post('/infections/', data)
    },

    // 获取感染趋势数据
    getInfectionTrend(timeRange = '24h') {
        return api.get(`/infections/trends/?time_range=${timeRange}`)
    },

    //执行命令
    executeCommand(data) {
        return api.post('/device-commands/', data)
    },



    // 查询设备执行命令历史纪录
    getDeviceCommands(params) {
        return api.get(`/device-commands/`, { params })
    }
    ,
    // 查询设备执行命令历史纪录
    getDeviceCommandDetail(id) {
        return api.get(`/device-commands/${id}`)
    }
}

