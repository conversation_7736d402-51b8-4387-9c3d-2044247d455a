from django.db.models import Q
from django.shortcuts import get_object_or_404
from rest_framework import serializers

from .models import Infect<PERSON><PERSON><PERSON>ord, Device, DeviceCommand
from ..assets.models import Asset, AssetGroup
from ..exercise.models import Exercise
from ..system.models import NotifyModel


class DeviceSerializer(serializers.ModelSerializer):
    """设备信息序列化器"""
    hostname = serializers.SerializerMethodField()
    username = serializers.SerializerMethodField()
    ip = serializers.SerializerMethodField()
    location = serializers.SerializerMethodField()
    system_version = serializers.SerializerMethodField()

    class Meta:
        model = Device
        fields = ('device_id', 'first_seen', 'last_seen', 'infection_count',
                 'hostname', 'username', 'ip', 'location', 'system_version')
        read_only_fields = ('first_seen', 'last_seen', 'infection_count')

    def get_latest_infection_record(self, obj):
        """获取最新的感染记录"""
        return obj.infection_records.order_by('-system_time').first()

    def get_hostname(self, obj):
        """获取主机名"""
        record = self.get_latest_infection_record(obj)
        return record.hostname if record else obj.hostname

    def get_username(self, obj):
        """获取用户名"""
        record = self.get_latest_infection_record(obj)
        return record.username if record else None

    def get_ip(self, obj):
        """获取IP地址"""
        record = self.get_latest_infection_record(obj)
        return record.ip_address if record else None

    def get_location(self, obj):
        """获取位置信息"""
        record = self.get_latest_infection_record(obj)
        return record.location if record else None

    def get_system_version(self, obj):
        """获取系统版本"""
        record = obj.infection_records.order_by('-system_time').first()
        return record.system_version if record else None


class InfectionRecordSerializer(serializers.ModelSerializer):
    """感染记录序列化器"""
    device = DeviceSerializer(read_only=True)
    id = serializers.CharField(write_only=True, required=True)  # 用于接收设备ID
    ip = serializers.IPAddressField(write_only=True, required=True)  # 用于接收IP地址
    mac = serializers.CharField(write_only=True, required=True)  # 用于接收MAC地址

    class Meta:
        model = InfectionRecord
        fields = (
            'id', 'device', 'hostname', 'username', 
            'exec_path', 'system_time', 'ip', 'ip_address', 
            'location', 'system_version', 'mac'
        )
        read_only_fields = ('device', 'ip_address')

    def create(self, validated_data):
        """创建感染记录"""
        print(f"=============", validated_data)
        # 从validated_data中提取device_id和ip
        device_id = validated_data.pop('id')
        ip = validated_data.pop('ip')
        # 从资产下取出数据进行比较
        hostname = validated_data.get('hostname')
        username = validated_data.get('username')
        system_version = validated_data.get('system_version')
        mac = validated_data.get('mac')
        # ip_address = validated_data.get('ip_address')

        # 将ip赋值给ip_address
        validated_data['ip_address'] = ip
        validated_data['mac'] = mac
        # 演练下的全部资产组
        matching_exercise_ids = Exercise.objects.filter(
            Q(target_groups__asset__name=hostname) |  # 匹配资产名称
            Q(target_groups__asset__mac_address=mac),  # 匹配资产名称
            # Q(target_groups__asset__ip_address_v4=ip)  # 匹配资产 IP
        ).filter(status=Exercise.Status.RUNNING).distinct().values_list('id', flat=True)
        print(f"=============", validated_data, matching_exercise_ids)
        for exercise_id in matching_exercise_ids:
            # 获取或创建Device
            devices = Device.objects.filter(exercise_id=exercise_id)
            devices = devices.filter(Q(hostname=hostname) | Q(mac_address=mac))
            print("=============", devices.count())
            for device_obj in devices:
                print(device_id)
                # 更新设备信息
                device_obj.infection_count += 1
                device_obj.device_id = device_id
                device_obj.save()
                infection_record = InfectionRecord.objects.create(
                    exercise_id=exercise_id,
                    status='IN',
                    device=device_obj,
                    exec_path=validated_data.get("exec_path"),
                    hostname=hostname,
                    username=username,
                    ip_address=ip,
                    system_version=system_version,
                )
                exercise = get_object_or_404(Exercise, id=exercise_id)
                NotifyModel.objects.create(
                    exercise_id=exercise_id,
                    user=exercise.created_by,
                    content=f"{exercise.name}演练下的{validated_data['username']}的设备被感染"
                )

            # # 创建感染记录
            # validated_data['device'] = device
            # super().create(validated_data)
        return validated_data


class DeviceCommandSerializer(serializers.ModelSerializer):
    """设备函数调用序列化器"""
    device = serializers.CharField(required=False)  # 接收设备ID字符串
    exercise_id = serializers.CharField(required=False)  # 接收演练ID字符串

    class Meta:
        model = DeviceCommand
        fields = '__all__'
        read_only_fields = ['status', 'response']

    def validate(self, attrs):
        """验证函数调用参数"""
        command = attrs.get('command')
        args = attrs.get('args', {})
        device_id = attrs.get('device')
        exercise_id = attrs.get('exercise_id')
        print('device_id', device_id)
        print('exercise_id', exercise_id)

        # 验证all命令不需要device
        if command == 'all':
            if device_id:
                raise serializers.ValidationError("获取在线设备命令不需要指定设备")
            attrs['device'] = None
        # 其他函数需要指定device
        else:
            if not device_id:
                raise serializers.ValidationError("需要指定操作设备")

            # 根据device_id查找Device对象
            device = Device.objects.filter(device_id=device_id).last()
            if not device:
                # 如果设备不存在，创建一个临时设备记录用于测试
                # 先检查是否有可用的演练
                from ..exercise.models import Exercise
                exercise = Exercise.objects.first()
                if exercise:
                    exercise_id = exercise.pk
                else:
                    # 如果没有演练，创建一个默认演练
                    exercise = Exercise.objects.create(
                        name="默认测试演练",
                        description="用于测试的默认演练",
                        status=Exercise.Status.RUNNING
                    )
                    exercise_id = exercise.pk

                device = Device.objects.create(
                    device_id=device_id,
                    hostname=device_id,
                    exercise_id=exercise_id
                )
            attrs['device'] = device

            # 根据函数类型验证特定参数（可选）
            # 这里可以添加特定函数的参数验证逻辑
            # 例如：
            # if command == 'change_wallpaper' and 'image_path' not in args:
            #     raise serializers.ValidationError("更改壁纸函数需要image_path参数")

        return attrs


class BroadcastCommandSerializer(serializers.Serializer):
    """广播命令序列化器"""
    command = serializers.CharField(
        required=True,
        help_text="要广播执行的函数名称",
        style={'placeholder': 'show_ransom_note'}
    )
    args = serializers.JSONField(
        required=False,
        default=dict,
        help_text="函数执行参数，JSON格式",
        style={'placeholder': '{"note_text": "演练警告", "contact_info": "<EMAIL>"}'}
    )

    def validate_command(self, value):
        """验证命令名称"""
        # 定义允许的广播命令列表
        allowed_commands = [
            'show_ransom_note',      # 显示勒索信息
            'change_wallpaper',      # 更改壁纸
            'restore_wallpaper',     # 恢复壁纸
            'encrypt_files',         # 加密文件
            'decrypt_files',         # 解密文件
            'collect_system_info',   # 收集系统信息
            'kill_antivirus',        # 关闭杀毒软件
            'maintain_persistence',  # 权限维持
            'destroy_virus',         # 销毁病毒程序
        ]

        if value not in allowed_commands:
            raise serializers.ValidationError(
                f"不支持的命令: {value}。支持的命令: {', '.join(allowed_commands)}"
            )

        return value

    def validate_args(self, value):
        """验证参数格式"""
        if not isinstance(value, dict):
            raise serializers.ValidationError("参数必须是字典格式")
        return value


class BroadcastResponseSerializer(serializers.Serializer):
    """广播响应序列化器"""
    status = serializers.CharField(help_text="执行状态: success/error")
    message = serializers.CharField(help_text="执行消息")
    results = serializers.JSONField(help_text="各设备执行结果详情")
