<template>
  <button type="button"
    class="relative p-2.5 bg-gradient-to-br from-amber-100 to-amber-50 hover:from-amber-200 hover:to-amber-100 rounded-xl dark:from-amber-500/20 dark:to-amber-600/20 dark:hover:from-amber-500/30 dark:hover:to-amber-600/30 transition-all duration-200 group"
    @click="$emit('click')">
    <Icon name="heroicons:bell" class="w-5 h-5 text-amber-600 dark:text-amber-400" />
    <div v-if="count > 0"
      class="absolute inline-flex items-center justify-center w-5 h-5 text-xs font-medium text-white bg-gradient-to-r from-amber-500 to-orange-500 rounded-full -top-1.5 -right-1.5 transform scale-100 group-hover:scale-110 transition-transform duration-200">
      {{ count }}
    </div>
  </button>
</template>

<script setup>
defineProps({
  count: {
    type: Number,
    default: 0
  }
})

defineEmits(['click'])
</script> 