<template>
  <div class="p-4 bg-white border border-gray-200 rounded-lg shadow-sm">
    <div class="flex items-center">
      <!-- 图标 -->
      <div 
        class="inline-flex flex-shrink-0 justify-center items-center w-8 h-8 rounded-lg"
        :class="[`text-${color}-600 bg-${color}-100`]"
      >
        <slot name="icon">
          <Icon name="heroicons:clock" class="w-4 h-4" />
        </slot>
      </div>
      <!-- 内容 -->
      <div class="flex-1 min-w-0 ms-4">
        <p class="text-sm font-medium text-gray-500 truncate">
          {{ title }}
        </p>
        <p class="text-2xl font-semibold text-gray-900">
          <slot>{{ value }}</slot>
        </p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  title: string
  value?: string | number
  color?: 'blue' | 'red' | 'green' | 'yellow' | 'purple'
}

withDefaults(defineProps<Props>(), {
  color: 'blue'
})
</script> 