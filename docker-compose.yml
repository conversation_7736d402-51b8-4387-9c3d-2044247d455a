services:
  web:
    build: .
    restart: unless-stopped
    environment:
      - DJANGO_ENV=product
      # 默认管理员账户配置
      - ADMIN_USERNAME=admin
      - ADMIN_EMAIL=<EMAIL>
      - ADMIN_PASSWORD=Admin@123456
    volumes:
      - ./media:/app/media
      - ./static:/app/static
      - ./logs:/app/logs
    ports:
      - "8001:8001"
      - "9002:9002"
      - "18861:18861"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/api/schema/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

