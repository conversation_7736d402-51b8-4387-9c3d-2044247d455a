version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:17.5
    container_name: fls-postgres
    environment:
      POSTGRES_DB: fls
      POSTGRES_USER: fls
      POSTGRES_PASSWORD: FW5255WA125p5Sa4pHbS6jw
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - fls-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U fls -d fls"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:8-alpine
    container_name: fls-redis
    command: redis-server --requirepass redis_e2SZWp6a8 --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - fls-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # 后端服务
  backend:
    image: sierting-docker.pkg.coding.net/ls/docker/virus-django-image:master
    container_name: fls-backend
    environment:
      - DJANGO_ENV=product
      # 默认管理员账户配置
      - ADMIN_USERNAME=admin
      - ADMIN_EMAIL=<EMAIL>
      - ADMIN_PASSWORD=Admin@123456
      # 允许的域名
      - ALLOWED_HOSTS=localhost,127.0.0.1,backend,*************
      - CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://*************:3000
      - DB_NAME=fls
      - DB_USER=fls
      - DB_PASSWORD=FW5255WA125p5Sa4pHbS6jw
      - DB_HOST=postgres
      - DB_PORT=5432
      - REDIS_URL=redis://:redis_e2SZWp6a8@redis:6379/0
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis_e2SZWp6a8
      - RESERVE_EMAIL=http://*************:8000/api/v1/exercises/reserve_email/
      - CSRF_TRUSTED_ORIGINS=http://localhost:8000,http://127.0.0.1:8000,http://*************:8000,http://localhost:3000,http://127.0.0.1:3000,http://*************:3000
      - DEBUG=True
      - FILE_STORAGE_BACKEND=local
      - PHISHING_LINK=http://*************:8000/api/v1/form
      # RPyC服务配置
      - RPYC_HOST=localhost
      - RPYC_PORT=18861
    volumes:
      - ./media:/app/media
      - ./static:/app/static
      - ./logs:/app/logs
    ports:
      - "8000:8001"
      - "9002:9002"
      - "18861:18861"
    networks:
      - fls-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://*************:8000/"]
      interval: 30s
      timeout: 10s
      retries: 3


  # 前端服务
  frontend:
    image: sierting-docker.pkg.coding.net/ls/docker/virus-nuxt-image:master
    container_name: fls-frontend
    environment:
      - NUXT_PUBLIC_API_BASE=http://*************:8000/api/v1
    ports:
      - "3000:3000"
    networks:
      - fls-network
    depends_on:
      - backend
    restart: unless-stopped

  # PostgreSQL 管理工具 pgAdmin
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: fls-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - fls-network
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped

# 数据卷
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

# 网络
networks:
  fls-network:
    driver: bridge