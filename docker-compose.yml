version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:17.5
    container_name: fls-postgres
    environment:
      POSTGRES_DB: fls
      POSTGRES_USER: fls
      POSTGRES_PASSWORD: FW5255WA125p5Sa4pHbS6jw
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-scripts:/docker-entrypoint-initdb.d
    networks:
      - fls-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U fls -d fls"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis 缓存
  redis:
    image: redis:8-alpine
    container_name: fls-redis
    command: redis-server --requirepass redis_e2SZWp6a8 --appendonly yes
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - fls-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5

  # 后端服务
  backend:
    image: sierting-docker.pkg.coding.net/ls/docker/virus-django-image:master
    container_name: fls-backend
    environment:
      # Django环境配置
      - DJANGO_ENV=product
      - DEBUG=False
      - SECRET_KEY=p8e7q4w9o2i5u6y3t0r1z8x7c4v5b6n9m2a3s4d5f6g7h8j9k0l

      # 默认管理员账户配置
      - ADMIN_USERNAME=admin
      - ADMIN_EMAIL=<EMAIL>
      - ADMIN_PASSWORD=Admin@123456

      # 数据库配置
      - DB_NAME=fls
      - DB_USER=fls
      - DB_PASSWORD=FW5255WA125p5Sa4pHbS6jw
      - DB_HOST=postgres
      - DB_PORT=5432

      # Redis配置
      - REDIS_URL=redis://:redis_e2SZWp6a8@redis:6379/0
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=redis_e2SZWp6a8

      # 网络安全配置
      - ALLOWED_HOSTS=localhost,127.0.0.1,backend,*************,fls-backend
      - CORS_ALLOWED_ORIGINS=http://localhost:3000,http://127.0.0.1:3000,http://*************:3000,http://frontend:3000
      - CSRF_TRUSTED_ORIGINS=http://localhost:8000,http://127.0.0.1:8000,http://*************:8000,http://localhost:3000,http://127.0.0.1:3000,http://*************:3000,http://backend:8001

      # 文件存储配置
      - FILE_STORAGE_BACKEND=local

      # 业务功能配置
      - RESERVE_EMAIL=http://*************:8000/api/v1/exercises/reserve_email/
      - PHISHING_LINK=http://*************:8000/api/v1/form

      # RPyC服务配置
      - RPYC_HOST=localhost
      - RPYC_PORT=18861

      # AI相关配置（可选，如需要AI功能请取消注释并配置）
      # - AI_AUTH_TYPE=jwt
      # - AI_ACCESS_TOKEN=your_access_token
      # - AI_BOT_ID=your_bot_id
      # - AI_ENDPOINT=https://dashscope.aliyuncs.com/compatible-mode
      # - AI_MODEL=qwen-max
      # - DASHSCOPE_API_KEY=your_api_key

      # 阿里云OSS配置（如需要OSS存储请取消注释并配置）
      # - ALIYUN_OSS_ACCESS_KEY_ID=your_access_key_id
      # - ALIYUN_OSS_ACCESS_KEY_SECRET=your_access_key_secret
      # - ALIYUN_OSS_BUCKET_NAME=your_bucket_name
      # - ALIYUN_OSS_ENDPOINT=https://oss-cn-qingdao.aliyuncs.com

      # 阿里云短信配置（如需要短信功能请取消注释并配置）
      # - ALIYUN_SMS_ACCESS_KEY_ID=your_sms_access_key_id
      # - ALIYUN_SMS_ACCESS_KEY_SECRET=your_sms_access_key_secret
      # - ALIYUN_SMS_SIGN_NAME=your_sign_name
      # - ALIYUN_SMS_TEMPLATE_CODE=your_template_code
      # - ALIYUN_SMS_REGION_ID=cn-hangzhou
    volumes:
      - ./media:/app/media
      - ./static:/app/static
      - ./logs:/app/logs
    ports:
      - "8000:8000"
      - "9002:9002"
      - "18861:18861"
    networks:
      - fls-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health/"]
      interval: 30s
      timeout: 10s
      retries: 3


  # 前端服务
  frontend:
    image: sierting-docker.pkg.coding.net/ls/docker/virus-nuxt-image:master
    container_name: fls-frontend
    environment:
      # Nuxt.js配置
      - NODE_ENV=production
      - NUXT_SSR=false

      # API配置 - 使用容器内部通信或外部IP
      # - NUXT_PUBLIC_API_BASE=http://*************:8000/api/v1
      # 如果需要使用容器内部通信，可以改为：
      - NUXT_PUBLIC_API_BASE=http://backend:8000/api/v1

      # AI相关配置（可选，如需要AI功能请取消注释并配置）
      # - AI_ENDPOINT=https://dashscope.aliyuncs.com/compatible-mode
      # - AI_MODEL=qwen-max
      # - AI_API_KEY=your_api_key
    ports:
      - "3000:3000"
    networks:
      - fls-network
    depends_on:
      - backend
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3

  # PostgreSQL 管理工具 pgAdmin
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: fls-pgadmin
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    ports:
      - "5050:80"
    volumes:
      - pgadmin_data:/var/lib/pgadmin
    networks:
      - fls-network
    depends_on:
      postgres:
        condition: service_healthy
    restart: unless-stopped

# 数据卷
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  pgadmin_data:
    driver: local

# 网络
networks:
  fls-network:
    driver: bridge