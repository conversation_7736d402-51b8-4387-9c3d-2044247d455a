<template>
  <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <div class="mb-8">
      <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent">
        {{ $t('users.title') }}
      </h1>
      <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
        {{ $t('users.subtitle') }}
      </p>
    </div>

    <div class="flex justify-between items-center mb-6">
      <div></div>
      <div class="space-x-4">
        <button @click="showFormModal = true"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          {{ $t('all.create') }}{{ $t('users.user') }}
        </button>
      </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow mb-6">
      <div class="p-4 border-b border-gray-200 dark:border-gray-700">
        <SearchFilter v-model="filters" :search-value="searchQuery" @update:search-value="val => searchQuery = val"
          :search-placeholder="$t('users.placeholder_search')" @search="handleSearch" :show-search-button="true"
          :search-button-text="$t('all.search')" :selects="[
            {
              key: 'status',
              placeholder: $t('table.status'),
              'options': [
                { value: '1', label: $t('all.enabled') },
                { value: '0', label: $t('all.disabled') }
              ]
            }
          ]" :show-delete-button="true" @delete-all="handleDeleteAll" />
      </div>

      <DataTable :columns="[
        { title: $t('table.avatar'), key: 'avatar', slot: 'avatar', width: 80 },
        { title: $t('table.username'), key: 'username', width: 150 },
        { title: $t('table.email'), key: 'email', width: 200 },
        { title: $t('table.company'), key: 'company', slot: 'company', width: 120 },
        { title: $t('table.create_time'), key: 'date_joined', width: 180 },
        { title: $t('table.status'), key: 'is_active', slot: 'status', width: 100 },
        { title: $t('table.action'), key: 'actions', slot: 'actions', width: 100 }
      ]" :data="users" :loading="loading" :pagination="pagination" @page-change="handlePageChange"
        :row-selection="rowSelection" @select="() => { rowSelection.selectedRowKeys = $event }">
        <template #avatar="{ row }">
          <div class="flex items-center justify-center rounded-full">
            <img :src="row.avatar || `https://api.dicebear.com/6.x/avataaars/svg?seed=${row.username || 'default'}`"
              :alt="row.username" class="w-8 h-8 rounded-full object-cover" />
          </div>
        </template>

        <template #company="{ row }">
          <div class="flex items-center justify-center min-w-[120px]">
            <span class="whitespace-nowrap text-center">{{ row.company || '-' }}</span>
          </div>
        </template>

        <template #status="{ row }">
          <span :class="[
            'inline-flex px-2 py-1 text-xs font-medium rounded-full',
            row.is_active
              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
              : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
          ]">
            {{ row.is_active ? $t('all.enabled') : $t('all.disabled') }}
          </span>
        </template>

        <template #actions="{ row }">
          <div class="flex items-center space-x-2">
            <button @click="handleEdit(row)"
              class="p-1 text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </button>
            <button @click="handleDelete(row)"
              class="p-1 text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
              <Icon name="heroicons:trash" class="w-5 h-5" />
            </button>
          </div>
        </template>
      </DataTable>
    </div>

    <UserFormModal v-if="showFormModal" :user="selectedUser" @close="handleCloseForm" @submit="handleSubmitForm" />

    <ConfirmDialog :show="showDeleteConfirm" :title="$t('all.delete') + $t('users.user')"
      :message="deleteConfirmMessage" type="danger" @confirm="confirmDelete" @cancel="showDeleteConfirm = false" />

    <!-- 删除多个确认弹窗 -->
    <ConfirmDialog :show="showDeleteAllConfirm" :title="`${$t('all.delete')}${$t('assets.asset')}`"
      :message="deleteAllConfirmMessage" type="danger" @confirm="confirmDelete"
      @cancel="showDeleteAllConfirm = false" />
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { useToast } from '~/composables/useToast'
import DataTable from '~/components/common/DataTable.vue'
import SearchFilter from '~/components/common/SearchFilter.vue'
import ConfirmDialog from '~/components/common/ConfirmDialog.vue'
import UserFormModal from '~/components/user/UserFormModal.vue'
import { userApi } from '~/api/users'

const { t } = useI18n()
const toast = useToast()

const users = ref([])
const loading = ref(false)
const showFormModal = ref(false)
const selectedUser = ref(null)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const showDeleteConfirm = ref(false)
const showDeleteAllConfirm = ref(false)
const userToDelete = ref(null)
const rowSelection = ref({
  selectedRowKeys: [],
  onChange: (selectedRowKeys) => {
    rowSelection.value.selectedRowKeys = selectedRowKeys
  }
})

const searchQuery = ref('')

const filters = ref({
  status: '',
  is_active: ''
})

const pagination = computed(() => ({
  currentPage: currentPage.value,
  pageSize: pageSize.value,
  total: total.value
}))

const fetchUsers = async (page = 1) => {
  loading.value = true
  try {
    const params = {
      page,
      page_size: pageSize.value,
      search: searchQuery.value || '',
      is_active: filters.value.status === '' ? '' : filters.value.status === '1',
    }

    Object.keys(params).forEach(key => {
      if (params[key] === '') {
        delete params[key]
      }
    })

    const response = await userApi.getUsers(params)
    users.value = response.results
    total.value = response.count
  } catch (error) {
    toast.error(t('users.message_1'))
    console.error('Failed to fetch users:', error)
  } finally {
    loading.value = false
  }
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchUsers(page)
}

const handleSubmitForm = async (formData) => {
  try {
    if (selectedUser.value) {
      await userApi.updateUser(selectedUser.value.id.toString(), formData)
      toast.success(t('all.update_success'))
    } else {
      await userApi.createUser(formData)
      toast.success(t('all.create_success'))
    }
    showFormModal.value = false
    fetchUsers()
  } catch (error) {
    console.log('error', error)
    toast.error(selectedUser.value ? t('all.update_failed') : t('all.create_failed'))
    console.error('Failed to submit user:', error)
  }
}

const handleCloseForm = () => {
  showFormModal.value = false
  selectedUser.value = null
}

const handleEdit = (user) => {
  selectedUser.value = user
  showFormModal.value = true
}

const handleDelete = (user) => {
  userToDelete.value = user
  showDeleteConfirm.value = true
}

const confirmDelete = async () => {
  if (!userToDelete.value?.id && rowSelection.value.selectedRowKeys.length === 0) return

  const ids = showDeleteAllConfirm.value ? rowSelection.value.selectedRowKeys.join(',') : userToDelete.value.id
  try {
    await userApi.deleteUser(ids)
    toast.success(t('all.delete_success'))
    fetchUsers()
    rowSelection.value.selectedRowKeys = []
  } catch (error) {
    toast.error(t('all.delete_failed'))
    console.error('Failed to delete user:', error)
  } finally {
    showDeleteConfirm.value = false
    showDeleteAllConfirm.value = false
    userToDelete.value = null
  }
}

const deleteConfirmMessage = computed(() => {
  return userToDelete.value ? t('users.delete_message', { username: userToDelete.value.username }) : ''
})

// 批量删除确认消息
const deleteAllConfirmMessage = computed(() => {
  return t('all.delete_all_message')
})

// 确认删除资产
const handleDeleteAll = async () => {
  if (rowSelection.value.selectedRowKeys.length === 0) {
    toast.error(t('all.select_one'))
    return
  }

  showDeleteAllConfirm.value = true
}


const handleSearch = (value) => {
  searchQuery.value = value
  currentPage.value = 1
  fetchUsers()
}

// 监听 filters 变化
watch([filters, searchQuery], () => {
  currentPage.value = 1
  fetchUsers()
}, { deep: true })

onMounted(() => {
  fetchUsers()
})
</script>