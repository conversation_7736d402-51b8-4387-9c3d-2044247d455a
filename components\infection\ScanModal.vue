<template>
  <div v-if="modelValue" class="fixed inset-0 z-50 flex items-center justify-center">
    <!-- 背景遮罩 -->
    <div class="fixed inset-0 bg-black bg-opacity-50 transition-opacity" @click="closeModal"></div>

    <!-- 模态框内容 -->
    <div class="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full mx-4 transform transition-all">
      <!-- 标题栏 -->
      <div class="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white">
          {{ $t('infection.lateral_scan') }}
        </h3>
        <button @click="closeModal" class="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 focus:outline-none">
          <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>

      <!-- 表单内容 -->
      <div class="p-6">
        <!-- 扫描结果显示 -->
        <div v-if="scanResult" class="mb-6">
          <h4 class="text-md font-medium text-gray-900 dark:text-white mb-2">
            {{ $t('infection.scan_result') }}
          </h4>
          <div class="bg-gray-100 dark:bg-gray-700 rounded-md p-3 overflow-auto max-h-60">
            <pre class="text-sm text-gray-800 dark:text-gray-200 whitespace-pre-wrap">{{ scanResult }}</pre>
          </div>
        </div>

        <form v-if="!scanResult" @submit.prevent="submitForm">
          <div class="mb-4">
            <label for="scan_ip" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ $t('infection.target_ip') }}
            </label>
            <input
              type="text"
              id="scan_ip"
              v-model="formData.scan_ip"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm
                     focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                     dark:bg-gray-700 dark:text-white"
              :placeholder="$t('infection.enter_target_ip')"
              required
            />
            <p v-if="validationError" class="mt-1 text-sm text-red-600 dark:text-red-400">
              {{ validationError }}
            </p>
          </div>

          <div class="mb-4">
            <label for="scan_port" class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              {{ $t('infection.target_port') }}
            </label>
            <input
              type="number"
              id="scan_port"
              v-model="formData.port"
              class="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm
                     focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500
                     dark:bg-gray-700 dark:text-white"
              :placeholder="$t('infection.enter_target_port')"
              min="1"
              max="65535"
              required
            />
            <p v-if="portValidationError" class="mt-1 text-sm text-red-600 dark:text-red-400">
              {{ portValidationError }}
            </p>
          </div>

          <div class="flex justify-end space-x-3 mt-6">
            <button
              type="button"
              @click="closeModal"
              class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium
                     text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600
                     focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {{ $t('all.cancel') }}
            </button>
            <button
              type="submit"
              :disabled="loading"
              class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium
                     text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800
                     focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500
                     disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span v-if="loading">{{ $t('all.loading') }}</span>
              <span v-else>{{ $t('infection.start_scan') }}</span>
            </button>
          </div>
        </form>

        <!-- 扫描结果后的按钮 -->
        <div v-if="scanResult" class="flex justify-end space-x-3 mt-6">
          <button
            @click="resetForm"
            class="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium
                   text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600
                   focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            {{ $t('infection.scan_again') }}
          </button>
          <button
            @click="closeModal"
            class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium
                   text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800
                   focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            {{ $t('all.close') }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits } from 'vue'
import { useI18n } from '#imports'
import { infectionApi } from '~/api/infection'

const { t } = useI18n()
const toast = useToast()

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  deviceId: {
    type: String,
    required: true
  },
  exerciseId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update:modelValue', 'scan-complete'])

const formData = ref({
  scan_ip: '',
  port: 22
})
const loading = ref(false)
const validationError = ref('')
const portValidationError = ref('')
const scanResult = ref('')

// 关闭模态框
const closeModal = () => {
  emit('update:modelValue', false)
  // 重置表单
  resetForm()
}

// 重置表单
const resetForm = () => {
  formData.value.scan_ip = ''
  formData.value.port = 22
  validationError.value = ''
  portValidationError.value = ''
  scanResult.value = ''
}

// IP地址验证
const validateIp = (ip) => {
  const ipPattern = /^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
  return ipPattern.test(ip)
}

// 端口验证
const validatePort = (port) => {
  const portNum = parseInt(port)
  return portNum >= 1 && portNum <= 65535
}

// 提交表单
const submitForm = async () => {
  // 验证IP地址
  if (!validateIp(formData.value.scan_ip)) {
    validationError.value = t('infection.invalid_ip_format')
    return
  }

  // 验证端口
  if (!validatePort(formData.value.port)) {
    portValidationError.value = t('infection.invalid_port_format')
    return
  }

  try {
    loading.value = true
    validationError.value = ''
    portValidationError.value = ''

    // 使用统一的device-commands接口
    const response = await infectionApi.executeCommand({
      device: props.deviceId,
      command: 'lateral_scan',
      exercise_id: props.exerciseId,
      args: {
        scan_ip: formData.value.scan_ip,
        port: parseInt(formData.value.port)
      }
    })

    // 处理响应数据
    if (response && response.task_id) {
      // 由于使用了统一接口，这里可能需要轮询获取结果
      // 暂时显示任务已提交的信息
      scanResult.value = t('infection.scan_task_submitted', { taskId: response.task_id })
      emit('scan-complete', response)
    } else {
      toast.error(t('infection.scan_failed'))
    }
  } catch (error) {
    console.error('横向爆破失败:', error)
    toast.error(t('infection.scan_failed'))
  } finally {
    loading.value = false
  }
}
</script>
