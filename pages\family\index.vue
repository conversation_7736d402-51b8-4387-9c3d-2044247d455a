<template>
  <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <div class="mb-8">
      <h1 class="text-3xl font-bold text-blue-600 bg-clip-text">
        {{ $t('family.family') }}
      </h1>
      <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
        {{ $t('family.subtitle') }}
      </p>
    </div>

    <div class="flex justify-between items-center mb-6">
      <div></div>
      <div class="space-x-4">
        <NuxtLink to="/family/form"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          {{ $t('all.create') }}{{ $t('family.family') }}
        </NuxtLink>
      </div>
    </div>

    <div class="bg-white dark:bg-gray-800 rounded-lg shadow mb-6">
      <div class="p-4 border-b border-gray-200 dark:border-gray-700">
        <SearchFilter v-model="filters" :search-value="searchQuery" @update:search-value="val => searchQuery = val"
          :search-placeholder="$t('family.placeholder_search')" @search="handleSearch" :show-search-button="true"
          :show-reset-button="true" :search-button-text="$t('all.search')" :reset-button-text="$t('all.reset')"
          @reset="handleReset" :selects="[]" />
      </div>

      <DataTable :columns="[
        { title: $t('table.family_name'), key: 'name', width: 150 },
        { title: $t('table.family_logo'), key: 'logo', slot: 'logo', width: 100 },
        { title: $t('table.first_appearance_time'), key: 'firstSeenAt', width: 120 },
        { title: $t('table.encryption_algorithm'), key: 'encryptionMethod', width: 120 },
        { title: $t('table.ransom_method'), key: 'ransomMethod', width: 120 },
        { title: $t('table.infection_type'), key: 'infectionType', width: 120 },
        { title: $t('table.wallet_address'), key: 'walletAddress', width: 180 },
        { title: $t('table.public_decryptor'), key: 'publicDecryptor', slot: 'publicDecryptor', width: 100 },
        { title: $t('table.action'), key: 'actions', slot: 'actions', width: 120 }
      ]" :data="families" :loading="loading" :pagination="pagination" @page-change="handlePageChange">
        <template #logo="{ row }">
          <img v-if="row.logo_url" :src="row.logo_url" class="w-8 h-8 rounded-full object-cover" :alt="row.name">
          <div v-else class="w-8 h-8 rounded-full bg-gray-200 flex items-center justify-center">
            <Icon name="heroicons:photo" class="w-4 h-4 text-gray-500" />
          </div>
        </template>

        <template #publicDecryptor="{ row }">
          <span :class="[
            'inline-flex px-2 py-1 text-xs font-medium rounded-full',
            row.publicDecryptor === 'yes'
              ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
              : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300'
          ]">
            {{ row.publicDecryptor === 'yes' ? $t('all.yes') : $t('all.no') }}
          </span>
        </template>

        <template #actions="{ row }">
          <div class="flex items-center space-x-2">
            <button @click="handleEdit(row)"
              class="p-1 text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
              </svg>
            </button>
            <button @click="handleDelete(row)" :disabled="deleteLoading === row.id"
              class="p-1 text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 disabled:opacity-50">
              <svg v-if="deleteLoading !== row.id" class="w-5 h-5" fill="none" stroke="currentColor"
                viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
              <svg v-else class="w-5 h-5 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none"
                viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor"
                  d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                </path>
              </svg>
            </button>
            <button @click="handleView(row)"
              class="p-1 text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </button>
          </div>
        </template>
      </DataTable>
    </div>

    <ConfirmDialog :show="showDeleteConfirm" :title="`${$t('all.delete')}${$t('family.family')}`"
      :message="deleteConfirmMessage" type="danger" @confirm="confirmDelete" @cancel="showDeleteConfirm = false" />
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { useToast } from '~/composables/useToast'
import { debounce } from 'lodash-es'
import SearchFilter from '~/components/common/SearchFilter.vue'
import DataTable from '~/components/common/DataTable.vue'
import ConfirmDialog from '~/components/common/ConfirmDialog.vue'
import { familyApi } from '~/api/family'

const { t } = useI18n()

// 格式化日期的工具函数
const formatDate = (date) => {
  if (!date) return '-'
  const options = {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }
  return new Intl.DateTimeFormat('zh-CN', options).format(new Date(date))
}

const router = useRouter()
const toast = useToast()

// 状态管理
const loading = ref(false)
const families = ref([])
const total = ref(0)

// 分页和搜索相关
const currentPage = ref(1)
const pageSize = ref(10)
const searchQuery = ref('')
const filters = ref({})

// 删除相关的状态
const showDeleteConfirm = ref(false)
const familyToDelete = ref(null)

// 删除loading状态
const deleteLoading = ref(null)

// 计算属性
const pagination = computed(() => ({
  currentPage: currentPage.value,
  pageSize: pageSize.value,
  total: total.value
}))

// 删除确认消息
const deleteConfirmMessage = computed(() => {
  if (!familyToDelete.value) return ''
  return t('family.delete_message', { name: familyToDelete.value.name })
})

// 获取数据
const fetchFamilies = async (page = 1) => {
  loading.value = true
  try {
    const params = {
      page,
      page_size: pageSize.value,
      search: searchQuery.value || '',
    }

    const response = await familyApi.getFamilies(params)
    families.value = response.results.map(family => ({
      id: family.id,
      name: family.name,
      logo_url: family.logo_url,
      firstSeenAt: formatDate(family.first_seen_time),
      encryptionMethod: family.encryption_algorithm,
      ransomMethod: family.ransom_method,
      infectionType: family.infection_type,
      walletAddress: family.wallet_address,
      publicDecryptor: family.public_decrypt ? 'yes' : 'no'
    }))
    total.value = response.count
  } catch (error) {
    toast.error(t('family.message_1'))
    console.error('Failed to fetch families:', error)
  } finally {
    loading.value = false
  }
}

// 处理编辑
const handleEdit = (row) => {
  router.push(`/family/form?id=${row.id}`)
}

// 处理查看
const handleView = (row) => {
  router.push(`/family/${row.id}/detail`)
}

// 处理删除
const handleDelete = (row) => {
  familyToDelete.value = row
  showDeleteConfirm.value = true
}

// 确认删除
const confirmDelete = async () => {
  if (!familyToDelete.value?.id) return

  deleteLoading.value = familyToDelete.value.id
  try {
    await familyApi.deleteFamily(familyToDelete.value.id)
    await fetchFamilies(currentPage.value)
    toast.success(t('all.delete_success'))
  } catch (error) {
    console.error('Failed to delete family:', error)
    toast.error(t('all.delete_failed'))
  } finally {
    showDeleteConfirm.value = false
    familyToDelete.value = null
    deleteLoading.value = null
  }
}

// 处理搜索
const handleSearch = debounce((value) => {
  searchQuery.value = value
  currentPage.value = 1
  fetchFamilies()
}, 300)

// 处理重置
const handleReset = () => {
  searchQuery.value = ''
  filters.value = {}
  currentPage.value = 1
  fetchFamilies()
}

// 处理分页
const handlePageChange = (page) => {
  currentPage.value = page
  fetchFamilies(page)
}

// 监听筛选条件变化
watch([filters], () => {
  currentPage.value = 1
  fetchFamilies()
}, { deep: true })

// 初始化
onMounted(() => {
  fetchFamilies()
})
</script>