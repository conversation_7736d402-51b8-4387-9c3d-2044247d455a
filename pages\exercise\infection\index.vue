<template>
  <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- 页面标题 -->
    <div class="flex justify-between items-center">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-blue-400">
          {{ $t('infection.title') }}
        </h1>
        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
          {{ $t('infection.subtitle') }}
        </p>
      </div>

      <NuxtLink to="/exercise"
        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
          stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        {{ $t('all.back') }}
      </NuxtLink>
    </div>

    <!-- 搜索和内容区域 -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-100 dark:border-gray-700 mb-6">
      <!-- 搜索过滤器 -->
      <div class="p-4 border-b border-gray-100 dark:border-gray-700">
        <div class="flex flex-wrap justify-between items-center">
          <div class="flex-grow">
            <SearchFilter v-model="filters" :search-value="searchQuery" @update:search-value="val => searchQuery = val"
              :search-placeholder="$t('infection.placeholder_search')" @search="handleSearch" :show-search-button="true"
              :show-reset-button="true" :search-button-text="$t('all.search')" :reset-button-text="$t('all.reset')"
              @reset="handleReset" :selects="[]" />
          </div>
          <div class="mt-4 md:mt-0 ml-0 md:ml-4">
            <button @click="handleOfflineAll"
              class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
              <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                  d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728A9 9 0 015.636 5.636m12.728 12.728L5.636 5.636" />
              </svg>
              {{ $t('infection.offline_all') }}
            </button>
          </div>
        </div>
      </div>

      <!-- 卡片列表区域 -->
      <div class="p-4">
        <!-- 加载状态 -->
        <div v-if="loading" class="flex justify-center items-center h-64">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>

        <div v-else>
          <!-- 卡片列表 -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            <div v-for="record in records" :key="record.device_id" class="relative">
              <InfectionCard :record="record" :is-device-online="isDeviceOnline(record)" @view="handleView"
                @copy="copyToClipboard" />
            </div>
          </div>
        </div>

        <!-- 分页区域 -->
        <div class="px-6 py-4 bg-white dark:bg-[#1F2937] dark:border-gray-700">
          <Pagination v-model:currentPage="currentPage" :pageSize="pageSize" :total="total"
            @update:currentPage="handlePageChange" />
        </div>
      </div>
    </div>

    <!-- 下线确认Modal -->
    <div v-if="showOfflineModal" id="offline-modal" tabindex="-1"
         class="fixed inset-0 z-50 flex justify-center items-center w-full h-full bg-gray-900/50 dark:bg-gray-900/80">
      <div class="relative p-4 w-full max-w-md max-h-full">
        <div class="relative bg-white rounded-lg shadow-sm dark:bg-gray-700">
          <button type="button" @click="showOfflineModal = false"
                  class="absolute top-3 end-2.5 text-gray-400 bg-transparent hover:bg-gray-200 hover:text-gray-900 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center dark:hover:bg-gray-600 dark:hover:text-white">
            <Icon name="heroicons:x-mark" class="w-3 h-3" />
            <span class="sr-only">关闭</span>
          </button>
          <div class="p-4 md:p-5 text-center">
            <Icon name="heroicons:exclamation-circle" class="mx-auto mb-4 text-gray-400 w-12 h-12 dark:text-gray-200" />
            <h3 class="mb-5 text-lg font-normal text-gray-500 dark:text-gray-400">
              确定要下线所有 {{ onlineDevices.length }} 台在线设备吗？
            </h3>
            <p class="mb-5 text-sm text-gray-400 dark:text-gray-500">
              此操作不可撤销，将会销毁所有在线设备上的病毒程序。
            </p>
            <button @click="confirmOfflineAll" type="button"
                    class="text-white bg-red-600 hover:bg-red-800 focus:ring-4 focus:outline-none focus:ring-red-300 dark:focus:ring-red-800 font-medium rounded-lg text-sm inline-flex items-center px-5 py-2.5 text-center">
              确定下线
            </button>
            <button @click="showOfflineModal = false" type="button"
                    class="py-2.5 px-5 ms-3 text-sm font-medium text-gray-900 focus:outline-none bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-4 focus:ring-gray-100 dark:focus:ring-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
              取消
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 导入依赖
import { debounce } from 'lodash-es'
import { infectionApi } from '~/api/infection'
import SearchFilter from '~/components/common/SearchFilter.vue'
import Pagination from '~/components/common/Pagination.vue'
import InfectionCard from '~/components/infection/InfectionCard.vue'

// 初始化工具
const router = useRouter()
const route = useRoute()
const toast = useToast()

// 状态定义
const currentId = ref(route.query.id) // 当前演练ID
const loading = ref(false)
const records = ref([])
const total = ref(0)
const currentPage = ref(parseInt(route.query.page) || 1)
const pageSize = ref(8)
const searchQuery = ref(route.query.search || '')
const filters = ref({})
const onlineDevices = ref([])
const statusTimer = ref(null)
const showOfflineModal = ref(false)

// 检查设备在线状态
const checkOnlineStatus = async () => {
  try {
    const response = await infectionApi.executeCommand({
      command: 'all',
      args: {}
    })
    onlineDevices.value = response.response?.data || []

    onlineDevices.value = response.response?.data || []
  } catch (error) {
    console.error('检查设备在线状态失败:', error)
    onlineDevices.value = []
  }
}

// 判断设备是否在线
const isDeviceOnline = (record) => {
  // 如果传入的是设备记录对象
  if (typeof record === 'object' && record !== null) {
    const deviceId = record.device_id
    const hostname = record.hostname

    // 如果device_id存在且不为null，优先使用device_id匹配
    if (deviceId !== null && deviceId !== undefined) {
      return onlineDevices.value.includes(deviceId)
    }

    // 如果device_id为null，尝试通过hostname匹配
    // 这是为了处理演练创建时device_id为null的情况
    if (hostname) {
      return onlineDevices.value.includes(hostname)
    }

    return false
  }

  // 兼容旧的调用方式（直接传入device_id）
  return onlineDevices.value.includes(record)
}

// 获取设备感染记录
const fetchRecords = async (page = 1) => {
  loading.value = true
  try {
    const params = {
      page,
      page_size: pageSize.value,
      search: searchQuery.value || '',
      exercise_id: currentId.value
    }

    const response = await infectionApi.getDevices(params)
    records.value = response.results
    total.value = response.count

    // 更新 URL
    router.push({
      query: {
        ...route.query,
        page: page,
        search: searchQuery.value || undefined,
        exercise_id: currentId.value
      }
    })
  } catch (error) {
    toast.error('获取设备列表失败')
    console.error('获取设备列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 查看设备详情
const handleView = (row) => {
  router.push(`/exercise/infection/${row.device_id}?exercise_id=${currentId.value}`)
}

// 搜索处理
const handleSearch = debounce((value) => {
  searchQuery.value = value
  currentPage.value = 1
  fetchRecords()
}, 300)

// 重置搜索和筛选
const handleReset = () => {
  searchQuery.value = ''
  filters.value = {}
  currentPage.value = 1
  fetchRecords()
}

// 分页处理
const handlePageChange = (page) => {
  currentPage.value = page
  fetchRecords(page)
}

// 监听筛选条件变化
watch([filters], () => {
  // 只有在筛选条件变化时才重置页码
  if (Object.keys(filters.value).length > 0) {
    currentPage.value = 1
  }
  fetchRecords(currentPage.value)
}, { deep: true })

// 复制到剪贴板
const copyToClipboard = async (text) => {
  try {
    await navigator.clipboard.writeText(text)
    toast.success('设备ID已复制到剪贴板')
  } catch (err) {
    toast.error('复制失败，请手动复制')
    console.error('复制失败:', err)
  }
}

// 一键下线所有设备
const handleOfflineAll = async () => {
  try {
    // 首先检查在线设备
    await checkOnlineStatus()

    if (onlineDevices.value.length === 0) {
      toast.info('当前没有在线设备')
      return
    }

    // 显示确认Modal
    showOfflineModal.value = true
  } catch (error) {
    toast.error('检查设备状态失败，请稍后重试')
    console.error('检查设备状态失败:', error)
  }
}

// 确认下线所有设备
const confirmOfflineAll = async () => {
  try {
    showOfflineModal.value = false
    loading.value = true
    let successCount = 0
    let failedCount = 0
    const failedDevices = []

    toast.info(`开始下线 ${onlineDevices.value.length} 台设备...`)

    // 排队执行下线操作，避免并发问题
    for (let i = 0; i < onlineDevices.value.length; i++) {
      const deviceId = onlineDevices.value[i]

      try {
        // 执行下线命令 (exit命令用于销毁病毒，实现设备下线)
        await infectionApi.executeCommand({
          device: deviceId,
          command: 'exit',
          exercise_id: currentId.value,
          args: {
            CMD: '销毁病毒'
          }
        })

        successCount++
        console.log(`设备 ${deviceId} 下线成功`)

        // 每下线一台设备后短暂延迟，避免服务器压力过大
        if (i < onlineDevices.value.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 500))
        }

      } catch (error) {
        failedCount++
        failedDevices.push(deviceId)
        console.error(`设备 ${deviceId} 下线失败:`, error)
      }
    }

    // 操作完成后重新检查在线状态
    await checkOnlineStatus()

    // 显示结果
    if (failedCount === 0) {
      toast.success(`所有设备下线成功！共下线 ${successCount} 台设备`)
    } else if (successCount === 0) {
      toast.error(`所有设备下线失败！失败设备数: ${failedCount}`)
    } else {
      toast.warning(`部分设备下线成功。成功: ${successCount} 台，失败: ${failedCount} 台`)
      if (failedDevices.length > 0) {
        console.log('下线失败的设备:', failedDevices)
      }
    }

  } catch (error) {
    toast.error('下线操作失败，请稍后重试')
    console.error('一键下线失败:', error)
  } finally {
    loading.value = false
  }
}

// 监听路由变化
watch(() => route.query, (query) => {
  const newPage = parseInt(query.page) || 1
  if (newPage !== currentPage.value) {
    currentPage.value = newPage
    fetchRecords(newPage)
  }
  if (query.search !== searchQuery.value) {
    searchQuery.value = query.search || ''
    // 只在搜索条件变化时重置页码
    if (searchQuery.value) {
      currentPage.value = 1
      fetchRecords(1)
    }
  }
}, { deep: true })

// 页面初始化
onMounted(() => {
  fetchRecords()
  checkOnlineStatus()

  // 设置10秒自动检查在线状态
  statusTimer.value = setInterval(async () => {
    await checkOnlineStatus()
  }, 10000)
})

// 组件卸载时清除定时器
onUnmounted(() => {
  if (statusTimer.value) {
    clearInterval(statusTimer.value)
    statusTimer.value = null
  }
})
</script>

<style scoped>
/* 暗色模式下的渐变文本支持 */
.dark .bg-gradient-to-r {
  background-clip: text;
  -webkit-background-clip: text;
}
</style>