#!/bin/bash

# Docker Compose 管理脚本
# 用法: ./docker-manage.sh [start|stop|restart|logs|status|clean]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目名称
PROJECT_NAME="fls"

# 打印带颜色的消息
print_message() {
    local color=$1
    local message=$2
    echo -e "${color}[$(date '+%Y-%m-%d %H:%M:%S')] ${message}${NC}"
}

# 检查Docker和Docker Compose是否安装
check_requirements() {
    if ! command -v docker &> /dev/null; then
        print_message $RED "错误: Docker 未安装或未在PATH中"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null; then
        print_message $RED "错误: Docker Compose 未安装或未在PATH中"
        exit 1
    fi
}

# 启动服务
start_services() {
    print_message $BLUE "正在启动 $PROJECT_NAME 服务..."
    
    # 检查环境变量文件
    if [ -f ".env.docker" ]; then
        print_message $GREEN "使用环境变量文件: .env.docker"
        docker-compose --env-file .env.docker up -d
    else
        print_message $YELLOW "未找到 .env.docker 文件，使用默认配置"
        docker-compose up -d
    fi
    
    print_message $GREEN "服务启动完成！"
    print_message $BLUE "前端访问地址: http://localhost:3000"
    print_message $BLUE "后端API地址: http://localhost:8000"
    print_message $BLUE "pgAdmin地址: http://localhost:5050"
}

# 停止服务
stop_services() {
    print_message $BLUE "正在停止 $PROJECT_NAME 服务..."
    docker-compose down
    print_message $GREEN "服务已停止"
}

# 重启服务
restart_services() {
    print_message $BLUE "正在重启 $PROJECT_NAME 服务..."
    stop_services
    sleep 2
    start_services
}

# 查看日志
view_logs() {
    local service=$1
    if [ -z "$service" ]; then
        print_message $BLUE "显示所有服务日志..."
        docker-compose logs -f
    else
        print_message $BLUE "显示 $service 服务日志..."
        docker-compose logs -f $service
    fi
}

# 查看服务状态
check_status() {
    print_message $BLUE "检查服务状态..."
    docker-compose ps
    echo ""
    print_message $BLUE "容器健康状态:"
    docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
}

# 清理资源
clean_resources() {
    print_message $YELLOW "警告: 这将删除所有容器、网络和未使用的镜像"
    read -p "确定要继续吗? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        print_message $BLUE "正在清理资源..."
        docker-compose down -v --remove-orphans
        docker system prune -f
        print_message $GREEN "清理完成"
    else
        print_message $YELLOW "操作已取消"
    fi
}

# 显示帮助信息
show_help() {
    echo "Docker Compose 管理脚本"
    echo ""
    echo "用法: $0 [命令] [选项]"
    echo ""
    echo "命令:"
    echo "  start          启动所有服务"
    echo "  stop           停止所有服务"
    echo "  restart        重启所有服务"
    echo "  logs [服务名]   查看日志 (可选指定服务名)"
    echo "  status         查看服务状态"
    echo "  clean          清理所有资源 (危险操作)"
    echo "  help           显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 start                # 启动所有服务"
    echo "  $0 logs backend         # 查看后端服务日志"
    echo "  $0 logs                 # 查看所有服务日志"
}

# 主函数
main() {
    check_requirements
    
    case "${1:-help}" in
        start)
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        logs)
            view_logs $2
            ;;
        status)
            check_status
            ;;
        clean)
            clean_resources
            ;;
        help|--help|-h)
            show_help
            ;;
        *)
            print_message $RED "未知命令: $1"
            show_help
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
