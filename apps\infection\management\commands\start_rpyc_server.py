"""
Django管理命令：启动RPyC服务器
"""
import signal
import sys
from django.core.management.base import BaseCommand
from django.conf import settings
from apps.infection.rpyc_server import rpyc_manager


class Command(BaseCommand):
    help = '启动RPyC服务器用于恶意软件模拟通信'
    
    def add_arguments(self, parser):
        parser.add_argument(
            '--host',
            type=str,
            default='0.0.0.0',
            help='服务器监听地址 (默认: 0.0.0.0)'
        )
        parser.add_argument(
            '--port',
            type=int,
            default=18861,
            help='服务器监听端口 (默认: 18861)'
        )
    
    def handle(self, *args, **options):
        host = options['host']
        port = options['port']
        
        # 设置服务器参数
        rpyc_manager.host = host
        rpyc_manager.port = port
        
        # 设置信号处理器
        def signal_handler(sig, frame):
            self.stdout.write(
                self.style.WARNING('\n正在停止RPyC服务器...')
            )
            rpyc_manager.stop_server()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        self.stdout.write(
            self.style.SUCCESS(f'正在启动RPyC服务器 {host}:{port}...')
        )
        
        # 启动服务器
        if rpyc_manager.start_server():
            self.stdout.write(
                self.style.SUCCESS(f'RPyC服务器已启动在 {host}:{port}')
            )
            self.stdout.write(
                self.style.WARNING('按 Ctrl+C 停止服务器')
            )
            
            # 保持主线程运行
            try:
                while True:
                    import time
                    time.sleep(1)
            except KeyboardInterrupt:
                self.stdout.write(
                    self.style.WARNING('\n正在停止RPyC服务器...')
                )
                rpyc_manager.stop_server()
        else:
            self.stdout.write(
                self.style.ERROR('RPyC服务器启动失败')
            )
            sys.exit(1)
