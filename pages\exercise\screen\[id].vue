<template>
  <div class="min-h-screen bg-[url(/static/img/screen/bg-screen.png)] bg-cover bg-center">
    <header class="w-full flex justify-center text-center text-white fixed top-0 
      left-0 right-0 bg-[url(/static/img/screen/nav-header.png)] bg-cover h-24" style="background-size: 100% 96px;">
      <div class="w-3/4 h-24 relative">
        <span class="absolute top-3 left-0 right-0 text-center leading-10 text-3xl tracking-wider gradient-text">
          {{ detail?.name }}
        </span>
      </div>
    </header>
    <main class="pt-24 flex justify-between gap-6 px-8">
      <div class="w-1/4">
        <div
          class="w-full h-[calc(calc(100vh-144px-16px)/3)] bg-[url(/static/img/screen/small-card.png)] text-center text-white mb-4 bg-no-repeat p-6 relative"
          style="background-size: 100% 100%">
          <div class="bg-[url(/static/img/screen/title.png)] h-8 bg-no-repeat text-left flex gap-2"
            style="background-size: 100% 32px">
            <img src="/static/img/screen/icon.png" class="w-6 h-6" alt="icon" />
            钓鱼邮件发送总览
          </div>

          <div class="h-[calc(calc(calc(100vh-144px)/3)-80px)]" ref="sendTotal">
          </div>
          <div class="absolute bottom-7 left-0 right-0 flex justify-around text-sm">
            <span class="text">发送总数</span>
            <span class="text">发送成功数</span>
            <span class="text">发送失败数</span>
          </div>
        </div>

        <div
          class="w-full h-[calc(calc(100vh-144px-16px)/3)] bg-[url(/static/img/screen/small-card.png)] text-center text-white mb-4 bg-no-repeat p-6"
          style="background-size: 100% 100%">
          <div class="bg-[url(/static/img/screen/title.png)] h-8 bg-no-repeat text-left flex gap-2"
            style="background-size: 100% 32px">
            <img src="/static/img/screen/icon.png" class="w-6 h-6" alt="icon" />
            打开数据统计
          </div>

          <div class="h-[calc(calc(calc(100vh-144px)/3)-80px)]" ref="clickTotal"></div>
        </div>

        <div
          class="w-full h-[calc(calc(100vh-144px-16px)/3)] bg-[url(/static/img/screen/small-card.png)] text-center text-white mb-4 bg-no-repeat p-6"
          style="background-size: 100% 100%">
          <div class="bg-[url(/static/img/screen/title.png)] h-8 bg-no-repeat text-left flex gap-2"
            style="background-size: 100% 32px">
            <img src="/static/img/screen/icon.png" class="w-6 h-6" alt="icon" />
            钓鱼邮件点击情况
          </div>

          <div class="h-[calc(calc(calc(100vh-144px)/3)-80px)]" ref="dataTotal"></div>
        </div>
      </div>

      <div class="flex-1 max-w-2/4">
        <div
          class="w-full h-[calc(100vh-444px)] bg-[url(/static/img/screen/high-card.png)] text-center text-white mb-4 bg-no-repeat p-6 relative"
          style="background-size: 100% 100%">
          <RelationGraph :exerciseId="id" />
        </div>

        <div class="w-full text-center text-white">
          <div
            class="w-full h-[calc(calc(100vh-144px)/3)] min-h-52 bg-[url(/static/img/screen/long-card.png)] text-center text-white bg-no-repeat p-6 relative"
            style="background-size: 100% 100%">
            <div class="bg-[url(/static/img/screen/long-title.png)] h-8 bg-no-repeat text-left flex gap-2"
              style="background-size: 100% 32px">
              <img src="/static/img/screen/icon.png" class="w-6 h-6" alt="icon" />
              参与情况
            </div>
            <div class="flex">
              <div ref="department" class="w-1/2 h-[calc(calc(calc(100vh-144px)/3)-60px)] border-r border-r-[#263c79]">
              </div>
              <div ref="equipment" class="w-1/2 h-[calc(calc(calc(100vh-144px)/3)-60px)]"></div>
            </div>
          </div>
        </div>
      </div>

      <div class="w-1/4">
        <div
          class="w-full h-[calc(calc(100vh-144px)/3)] bg-[url(/static/img/screen/small-card.png)] text-center text-white mb-4 bg-no-repeat p-6 relative"
          style="background-size: 100% 100%">
          <div class="bg-[url(/static/img/screen/title.png)] h-8 bg-no-repeat text-left flex gap-2"
            style="background-size: 100% 32px">
            <img src="/static/img/screen/icon.png" class="w-6 h-6" alt="icon" />
            感染总览
          </div>

          <div class="h-[calc(calc(calc(100vh-144px)/3)-80px)]" ref="infectTotal">
          </div>
          <div class="absolute bottom-7 left-0 right-0 flex justify-around text-sm">
            <span class="text">资产总数</span>
            <span class="text">未感染数</span>
            <span class="text">已感染数</span>
          </div>
        </div>

        <div
          class="w-full h-[calc(calc(calc(100vh-144px)/3)*2)] bg-[url(/static/img/screen/high-card.png)] text-center text-white mb-4 bg-no-repeat p-6 relative"
          style="background-size: 100% 100%">
          <div class="bg-[url(/static/img/screen/title.png)] h-8 bg-no-repeat text-left flex gap-2"
            style="background-size: 100% 32px">
            <img src="/static/img/screen/icon.png" class="w-6 h-6" alt="icon" />
            感染情况详情
          </div>

          <NuxtLink :to="`/exercise/infection?page=1&id=${id}&exercise_id=${id}`"
            class="absolute top-6 right-6 w-16 h-6 leading-6 text-white text-sm cursor-pointer rounded"
            style="background: linear-gradient( 180deg, #2557E2 0%, #002078 100%);box-shadow: inset 3px 3px 4px 0px rgba(71,230,252,0.25);">
            详情
          </NuxtLink>

          <div class="overflow-hidden">
            <ul class="overflow-hidden my-4" style="height: calc(100vh - 144px - 392px)">
              <template v-if="noticeList.results.length > 0">
                <vue3-seamless-scroll :list="noticeList.results" class="text-white" :step="0.5">
                  <template v-slot="{ data, index }">
                    <li :class="['w-full h-14', index % 2 === 0 ? 'bg-[#0D2B60]' : 'bg-[#081242]']"
                      style="border: 1px solid;border-image: linear-gradient(270deg, rgba(0, 128, 254, 0), rgba(0, 128, 254, 1), rgba(0, 128, 254, 0)) 1 1;">
                      <div class="p-4 px-6 flex items-center">
                        <div class="whitespace-nowrap">
                          {{ data?.first_seen }} {{ truncateText(`${data?.hostname}`, 20, true) }} 已被感染
                        </div>
                      </div>
                    </li>
                  </template>
                </vue3-seamless-scroll>
              </template>

              <template v-else>
                <div class="w-full h-full flex items-center justify-center text-center">
                  <div class="mb-10 text-[#058ac1]">
                    <svg t="1739331332032" class="icon w-36 h-36" viewBox="0 0 1024 1024" version="1.1"
                      xmlns="http://www.w3.org/2000/svg" p-id="5758" width="1024" height="1024">
                      <path
                        d="M167.38986667 829.8496c6.00746667 0 9.8304 3.82293333 9.8304 9.8304s-3.82293333 9.8304-9.8304 9.8304H71.2704c-6.00746667 0-9.8304-3.82293333-9.8304-9.8304s3.82293333-9.8304 9.8304-9.8304h96.11946667z m739.46453333-75.91253333c6.00746667 0 9.8304 3.82293333 9.8304 9.8304s-3.82293333 9.8304-9.8304 9.8304h-167.66293333v25.66826666c0 27.8528-21.84533333 49.69813333-49.69813334 49.69813334H659.456c0 6.00746667-2.73066667 12.01493333-7.09973333 16.93013333-9.8304 9.8304-24.576 9.8304-34.95253334 0l-15.83786666-16.93013333H217.088c-6.00746667 0-9.8304-3.82293333-9.8304-9.8304s3.82293333-9.8304 9.8304-9.8304h43.69066667c-6.5536-8.73813333-9.8304-19.11466667-9.8304-30.03733334v-496.98133333c0-27.8528 21.84533333-49.69813333 49.69813333-49.69813333h31.67573333v-25.66826667c0-27.8528 21.84533333-49.69813333 49.69813334-49.69813333h389.9392c27.8528 0 49.69813333 21.84533333 49.69813333 49.69813333v496.98133333c0 10.92266667-3.82293333 21.84533333-9.8304 30.03733334h95.0272z m-156.74026667 0h20.75306667c16.93013333 0 29.4912-13.1072 29.4912-30.03733334v-496.98133333c0-16.93013333-13.1072-30.03733333-29.4912-30.03733333H380.928c-16.93013333 0-29.4912 13.1072-29.4912 30.03733333v25.66826667h339.1488c27.8528 0 49.69813333 21.84533333 49.69813333 49.69813333v451.65226667h9.8304z m-233.19893333-24.576c33.86026667-33.86026667 33.86026667-88.4736 0-122.33386667-15.83786667-15.83786667-37.6832-25.12213333-60.6208-25.12213333-22.9376 0-44.78293333 8.73813333-60.6208 25.12213333-33.86026667 33.86026667-33.86026667 88.4736 0 122.33386667 15.83786667 15.83786667 37.6832 25.12213333 60.6208 25.12213333 21.84533333-0.54613333 43.69066667-9.28426667 60.6208-25.12213333zM412.60373333 637.61066667c2.18453333 1.09226667 2.73066667 3.82293333 2.18453334 7.09973333-7.09973333 12.01493333-8.192 27.8528-2.73066667 40.96 1.09226667 2.73066667 0 4.9152-2.73066667 7.09973333h-2.18453333c-2.18453333 0-3.82293333-1.09226667-4.9152-2.73066666-6.00746667-15.83786667-4.9152-34.95253333 2.73066667-49.69813334 1.6384-2.73066667 4.9152-3.82293333 7.64586666-2.73066666z m307.47306667 162.2016v-496.98133334c0-16.93013333-13.1072-30.03733333-30.03733333-30.03733333H299.55413333c-16.93013333 0-30.03733333 13.1072-30.03733333 30.03733333v497.52746667c0 16.93013333 13.1072 30.03733333 30.03733333 30.03733333h282.89706667l-27.8528-27.8528c-6.5536-6.5536-9.28426667-16.93013333-6.00746667-25.66826666l-24.576-25.66826667c-18.56853333 15.83786667-42.5984 24.576-67.1744 25.12213333-28.94506667 0-54.61333333-10.92266667-75.3664-30.58346666-41.50613333-40.96-41.50613333-108.1344-0.54613333-149.64053334l0.54613333-0.54613333c19.6608-19.6608 46.42133333-30.58346667 75.3664-30.58346667s54.61333333 10.92266667 75.3664 30.58346667c38.77546667 38.77546667 40.41386667 101.5808 7.09973334 143.08693333l25.66826666 25.66826667c8.73813333-2.73066667 19.11466667-2.18453333 25.66826667 6.00746667l61.71306667 61.71306666h38.77546666c15.29173333-2.18453333 28.94506667-15.29173333 28.94506667-32.22186666zM361.2672 374.92053333c-6.00746667 0-9.8304-3.82293333-9.8304-9.8304s3.82293333-9.8304 9.8304-9.8304h173.6704c6.00746667 0 9.8304 3.82293333 9.8304 9.8304s-3.82293333 9.8304-9.8304 9.8304H361.2672z m231.0144 43.69066667c6.00746667 0 9.8304 3.82293333 9.8304 9.8304s-3.82293333 9.8304-9.8304 9.8304H363.99786667c-6.00746667 0-9.8304-3.82293333-9.8304-9.8304s3.82293333-9.8304 9.8304-9.8304h228.28373333zM480.8704 482.5088c6.00746667 0 9.8304 3.82293333 9.8304 9.8304s-3.82293333 9.8304-9.8304 9.8304H359.08266667c-6.00746667 0-9.8304-3.82293333-9.8304-9.8304s3.82293333-9.8304 9.8304-9.8304h121.78773333zM101.85386667 263.50933333c10.92266667 0 19.6608 8.73813333 19.6608 19.6608s-8.73813333 19.6608-19.6608 19.6608-19.6608-8.73813333-19.6608-19.6608c0-10.37653333 8.73813333-19.6608 19.6608-19.6608z m0 60.07466667c21.84533333 0 39.86773333-18.0224 39.86773333-39.86773333s-18.0224-39.86773333-39.86773333-39.86773334-39.86773333 18.0224-39.86773334 39.86773334c0.54613333 21.84533333 18.0224 39.3216 39.86773334 39.86773333zM932.52266667 340.51413333c-6.00746667 0-9.8304-3.82293333-9.8304-9.8304s3.82293333-9.8304 9.8304-9.8304 9.8304 3.82293333 9.8304 9.8304c0 5.46133333-3.82293333 9.8304-9.8304 9.8304z m0-39.86773333c-16.93013333 0-29.4912 13.1072-29.4912 30.03733333s13.1072 30.03733333 29.4912 30.03733334 29.4912-13.1072 29.4912-30.03733334c0-17.47626667-12.56106667-30.03733333-29.4912-30.03733333z m-725.26506667-103.76533333c0 6.00746667 3.82293333 9.8304 9.8304 9.8304s9.8304-3.82293333 9.8304-9.8304v-12.01493334H238.93333333c6.00746667 0 9.8304-3.82293333 9.8304-9.8304s-3.82293333-9.8304-9.8304-9.8304h-12.01493333v-12.01493333c0-6.00746667-3.82293333-9.8304-9.8304-9.8304s-9.8304 3.82293333-9.8304 9.8304v12.01493333H195.24266667c-6.00746667 0-9.8304 3.82293333-9.8304 9.8304s3.82293333 9.8304 9.8304 9.8304h12.01493333v12.01493334z m-24.02986667 278.528h-12.01493333v-12.01493334c0-6.00746667-3.82293333-9.8304-9.8304-9.8304S151.552 457.38666667 151.552 463.39413333v12.01493334h-12.01493333c-6.00746667 0-9.8304 3.82293333-9.8304 9.8304s3.82293333 9.8304 9.8304 9.8304H151.552v12.01493333c0 6.00746667 3.82293333 9.8304 9.8304 9.8304s9.8304-3.82293333 9.8304-9.8304V495.616h12.01493333c6.00746667 0 9.8304-3.82293333 9.8304-9.8304 0.54613333-6.00746667-3.82293333-10.37653333-9.8304-10.37653333z"
                        p-id="5759" fill="#058ac1"></path>
                      <path
                        d="M920.50773333 503.26186667h-12.01493333v-12.01493334c0-6.00746667-3.82293333-9.8304-9.8304-9.8304s-9.8304 3.82293333-9.8304 9.8304v12.01493334h-12.01493333c-6.00746667 0-9.8304 3.82293333-9.8304 9.8304s3.82293333 9.8304 9.8304 9.8304h12.01493333v12.01493333c0 6.00746667 3.82293333 9.8304 9.8304 9.8304s9.8304-3.82293333 9.8304-9.8304V522.92266667h12.01493333c6.00746667 0 9.8304-3.82293333 9.8304-9.8304 0-5.46133333-4.36906667-9.8304-9.8304-9.8304"
                        p-id="5760" fill="#058ac1"></path>
                    </svg>
                    暂无数据
                  </div>
                </div>
              </template>
            </ul>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup>
import RelationGraph from '~/components/screen/RelationGraph.vue'
import { Vue3SeamlessScroll } from "vue3-seamless-scroll"
import { exerciseApi } from '@/api/exercises'
import { screenApi } from '~/api/screen'
import { assetApi } from '@/api/asset'
import * as echarts from 'echarts'
import "echarts-liquidfill";

definePageMeta({
  layout: 'empty'
})

const route = useRoute()
const id = route.params.id
const sendTotal = ref(null)
const dataTotal = ref(null)
const clickTotal = ref(null)
const infectTotal = ref(null)
const department = ref(null)
const equipment = ref(null)

// 获取演练详情
const { data: detail } = await useAsyncData('detail', () => exerciseApi.getExercisesDetailApi(id))

const { data: departmentList } = await useAsyncData('departmentList', () => exerciseApi.getDepartmentListApi(id))

const { data: noticeList } = await useAsyncData('noticeList', () => screenApi.getDevices({ exercise_id: id, infection_count: 0 }))

const { data: sendData } = await useAsyncData('sendData', () => screenApi.getPhishingEmailSendingOverviewApi(id))

const { data: openData } = await useAsyncData('openData', () => screenApi.getOpenEmailDataStatisticsApi(id))

const { data: infectionData } = await useAsyncData('infectionData', () => screenApi.getInfectionOverviewApi(id))

const { data: clickData } = await useAsyncData('clickData', () => screenApi.getPhishingEmailClickStatsApi(id))

// 钓鱼邮件发送总览
const renderSendChart = () => {
  const myLiuqiud = echarts.init(sendTotal.value);

  // 计算成功率和失败率
  const success = sendData.value.success_count / sendData.value.count || 0;
  const failed = sendData.value.failed_count / sendData.value.count || 0;

  const option = {
    backgroundColor: "transparent", //背景色
    series: [
      {
        type: "liquidFill", //配置echarts图类型
        radius: "50%",
        center: ["15%", "50%"],
        data: [1, 1],  //设置波浪的值 
        backgroundStyle: {
          borderWidth: 1,
          borderColor: "rgba(45, 158, 255, 1)",
          color: "transparent",//水球图内部背景色
        },
        outline: {
          borderDistance: 8,
          itemStyle: {
            borderWidth: 3,
            borderColor: "rgba(45, 158, 255, 1)",
          },
        },
        color: [ //波浪颜色
          {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 1,
                color: "rgba(181, 241, 252, 1)", //下
              },
              {
                offset: 0,
                color: "rgba(11, 216, 211, 1)",
              },
            ],
            globalCoord: false,
          },
          {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 1,
                color: "rgb(11, 205, 208)", //下
              },
              {
                offset: 0,
                color: "rgb(18, 96, 174)",
              },
            ],
            globalCoord: false,
          },
        ],
        label: {
          normal: {
            formatter: sendData.value.count.toString(),
            color: "#fff",
            fontSize: 36,
          },
        },
      },
      {
        type: "liquidFill", //配置echarts图类型
        radius: "50%",
        center: ["50%", "50%"],
        data: [success, success],  //设置波浪的值 
        backgroundStyle: {
          borderWidth: 1,
          borderColor: "rgba(255, 192, 45, 1)",
          color: "transparent",//水球图内部背景色
        },
        outline: {
          borderDistance: 8,
          itemStyle: {
            borderWidth: 3,
            borderColor: "rgba(255, 192, 45, 1)",
          },
        },
        color: [ //波浪颜色
          {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 1,
                color: "rgba(255, 192, 45, 1)", //下
              },
              {
                offset: 0,
                color: "rgba(179, 131, 20, 0.72)",
              },
            ],
            globalCoord: false,
          },
          {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 1,
                color: "rgb(217, 156, 13)", //下
              },
              {
                offset: 0,
                color: "rgb(236, 176, 38)",
              },
            ],
            globalCoord: false,
          },
        ],
        label: {
          normal: {
            formatter: sendData.value.success_count.toString(),
            color: "#fff",
            fontSize: 36,
          },
        },
      },
      {
        type: "liquidFill", //配置echarts图类型
        radius: "50%",
        center: ["85%", "50%"],
        data: [failed, failed],  //设置波浪的值 
        backgroundStyle: {
          borderWidth: 1,
          borderColor: "rgb(250, 87, 42)",
          color: "transparent",//水球图内部背景色
        },
        outline: {
          borderDistance: 8,
          itemStyle: {
            borderWidth: 3,
            borderColor: "rgb(250, 87, 42)",
          },
        },
        color: [ //波浪颜色
          {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 1,
                color: "rgb(220, 59, 15)", //下
              },
              {
                offset: 0,
                color: "rgb(149, 50, 36)",
              },
            ],
            globalCoord: false,
          },
          {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 1,
                color: "rgb(220, 59, 15)", //下
              },
              {
                offset: 0,
                color: "rgb(250, 87, 42)",
              },
            ],
            globalCoord: false,
          },
        ],
        label: {
          normal: {
            formatter: sendData.value.failed_count.toString(),
            color: "#fff",
            fontSize: 36,
          },
        },
      },
    ]
  }
  myLiuqiud.setOption(option);

  window.addEventListener("resize", () => {
    myLiuqiud.resize();
  });
}

// 打开数据统计
const renderClickChart = () => {
  const myChart = echarts.init(clickTotal.value);

  const option = {
    series: [
      {
        type: 'gauge',
        radius: "50%",
        startAngle: -90,
        endAngle: 270,
        center: ['15%', '50%'],
        title: {
          fontSize: 14,
          show: true
        },
        pointer: {
          show: false
        },
        progress: {
          show: true,
          overlap: false,
          roundCap: true,
          clip: false,
          backgroundColor: 'transparent',
          itemStyle: {
            borderWidth: 1,
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(255, 237, 204, 1)' }, // 0% 处的颜色
                { offset: 1, color: 'rgba(255, 165, 0, 1)' } // 100% 处的颜色
              ],
              global: false // 缺省为 false
            }
          }
        },
        axisLine: {
          show: true,
          lineStyle: {
            width: 10,
            color: [
              [1, 'rgb(32, 49, 92)']
            ]
          }
        },
        splitNumber: 20,
        splitLine: {
          show: true,
          distance: 6,
          length: 3,
          lineStyle: {
            width: 1,
            color: 'rgb(255, 255, 255)'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: false
        },
        max: sendData.value.count,
        data: [
          {
            value: openData.value.today_open,
            name: '今日打开数',
            title: {
              color: '#ffffff',
              offsetCenter: ['0%', '150%']
            },
            detail: {
              valueAnimation: true,
              offsetCenter: ['0%', '5%']
            }
          },
        ],
        detail: {
          width: 50,
          height: 14,
          fontSize: 36,
          color: 'rgb(255, 165, 0)',
          formatter: '{value}'
        }
      },
      {
        type: 'gauge',
        radius: "50%",
        startAngle: -90,
        endAngle: 270,
        center: ['50%', '50%'],
        title: {
          fontSize: 14,
          show: true
        },
        pointer: {
          show: false
        },
        progress: {
          show: true,
          overlap: false,
          roundCap: true,
          clip: false,
          backgroundColor: 'transparent',
          itemStyle: {
            borderWidth: 1,
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgb(150, 214, 171)' }, // 0% 处的颜色
                { offset: 1, color: 'rgb(57, 178, 97)' } // 100% 处的颜色
              ],
              global: false // 缺省为 false
            }
          }
        },
        axisLine: {
          show: true,
          lineStyle: {
            width: 10,
            color: [
              [1, 'rgb(32, 49, 92)']
            ]
          }
        },
        splitNumber: 20,
        splitLine: {
          show: true,
          distance: 6,
          length: 3,
          lineStyle: {
            width: 1,
            color: 'rgb(255, 255, 255)'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: false
        },
        max: sendData.value.count,
        data: [
          {
            value: openData.value.total_open,
            name: '总打开数',
            title: {
              color: '#ffffff',
              offsetCenter: ['0%', '150%']
            },
            detail: {
              valueAnimation: true,
              offsetCenter: ['0%', '5%']
            }
          },
        ],
        detail: {
          width: 50,
          height: 14,
          fontSize: 36,
          color: 'rgb(57, 178, 97)',
          formatter: '{value}'
        }
      },
      {
        type: 'gauge',
        radius: "50%",
        startAngle: -90,
        endAngle: 270,
        center: ['85%', '50%'],
        title: {
          fontSize: 14,
          show: true
        },
        pointer: {
          show: false
        },
        progress: {
          show: true,
          overlap: false,
          roundCap: true,
          clip: false,
          backgroundColor: 'transparent',
          itemStyle: {
            borderWidth: 1,
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgb(254, 217, 215)' }, // 0% 处的颜色
                { offset: 1, color: 'rgb(249, 65, 55)' } // 100% 处的颜色
              ],
              global: false // 缺省为 false
            }
          }
        },
        axisLine: {
          show: true,
          lineStyle: {
            width: 10,
            color: [
              [1, 'rgb(32, 49, 92)']
            ]
          }
        },
        splitNumber: 20,
        splitLine: {
          show: true,
          distance: 6,
          length: 3,
          lineStyle: {
            width: 1,
            color: 'rgba(255, 255, 255, 1)'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: false
        },
        max: 100,
        data: [
          {
            value: openData.value.increase_rate,
            name: '环比增长',
            title: {
              color: '#ffffff',
              offsetCenter: ['0%', '150%']
            },
            detail: {
              valueAnimation: true,
              offsetCenter: ['0%', '5%']
            }
          },
        ],
        detail: {
          width: 50,
          height: 14,
          fontSize: 30,
          color: 'rgb(249, 65, 55)',
          formatter: '{value}%'
        }
      },
    ]
  };

  option && myChart.setOption(option);

  window.addEventListener("resize", () => {
    myChart.resize();
  });
}

// 邮件点击情况
const renderDataChart = () => {
  const myChart = echarts.init(dataTotal.value);

  const option = {
    series: [
      {
        type: 'gauge',
        radius: "50%",
        startAngle: -90,
        endAngle: 270,
        center: ['15%', '50%'],
        title: {
          fontSize: 14,
          show: true
        },
        pointer: {
          show: false
        },
        progress: {
          show: true,
          overlap: false,
          roundCap: true,
          clip: false,
          backgroundColor: 'transparent',
          itemStyle: {
            borderWidth: 1,
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(255, 237, 204, 1)' }, // 0% 处的颜色
                { offset: 1, color: 'rgba(255, 165, 0, 1)' } // 100% 处的颜色
              ],
              global: false // 缺省为 false
            }
          }
        },
        axisLine: {
          show: true,
          lineStyle: {
            width: 10,
            color: [
              [1, 'rgb(32, 49, 92)']
            ]
          }
        },
        splitNumber: 20,
        splitLine: {
          show: true,
          distance: 6,
          length: 3,
          lineStyle: {
            width: 1,
            color: 'rgb(255, 255, 255)'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: false
        },
        max: sendData.value.count,
        data: [
          {
            value: clickData.value.total_open,
            name: '邮件打开数',
            title: {
              color: '#ffffff',
              offsetCenter: ['0%', '150%']
            },
            detail: {
              valueAnimation: true,
              offsetCenter: ['0%', '5%']
            }
          },
        ],
        detail: {
          width: 50,
          height: 14,
          fontSize: 36,
          color: 'rgb(255, 165, 0)',
          formatter: '{value}'
        }
      },
      {
        type: 'gauge',
        radius: "50%",
        startAngle: -90,
        endAngle: 270,
        center: ['50%', '50%'],
        title: {
          fontSize: 14,
          show: true
        },
        pointer: {
          show: false
        },
        progress: {
          show: true,
          overlap: false,
          roundCap: true,
          clip: false,
          backgroundColor: 'transparent',
          itemStyle: {
            borderWidth: 1,
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgb(147, 189, 251)' }, // 0% 处的颜色
                { offset: 1, color: 'rgb(1, 101, 245)' } // 100% 处的颜色
              ],
              global: false // 缺省为 false
            }
          }
        },
        axisLine: {
          show: true,
          lineStyle: {
            width: 10,
            color: [
              [1, 'rgb(32, 49, 92)']
            ]
          }
        },
        splitNumber: 20,
        splitLine: {
          show: true,
          distance: 6,
          length: 3,
          lineStyle: {
            width: 1,
            color: 'rgb(255, 255, 255)'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: false
        },
        max: sendData.value.count,
        data: [
          {
            value: clickData.value.link_click_count,
            name: '链接点击数',
            title: {
              color: '#ffffff',
              offsetCenter: ['0%', '150%']
            },
            detail: {
              valueAnimation: true,
              offsetCenter: ['0%', '5%']
            }
          },
        ],
        detail: {
          width: 50,
          height: 14,
          fontSize: 36,
          color: 'rgb(1, 101, 245)',
          formatter: '{value}'
        }
      },
      {
        type: 'gauge',
        radius: "50%",
        startAngle: -90,
        endAngle: 270,
        center: ['85%', '50%'],
        title: {
          fontSize: 14,
          show: true
        },
        pointer: {
          show: false
        },
        progress: {
          show: true,
          overlap: false,
          roundCap: true,
          clip: false,
          backgroundColor: 'transparent',
          itemStyle: {
            borderWidth: 1,
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgb(254, 217, 215)' }, // 0% 处的颜色
                { offset: 1, color: 'rgb(249, 65, 55)' } // 100% 处的颜色
              ],
              global: false // 缺省为 false
            }
          }
        },
        axisLine: {
          show: true,
          lineStyle: {
            width: 10,
            color: [
              [1, 'rgb(32, 49, 92)']
            ]
          }
        },
        splitNumber: 20,
        splitLine: {
          show: true,
          distance: 6,
          length: 3,
          lineStyle: {
            width: 1,
            color: 'rgba(255, 255, 255, 1)'
          }
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          show: false
        },
        max: sendData.value.count,
        data: [
          {
            value: clickData.value.form_count,
            name: '数据提交数',
            title: {
              color: '#ffffff',
              offsetCenter: ['0%', '150%']
            },
            detail: {
              valueAnimation: true,
              offsetCenter: ['0%', '5%']
            }
          },
        ],
        detail: {
          width: 50,
          height: 14,
          fontSize: 36,
          color: 'rgb(249, 65, 55)',
          formatter: '{value}'
        }
      },
    ]
  };

  option && myChart.setOption(option);

  window.addEventListener("resize", () => {
    myChart.resize();
  });
}

// 感染总览
const renderInfectChart = () => {
  const myLiuqiud = echarts.init(infectTotal.value);

  // 计算感染和未感染的比例
  const infected_count = infectionData.value.infected_count / infectionData.value.assets_count || 0;
  const not_infected_count = infectionData.value.not_infected_count / infectionData.value.assets_count || 0;

  const option = {
    backgroundColor: "transparent", //背景色
    series: [
      {
        type: "liquidFill", //配置echarts图类型
        radius: "50%",
        center: ["15%", "50%"],
        data: [1, 1],  //设置波浪的值 
        backgroundStyle: {
          borderWidth: 1,
          borderColor: "rgba(45, 158, 255, 1)",
          color: "transparent",//水球图内部背景色
        },
        outline: {
          borderDistance: 8,
          itemStyle: {
            borderWidth: 3,
            borderColor: "rgba(45, 158, 255, 1)",
          },
        },
        color: [ //波浪颜色
          {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 1,
                color: "rgba(181, 241, 252, 1)", //下
              },
              {
                offset: 0,
                color: "rgba(11, 216, 211, 1)",
              },
            ],
            globalCoord: false,
          },
          {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 1,
                color: "rgb(11, 205, 208)", //下
              },
              {
                offset: 0,
                color: "rgb(18, 96, 174)",
              },
            ],
            globalCoord: false,
          },
        ],
        label: {
          normal: {
            formatter: infectionData.value.assets_count.toString(),
            color: "#fff",
            fontSize: 36,
          },
        },
      },
      {
        type: "liquidFill", //配置echarts图类型
        radius: "50%",
        center: ["50%", "50%"],
        data: [not_infected_count, not_infected_count],  //设置波浪的值
        backgroundStyle: {
          borderWidth: 1,
          borderColor: "rgba(255, 192, 45, 1)",
          color: "transparent",//水球图内部背景色
        },
        outline: {
          borderDistance: 8,
          itemStyle: {
            borderWidth: 3,
            borderColor: "rgba(255, 192, 45, 1)",
          },
        },
        color: [ //波浪颜色
          {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 1,
                color: "rgba(255, 192, 45, 1)", //下
              },
              {
                offset: 0,
                color: "rgba(179, 131, 20, 0.72)",
              },
            ],
            globalCoord: false,
          },
          {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 1,
                color: "rgb(217, 156, 13)", //下
              },
              {
                offset: 0,
                color: "rgb(236, 176, 38)",
              },
            ],
            globalCoord: false,
          },
        ],
        label: {
          normal: {
            formatter: infectionData.value.not_infected_count.toString(),
            color: "#fff",
            fontSize: 36,
          },
        },
      },
      {
        type: "liquidFill", //配置echarts图类型
        radius: "50%",
        center: ["85%", "50%"],
        data: [infected_count, infected_count],  //设置波浪的值
        backgroundStyle: {
          borderWidth: 1,
          borderColor: "rgb(250, 87, 42)",
          color: "transparent",//水球图内部背景色
        },
        outline: {
          borderDistance: 8,
          itemStyle: {
            borderWidth: 3,
            borderColor: "rgb(250, 87, 42)",
          },
        },
        color: [ //波浪颜色
          {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 1,
                color: "rgb(220, 59, 15)", //下
              },
              {
                offset: 0,
                color: "rgb(149, 50, 36)",
              },
            ],
            globalCoord: false,
          },
          {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 1,
                color: "rgb(220, 59, 15)", //下
              },
              {
                offset: 0,
                color: "rgb(250, 87, 42)",
              },
            ],
            globalCoord: false,
          },
        ],
        label: {
          normal: {
            formatter: infectionData.value.infected_count.toString(),
            color: "#fff",
            fontSize: 36,
          },
        },
      },
    ]
  }
  myLiuqiud.setOption(option);

  window.addEventListener("resize", () => {
    myLiuqiud.resize();
  });
}

// 获取目标资产详情
const getTargetAssets = async () => {
  const value = detail.value.target_groups || 0;

  if (value?.length) {
    // 获取所有目标资产组
    const { results: groups } = await assetApi.getAssetGroups({ asset_type: 'EP,SV', page: 1, page_size: 1000 })

    const promises = value.map(async (groupId) => {
      // 从所有组中找到匹配的组
      const group = groups.find(g => g.id === groupId)
      if (!group) return null

      // 获取该组下的资产列表,注意这里要分别获取终端和服务器资产
      const [epAssets, svAssets] = await Promise.all([
        assetApi.getAssets({ group: groupId, asset_type: 'EP', page: 1, page_size: 1000 }),
        assetApi.getAssets({ group: groupId, asset_type: 'SV', page: 1, page_size: 1000 })
      ])

      // 合并两种类型的资产
      const assets = [...epAssets.results, ...svAssets.results]

      return {
        ...group,
        assets
      }
    })

    const targetAssets = (await Promise.all(promises)).filter(Boolean)

    const epAssets = targetAssets.flatMap(group => group.assets.filter(asset => asset.asset_type === 'EP'))
    const svAssets = targetAssets.flatMap(group => group.assets.filter(asset => asset.asset_type === 'SV'))

    return [
      {
        name: '终端',
        value: epAssets?.length || 0,
      },
      {
        name: '服务器',
        value: svAssets?.length || 0,
      }
    ]
  }
}

// 参与信息
const renderDepartmentChart = () => {
  const myChart = echarts.init(department.value);

  const colors = [
    [
      'rgb(9, 108, 213)', 'rgb(211, 229, 247)'
    ],
    [
      'rgb(255, 216, 145)', 'rgb(254, 165, 1)'
    ],
    [
      'rgb(252, 184, 181)', 'rgb(250, 108, 101)'
    ]
  ]

  const data = departmentList.value.department_data.map((items, index) => ({
    ...items,
    itemStyle: {
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        { offset: 0, color: colors[index][0] },
        { offset: 1, color: colors[index][1] }
      ])
    }
  }));

  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      bottom: 20,
      icon: 'circle',
      textStyle: {
        color: '#fff'
      }
    },
    series: [
      {
        name: '部门',
        type: 'pie',
        radius: ['60%', '75%'],
        center: ['50%', '50%'],
        itemStyle: {
          borderRadius: 10,
          borderWidth: 0
        },
        label: {
          show: true,
          position: 'outside'
        },
        labelLine: {
          show: true
        },
        data: data
      }
    ]
  }

  option && myChart.setOption(option);

  window.addEventListener("resize", () => {
    myChart.resize();
  });
}

const renderEquipmentChart = async () => {
  const results = await getTargetAssets();
  const myChart = echarts.init(equipment.value);

  const colors = [
    [
      'rgb(9, 108, 213)', 'rgb(211, 229, 247)'
    ],
    [
      'rgb(255, 216, 145)', 'rgb(254, 165, 1)'
    ],
    [
      'rgb(252, 184, 181)', 'rgb(250, 108, 101)'
    ]
  ];

  const data = results.map((items, index) => ({
    name: items.name,
    value: items.value,
    itemStyle: {
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        { offset: 0, color: colors[index][0] },
        { offset: 1, color: colors[index][1] }
      ])
    }
  }));

  const option = {
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 10,
      bottom: 20,
      icon: 'circle',
      textStyle: {
        color: '#fff'
      }
    },
    series: [
      {
        name: '资产',
        type: 'pie',
        radius: ['60%', '75%'],
        center: ['50%', '50%'],
        itemStyle: {
          borderRadius: 10,
          borderWidth: 0
        },
        label: {
          show: true,
          position: 'outside'
        },
        labelLine: {
          show: true
        },
        data: data
      }
    ]
  }

  option && myChart.setOption(option);

  window.addEventListener("resize", () => {
    myChart.resize();
  });
}

onMounted(() => {
  renderSendChart();
  renderClickChart();
  renderDataChart();
  renderInfectChart();
  renderDepartmentChart();
  renderEquipmentChart();
});
</script>

<style scoped>
.gradient-text {
  font-weight: bold;
  background: -webkit-linear-gradient(#316EB5, #51F4F8);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  /* text-shadow: rgb(122, 202, 236) 0px 0px 3px; */
}

.scrollbar-thin {
  scrollbar-width: thin;
  scrollbar-color: rgba(28, 100, 242, 0.4) rgba(255, 255, 255, 0.1);
}
</style>