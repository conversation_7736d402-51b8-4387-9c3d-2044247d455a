export default defineI18nConfig(() => ({
  legacy: false,
  locale: 'en',
  messages: {
    zh: {
      menu: {
        dashboard: '数据看板',
        users: '用户管理',
        assets: '资产管理',
        config: '演练配置',
        virus: '病毒管理',
        phishing: '钓鱼管理',
        negotiation: '谈判管理',
        exercise: '演练管理',
        family: '病毒家族'
      },
      unit: {
        days: '天',
        hours: '小时',
        minutes: '分',
        seconds: '秒'
      },
      all: {
        reset: '重置',
        search: '搜索',
        close: '关闭',
        enabled: '已启用',
        disabled: '已禁用',
        online: '在线',
        offline: '离线',
        create: '创建',
        edit: '编辑',
        copy: '复制',
        delete: '删除',
        cancel: '取消',
        save: '保存',
        confirm: '确认',
        create_success: '创建成功！',
        create_failed: '创建失败！',
        update_success: '更新成功！',
        update_failed: '更新失败！',
        delete_success: '删除成功！',
        delete_failed: '删除失败！',
        upload_success: '上传成功！',
        upload_failed: '上传失败！',
        import_success: '导入成功！',
        import_failed: '导入失败！',
        prev: '上一页',
        next: '下一页',
        total_1: '显示第',
        total_2: '到',
        total_3: '条，共',
        total_4: '条记录',
        import_multiple: '批量导入',
        administrator: '管理员',
        loading: '加载中...',
        processing: '处理中...',
        no_data: '无匹配结果',
        field_required: '此字段为必填项',
        other: '其他',
        back: '返回',
        current_file: '当前文件',
        click_upload: '点击上传',
        uploaded: '已上传',
        or_drag_and_drop_files: '或拖拽文件到此处',
        support_formats: '支持 {suffix} 格式',
        restriction_format: '限制格式',
        limit_size: '限制大小',
        no_restrictions: '无限制',
        upload_type_message: '文件类型不正确',
        upload_size_message: '文件大小超出限制',
        send: '发送',
        rich_text: '富文本',
        source_code: '源代码',
        preview: '预览',
        import: '导入',
        export: '导出',
        clone: '克隆',
        random_generation: '随机生成',
        select_time: '选择时间',
        select_date: '选择日期',
        form_reset: '表单已重置',
        yes: '是',
        no: '否',
        download: '下载',
        saving: '保存中...',
        need_help: '需要帮助？',
        view_documentation: '查看使用文档',
        success: '成功',
        failed: '失败',
        today: '今天',
        ago: '前',
        delete_all: '批量删除',
        delete_all_message: '确定要删除选中的数据吗？此操作不可恢复！',
        select_one: '请至少选择一项！',
      },
      home: {
        sierting: '思而听',
        home: '首页',
        features: '功能特点',
        services: '服务内容',
        stats: '数据统计',
        about: '关于我们',
        tag: '新一代网络安全平台',
        start_now: '立刻开始',
        learn_more: '了解更多',
        company: '思而听网络科技有限公司',
        friendly_links: '友情链接',
        contact_us: '联系我们',
        first: {
          title_1: '思而听防勒索病毒',
          title_2: '模拟演练平台',
          description: '精准模拟勒索病毒场景，评估防护能力，并整合EDR测试、应急评估和安全培训，最终优化防护策略，多维度强化企业安全防线，有效应对勒索威胁。',
          card_1: '7*24小时安全监控',
          description_1: '全天候不间断监控，及时发现和响应安全威胁',
          card_2: '全天候专业售后服务',
          description_2: '专业团队提供全方位技术支持和问题解决',
          card_3: '全新产品技术文档',
          description_3: '详尽的产品使用指南和技术说明文档',
          card_4: '技术专家服务',
          description_4: '资深技术专家提供专业咨询和解决方案',
        },
        second: {
          tag: '核心优势',
          title: '强大的平台优势',
          description: '全面的病毒库、专业的分析能力、高效的响应机制',
          card_1: '病毒家族库',
          description_1: '覆盖200+主流勒索病毒家族，提供IOC特征、攻击手法、加密算法等专业分析报告。深入解析病毒行为模式，为企业提供精准防护策略。',
          card_2: '多层次攻击链分析',
          description_2: '基于MITRE ATT&CK框架，构建完整攻击链路图谱。还原攻击者视角，深度分析攻击技术和战术，帮助企业发现防护短板。',
          card_3: '快速响应与恢复',
          description_3: '提供一键隔离、终止进程、清除样本等快速响应能力。配合专业的应急预案，实现分钟级威胁处置，最大程度降低攻击影响。',
          card_4: '定制化样本',
          description_4: '支持定制不同变种的勒索样本，兼容主流EDR/XDR产品。通过真实攻击场景，全面评估安全产品的检测和防护能力。',
          card_5: '威胁情报深度整合',
          description_5: '整合全球最新勒索威胁情报，实时更新攻击战术和技术特征。预判攻击趋势，帮助企业构建前瞻性防护体系。',
          card_6: '演练报告评分',
          description_6: '基于多维度指标体系，量化评估防护效果。生成专业的改进建议报告，从技术和管理层面助力企业持续提升安全能力。'
        },
        third: {
          tag: '服务流程',
          title: '专业的演练服务体系',
          description: '从准备到实施再到总结，提供全流程专业服务支持',
          preparation_phase: {
            title: '准备阶段',
            card_1: '演练方案定制',
            description_1: '根据客户现场环境和实际需求，制定演练流程，明确参演对象及职责，设定演练目标，列出注意事项，并规划时间周期和关键节点，确保演练过程安全可控、贴合实际。',
            card_2: '需求确认',
            description_2: '准备演练所需物料，包括病毒样本、攻击工具、邮件话术及其他辅助工具，确保所有物料可有效支持演练实施并达到预期效果。',
            card_3: '演练项目设置',
            description_3: '根据客户需求配置演练参数，包括资产信息、邮箱信息等关键要素，确保场景匹配实际环境，优化细节，提升演练效果。',
            card_4: '应急响应小组建立',
            description_4: '组建应急响应小组，明确职责分工，与成员对接演练规范，确保高效协作完成演练任务。演练结束后，根据表现进行评分和反馈，助力能力提升。'
          },
          implementation_phase: {
            title: '实施阶段',
            card_1: '攻击触达',
            description_1: '使用定制化病毒样本，按照规划的攻击路径执行模拟攻击，完成潜伏行为及预设任务，精准还原真实威胁场景。',
            card_2: '感染扩散',
            description_2: '模拟病毒样本的勒索行为及大规模横向扩散，通过平台大屏实时展示客户网络架构的感染情况，全面呈现威胁影响及传播路径。',
            card_3: '应急响应处置',
            description_3: '应急响应小组依据预定应急方案开展响应与处置，快速阻断攻击，控制扩散范围。平台根据处置效率进行评分评估，提供量化反馈，助力优化应急能力。',
            card_4: '一键恢复/下线',
            description_4: '启动一键恢复和下线功能，高效还原所有被感染文件，彻底清除所有受感染设备中的病毒样本，确保环境全面恢复安全状态。'
          },
          summary_phase: {
            title: '总结阶段',
            card_1: '总结报告导出',
            description_1: '平台根据演练结果，综合各项评估指标生成整体评分报告，并提供详细反馈，助力客户优化防护策略与应急能力。',
            card_2: '人员意识培训',
            description_2: '结合本次演练，通过知行和天狩平台进行人员意识培训，将真实案例与仿真靶场相结合，提升员工的安全防范意识和实战应对能力。',
            card_3: '安全建设建议',
            description_3: '依据总结报告，提供针对性的网络安全优化改进方案，协助客户加强网络架构的安全部署，完善防护措施，并提供疑难问题的专业解答支持。'
          }
        },
        fourth: {
          tag: '数据说话',
          title: '值得信赖的安全演练平台',
          description: '帮助众多企业提升网络安全防护能力',
          tag_1: '专业的安全团队支持',
          tag_2: '7*24小时技术保障',
          tag_3: '定制化解决方案',
          card_1: '病毒家族特征',
          description_1: '全面的病毒特征库',
          card_2: '病毒样本数量',
          description_2: '持续更新病毒库',
          card_3: '服务企业数量',
          description_3: '涵盖各大重点行业',
          card_4: '真实仿真靶场',
          description_4: '多场景实战演练',
          start_button: '立即开始安全演练'
        },
        fifth: {
          tag: '关于我们',
          title: '思而听（山东）网络科技有限公司',
          description: '深耕应急响应领域，为您提供全方位的网络安全保障',
          card_1: '丰富的实战经验',
          description_1: '深耕应急响应领域多年，凭借丰富的实战经验和专业的技术团队，赢得了个人用户和大型企业的广泛信任。',
          card_2: '专业的解决方案',
          description_2: '专注于提供高效、安全、可靠的解决方案，特别是在防范勒索病毒领域具备独特的优势，提供量身定制的全流程服务。',
          card_3: '全面的安全保障',
          description_3: '从快速恢复被勒索软件侵害的数据到溯源分析再到全面的后门排查与系统安全加固，确保数据恢复，源头修复，并杜绝二次感染的风险。',
          card_4: '我们的使命',
          description_4: '致力于为客户提供卓越的应急响应支持和全面的安全保障，成为您值得信赖的网络安全伙伴。'
        }
      },
      login: {
        subtitle: '请登录以继续使用系统',
        login_1: '账号密码登录',
        login_2: '手机号登录',
        remember: '记住密码',
        code: '验证码',
        get_code: '获取验证码',
        retry_code: '{second}秒后重试',
        placeholder_code: '请输入验证码',
        logging_in: '登录中...',
        login: '登录',
        register: '没有账户？',
        click_to_register: '点击注册',
        error_1: '用户名或密码错误',
        error_2: '登录失败，请稍后重试',
        error_3: '登录失败，手机号或验证码错误',
        error_4: '请输入正确的手机号',
        error_5: '发送验证码失败，请稍后重试',
        success_1: '验证码已发送，请注意查收'
      },
      register: {
        subtitle: '创建新账户',
        register_1: '账号注册',
        register: '注册',
        registering: '注册中...',
        login: '已有账户！',
        click_to_login: '点击登录',
        confirm_password: '确认密码',
        invitation_code: '邀请码',
        placeholder_invitation_code: '请输入邀请码',
        company_name: '企业名称',
        placeholder_company_name: '请输入企业名称',
        placeholder_confirm_password: '请再次输入密码',
        error_1: '注册失败，手机号可能已被注册',
        error_2: '注册失败，请稍后重试',
        error_3: '两次输入的密码不一致',
        success_1: '注册成功，请登录',
      },
      header: {
        title: '勒索病毒模拟演练平台',
        logout: '退出登录',
        admin: '管理员',
        super: '超级管理员',
        visitors: '访客',
        visitor: '访客'
      },
      dashboard: {
        title: '数据看板',
        subtitle: '实时监控系统状态和关键指标',
        card: {
          total_number_of_drills: '总演练数',
          ongoing_exercise: '进行中演练',
          infected_assets: '已感染资产',
          infection_rate: '感染率',
          exercise_trends: '演练趋势',
          exercise_trends_subtitle: '过去7天演练数据统计',
          asset_distribution: '资产分布',
          asset_distribution_subtitle: '按资产组统计的分布情况',
          total_assets: '总资产',
          online_assets: '在线资产',
          infected: '已感染',
          recent_walkthroughs: '最近演练',
          recent_walkthroughs_subtitle: '显示最近5个演练的状态'
        }
      },
      users: {
        user: '用户',
        title: '用户管理',
        subtitle: '管理系统用户，支持添加、编辑和删除用户，以及分配用户角色和权限',
        placeholder_search: '搜索用户名/邮箱/手机',
        placeholder_username: '请输入用户名',
        description_username: '3-20个字符，支持字母、数字、下划线和横线',
        error_username_required: '用户名不能为空',
        error_username_minLength: '用户名至少3个字符',
        error_username_maxLength: '用户名最多20个字符',
        error_username_rules: '用户名只能包含字母、数字、下划线和横线',
        placeholder_email: '请输入邮箱',
        error_email_required: '邮箱不能为空',
        error_email_rules: '请输入正确的邮箱地址',
        placeholder_password: '请输入密码',
        placeholder_password_1: '留空则不修改密码',
        description_password: '密码长度需在6-20个字符之间',
        error_password_required: '密码不能为空',
        error_password_minLength: '密码至少6个字符',
        error_password_maxLength: '密码最多20个字符',
        placeholder_phone: '请输入电话',
        error_phone_required: '电话不能为空',
        error_phone_rules: '请输入正确的电话号码',
        placeholder_company: '请输入公司',
        error_company_required: '公司名称不能为空',
        error_company_rules: '公司名称不能超过50个字符',
        enable_account: '启用账号',
        delete_message: '确定要删除用户 {username} 吗？',
        message_1: '获取用户列表失败'
      },
      assets: {
        asset: '资产',
        title: '资产管理',
        subtitle: '管理所有终端设备和服务器资产，支持批量导入和手动添加',
        asset_group_management: '资产组管理',
        total_assets: '资产总数',
        total_groups: '资产组总数',
        terminal_equipment: '终端设备',
        server: '服务器',
        email: '电子邮件',
        placeholder_search: '搜索资产名称/IP/MAC地址/所属用户',
        group_title: '资产组管理',
        group_subtitle: '管理资产组分类,对资产进行分组管理和权限控制',
        asset_group: '资产组',
        asset_list: '资产列表',
        placeholder_search_group: '搜索资产组名称或描述',
        asset_group_name: '资产组名称',
        placeholder_asset_group_name: '请输入资产组名称，例如：开发服务器组',
        group_description: '资产组描述',
        placeholder_group_description: '请输入该资产组的用途、特点等描述信息',
        delete_asset_group_message: '确定要删除资产组 {name} 吗？',
        placeholder_asset: '搜索资产...',
        table_null: '该资产组下暂无资产',
        placeholder_type: '请选择类型',
        placeholder_asset_group: '请选择资产组',
        placeholder_select_search_group: '搜索资产组...',
        error_get_asset_group_list: '获取资产组列表失败',
        device_name: '设备名称',
        placeholder_device_name: '请输入设备名称',
        user: '使用者',
        placeholder_device_user: '请输入使用者',
        placeholder_ip_address: '请输入IP地址',
        ipv6_address: 'IPv6地址',
        placeholder_ipv6_address: '请输入IPV6地址',
        error_ip_address_rules: '请输入正确的 {name} 格式',
        placeholder_mac_address: '请输入MAC地址（格式: 00-50-56-C0-00-08）',
        operating_system: '操作系统',
        placeholder_operating_system: '请选择操作系统',
        win10_w: 'Windows 10 网信版',
        department: '所属部门',
        placeholder_department: '请输入所属部门',
        remarks: '备注',
        placeholder_remarks: '请输入备注信息',
        delete_message: '确定要删除资产 {name} 吗？',
        asset_detail: '资产详情',
        basic_information: '基础信息',
        network_information: '网络信息',
        email_information: '邮箱信息',
        message_1: '获取资产组列表失败',
        import: {
          title: '导入资产',
          description: '请使用标准模板导入资产，支持的文件类型：.xlsx, .xls, .csv',
          placeholder_asset_type: '请选择资产类型',
          placeholder_asset_group: '请选择资产组',
          importing_files: '导入文件',
          download_import_template: '下载导入模板',
          importing: '导入中...'
        }
      },
      virus: {
        virus: '病毒',
        title: '病毒管理',
        subtitle: '管理和配置病毒样本，支持勒索病毒、木马和蠕虫等类型',
        total_number_of_viruses: '总病毒数量',
        ransomware: '勒索病毒',
        trojan_virus: '木马病毒',
        worm_virus: '蠕虫病毒',

        virus_type: '病毒类型',
        placeholder_search: '搜索病毒名称或描述...',
        delete_message: '确定要删除病毒 {name} 吗？',
        delete_protected_error: '无法删除该病毒，因为它正在被演练项目使用。请先删除相关演练项目后再试。',
        delete_protected_error_with_exercises: '无法删除病毒 "{virusName}"，因为它正在被以下演练项目使用：{exercises}。请先删除这些演练项目后再试。',
        message_1: '获取病毒列表失败',
        message_2: '获取病毒详情失败',
        message_3: '获取病毒家族列表失败',
        form: {
          virus_information: '病毒信息',


          infection_information_configuration: '感染信息配置',
          virus_name: '病毒名称',
          placeholder_virus_name: '请输入病毒名称',
          reference_family: '参考家族',
          placeholder_reference_family: '请输入病毒参考家族',
          type_of_infection: '感染类型',
          placeholder_type_of_infection: '请选择感染类型',
          encrypted_suffix: '加密后缀',
          placeholder_encrypted_suffix: '请输入加密后文件的后缀名，如：.locked、.encrypted、.virus',
          upload_encryptor: '上传加密器',


          encryptor: '加密器',
          placeholder_encryptor: '请上传加密器',
          encrypto_key: '对称加密密钥',
          placeholder_encrypto_key: '请输入对称加密密钥，长度16或32位',
          encrypto_iv: '对称加密向量',
          placeholder_encrypto_iv: '请输入对称加密向量，长度16或32位',
          target_dir: '加密路径（若有多个路径，请使用逗号分割）',
          placeholder_target_dir: '请输入加密路径',
          encrypted_file_types: '需要加密的文件类型',
          placeholder_encrypted_file_types: '请输入文件类型，如：txt,doc,jpg（用逗号分隔）',

          custom_suffix: '加密后缀',
          ransom_note_name: '勒索信文件名',
          placeholder_ransom_note_name: '请输入勒索信文件名',
          wallpaper: '壁纸',
          upload_wallpaper: '上传壁纸',
          click_to_change_wallpaper: '点击更换壁纸',
          placeholder_wallpaper: '请上传壁纸',
          ransom_note_content: '勒索信内容',
          placeholder_ransom_note_content: '请输入勒索信内容...'
        }
      },
      strategy: {
        strategy: '策略',
        title: '发送策略',
        subtitle: '配置钓鱼邮件的发送策略,包括发送时间、频率等',
        placeholder_search: '搜索策略名称',
        delete_message: '确定要删除策略 {name} 吗？',
        form: {
          p1: '创建策略前需要进行邮件测试',
          p2: '请填写邮件配置信息并进行测试,测试成功后才能创建策略',
          placeholder_strategy_name: '请输入策略名称',
          placeholder_interface_type: '请选择接口类型',
          placeholder_sender_email: '请输入发件人邮箱',
          placeholder_email_server_address: '请输入邮箱服务器地址',
          server_port: '服务器端口',
          placeholder_server_port: '请输入邮箱服务器端口',
          email_server_account: '邮箱服务器用户名',
          placeholder_email_server_account: '请输入邮箱服务器用户名',
          email_server_password: '邮箱服务器密码',
          placeholder_email_server_password: '请输入邮箱服务器密码',
          send_test_email: '发送测试邮件',
          send_test_email_desc: '请确保邮件配置正确,测试成功后才能创建策略',
          target_email_address: '目标邮件地址',
          placeholder_target_email_address: '请输入测试邮件地址',
          custom_email_header: '自定义邮件头',
          test_success: '（测试成功）',
          sending: '发送中...',
          test_success_message: '测试邮件发送成功',
        }
      },
      template: {
        templates: '模板',
        title: '邮件模版',
        subtitle: '管理钓鱼邮件模版,支持自定义邮件内容和样式',
        placeholder_search: '搜索模板名称',
        delete_message: '确定要删除模板 {name} 吗？',
        form: {
          placeholder_template_name: '请输入模板名称',
          placeholder_email_subject: '请输入邮件标题',
          whether_to_track_users: '是否跟踪用户',
          importing_a_template: '导入模板',
          add_attachments: '添加附件',
          virus: '选择病毒',
          placeholder_virus: '请选择病毒',
          whether_to_compress_attachments: '是否压缩附件',
          compressed_file_name: '压缩包名称',
          placeholder_compressed_file_name: '请输入压缩包名称',
          compressed_package_password: '压缩包密码',
          placeholder_compressed_package_password: '请输入压缩包密码'
        }
      },
      page: {
        page: '页面',
        title: '钓鱼页面',
        subtitle: '管理钓鱼页面模版,支持自定义页面内容和样式',
        placeholder_search: '搜索页面名称',
        delete_message: '确定要删除页面 {name} 吗？',
        form: {
          placeholder_page_name: '请输入页面名称',
          redirect_address: '重定向地址',
          placeholder_redirect_address: '请输入重定向地址',
          clone_a_website: '克隆网站',
          placeholder_url: '请输入Url',
          placeholder_content: '请填写页面内容',
        }
      },
      task: {
        task: '任务',
        title: '邮件任务',
        subtitle: '管理钓鱼邮件发送任务,查看任务执行状态和结果',
        placeholder_search: '搜索任务名称',
        delete_message: '确定要删除任务 {name} 吗？',
        form: {
          placeholder_task_name: '请输入任务名称',
          placeholder_email_templates: '请选择邮件模板',
          placeholder_phishing_page: '请选择钓鱼页面',
          placeholder_sending_strategy: '请选择发送策略',
          phishing_link: '钓鱼链接',
          placeholder_phishing_link: '请输入钓鱼链接'
        }
      },
      negotiation: {
        negotiation: '谈判',
        title: '谈判管理',
        subtitle: '管理谈判策略和规则配置,支持添加、编辑和删除配置项',
        placeholder_search: '搜索配置名称/描述',
        delete_message: '确定要删除谈判 {name} 吗？',
        copied_to_clipboard: 'ID已复制到剪贴板',
        copy_failure: '复制失败',
        recent_sessions: '最近会话',
        automatic_reply: '自动回复',
        manual_reply: '手动回复',
        start_negotiations: '开始谈判',
        no_introduction_yet: '暂无简介',
        message_1: '获取配置列表失败',
        form: {
          id_page: 'ID页',
          placeholder_id_page: '请输入ID',
          home_page: '首页',
          platform_name: '平台名称',
          placeholder_platform_name: '请输入平台名称',
          company_name: '公司名称',
          placeholder_company_name: '请输入公司名称',
          official_website_link: '官网链接',
          placeholder_official_website_link: '请输入官网链接',
          number_of_home_page_impressions: '首页展示数',
          placeholder_number_of_home_page_impressions: '请输入首页展示数',
          company_introduction: '公司介绍',
          placeholder_company_introduction: '请输入公司介绍',
          virus_family_details_page: '病毒家族详情页',
          logo: '公司Logo',
          logo_required: '公司Logo（必填）',
          company_valuation: '公司估值',
          placeholder_company_valuation: '请输入公司估值',
          amount_of_stolen_data: '窃取数据量',
          placeholder_amount_of_stolen_data: '请输入窃取数据量',
          negotiation_page: '谈判页',
          ransom_btc: '赎金（BTC）',
          placeholder_ransom_btc: '请输入公司估值（BTC）',
          ransom_usdt: '赎金（USDT）',
          placeholder_ransom_usdt: '请输入公司估值（USDT）',
          deadline: '截止时间',
          placeholder_deadline: '请输入截止时间',
          select_time: '选择时间',
          auto_chat: '自动聊天',
          auto_reply: '开启后将自动回复用户消息',
          btc_address: 'BTC地址',
          placeholder_btc_address: '请输入BTC地址',
          usdt_address: 'USDT地址',
          placeholder_usdt_address: '请输入USDT地址'
        }
      },
      family: {
        family: '病毒家族',
        title: '病毒家族管理',
        subtitle: '管理病毒家族信息,支持添加、编辑和删除病毒家族信息',
        placeholder_search: '搜索病毒家族名称',
        delete_message: '确定要删除病毒家族 {name} 吗？',
        message_1: '获取病毒家族列表失败',
        form: {
          basic_information: '基本信息',
          placeholder_family_name: '请输入病毒家族名称',
          placeholder_first_appearance_time: '请输入首次出现时间',
          placeholder_encryption_algorithm: '请选择加密算法',
          placeholder_ransom_method: '请选择勒索方式',
          single_encryption: '单重加密',
          double_encryption: '双重加密',
          triple_encryption: '三重加密',
          quadruple_encryption: '四重加密',
          placeholder_infection_type: '请选择感染类型',
          placeholder_wallet_address: '请输入钱包地址',
          placeholder_public_decryptor: '请选择公开解密器',
          encryption_suffix: '加密后缀',
          placeholder_encryption_suffix: '请输入加密后缀',
          family_profile: '病毒家族简介',
          placeholder_family_profile: '请输入病毒家族简介',
          attack_route: '攻击路线',
          placeholder_attack_route: '请输入攻击路线',
          ransom_note: '勒索信',
          add_ransom_note: '添加勒索信',
          ransom_note_name: '勒索信名称',
          placeholder_ransom_note_name: '请输入勒索信名称',
          text_content: '文本内容',
          placeholder_text_content: '请输入文本内容',
          ransomware_address: '勒索地址',
          add_ransomware_address: '添加勒索地址',
          address_option: '地址选项',
          placeholder_address_option: '请选择地址选项',
          common_tools: '常用工具',
          add_tool: '添加工具',
          virus_sample: '病毒样本',
          add_sample: '添加样本',
          negotiation_record: '谈判记录',
          add_record: '添加记录',
          victim_information: '受害者信息',
          add_information: '添加信息',
          ioc_information: 'IOC信息',
          delete_img: '删除图片',
          table: {
            tool_name: '工具名称',
            tool_type: '工具类型',
            tool_description: '工具介绍',
            attachment: '附件',
            sample_name: '样本名称',
            sample_file: '样本文件',
            date: '日期',
            initial_amount: '初始金额',
            final_delivery: '最终交付金额',
            whether_paid: '是否支付',
            chat_record: '聊天记录',
            victim: '受害者',
            official_website: '官网',
            location: '所在地区',
            introduction: '简介'
          },
          placeholder_tool_name: '请输入工具名称',
          placeholder_select: '请选择',
          ransomware: '勒索软件',
          intranet_scanning: '内网扫描',
          placeholder_tool_description: '请输入工具介绍',
          placeholder_sample_name: '请输入样本名称',
          placeholder_date: '请选择日期',
          placeholder_initial_amount: '请输入初始金额',
          placeholder_final_delivery: '请输入最终交付金额',
          placeholder_victim: '请输入受害者名称',
          placeholder_official_website: '请输入官网',
          placeholder_location: '请输入所在地区',
          placeholder_introduction: '请输入简介'
        },
        detail: {
          title: '病毒家族详情',
          subtitle: '查看病毒家族的详细信息',
          virus_family_identification: '病毒家族标识',
          address: '地址',
          download_tool: '下载工具',
          no_file: '暂无文件',
          related_documents: '相关文件',
          visit: '访问'
        }
      },
      exercise: {
        exercise: '演练',
        title: '演练管理',
        subtitle: '管理和配置演练任务，支持多种演练场景',
        placeholder_search: '搜索演练名称',
        delete_message: '确定要删除演练 {name} 吗？',
        not_started: '未开始',
        in_progress: '进行中',
        completed: '已完成',
        completion_progress: '完成进度',
        exercise_report: '演练报告',
        infection_situation: '感染情况',
        data_screen: '数据大屏',
        phishing: '钓鱼',
        message_1: '获取演练列表失败',
        form: {
          exercise_name: '演练名称',
          placeholder_exercise_name: '请输入演练名称',
          fishing_mission: '钓鱼任务',
          placeholder_fishing_mission: '请选择钓鱼任务',
          virus_configuration: '病毒配置',
          placeholder_virus_configuration: '请选择病毒配置',
          negotiation_configuration: '谈判配置',
          placeholder_negotiation_configuration: '请选择谈判配置',
          email_information: '邮箱信息',
          placeholder_email_information: '请选择邮箱信息（多选）',
          asset_information: '资产信息',
          placeholder_asset_information: '请选择资产信息（多选）',
          start_time: '开始时间',
          duration: '持续时间',
          end_time: '结束时间',
          not_set: '未设置'
        },
        detail: {
          exercise_details: '演练详情',
          h1: '钓鱼任务信息',
          h2: '病毒配置信息',
          h3: '谈判配置信息',
          h4: '邮箱资产信息',
          phishing_links: '钓鱼链接',
          encryptor_name: '加密器名称',
          encryptor_download: '加密器下载',
          attack_configuration: '攻击配置',
          infection_configuration: '感染配置',
          ip_pool: 'IP池',
          function_configuration: '功能配置',
          negotiation_function: '谈判功能',
          viral_evolution: '病毒进化',
          ransomware_information: '勒索信息',
          time_information: '时间信息'
        },
        report: {
          title: '网络安全演练报告',
          exercise_time: '演练时间',
          exercise_unit: '演练单位',
          exercise_status: '演练状态',
          virus_name: '病毒名称',
          h1: '演练结果统计',
          total_number_of_targeted_devices: '目标设备总数',
          number_of_infected_devices: '已感染设备数',
          phishing_email_click_rate: '钓鱼邮件点击率',
          total_number_of_sent_emails: '发送总数',
          number_of_clicks: '已点击数',
          h2: '演练时间轴',
          h3: '目标资产信息',
          h4: '邮件发送记录',
          exercise_begins: '演练开始',
          send_email: '发送邮件',
          click_email: '点击邮件',
          submit_data: '提交数据',
          not_clicked: '未点击',
          clicked: '已点击',
          export: '导出报告',
          export_report: '导出报告',
          export_format: '导出格式',
          template: '报告模板',
          default_template: '默认模板',
          export_options: '导出选项',
          include_charts: '包含图表',
          include_details: '包含详细信息',
          include_capture_data: '包含捕获数据',
          preview: '预览',
          exporting: '导出中...'
        }
      },
      infection: {
        title: '感染情况',
        subtitle: '查看和管理所有病毒感染记录',
        placeholder_search: '搜索设备ID',
        delete_message: '确定要删除感染记录 {name} 吗？',
        not_infected: '未感染',
        infected: '已感染',
        infection_status: '感染状态',
        system_version: '系统版本',
        location: '位置',
        first_infection: '首次感染',
        last_infection: '最后感染',
        device_infection_details: '设备感染详情',
        device_id: '设备ID',
        hostname: '主机名',
        program_path: '程序路径',
        remote_operation: '远程操作',

        execute: '执行',
        executing: '执行中...',
        check_online_status: '检查在线状态',
        change_wallpaper: '更改壁纸',
        modify_target_device_wallpaper: '修改目标设备壁纸',
        restore_wallpaper: '恢复壁纸',
        restore_device_original_wallpaper: '还原设备原始壁纸',
        command_execution: '命令执行',
        turn_off_antivirus_software: '关闭杀毒软件',
        maintaining_authority: '权限维持',
        maintain_device_authority: '维持病毒权限',
        start_encryption: '开始加密',
        offline_all: '一键下线所有',
        offline_all_success: '所有设备已下线',
        offline_all_failed: '下线操作失败',
        encrypt_target_device_files: '加密目标设备文件',
        start_decryption: '开始解密',
        decrypt_target_device_files: '解密目标设备文件',
        destroy_virus: '销毁病毒',
        remove_virus_from_target_device: '从目标设备中移除病毒',
        lateral_scan: '横向爆破',
        perform_lateral_scan: '执行横向爆破扫描',
        target_ip: '目标IP',
        enter_target_ip: '请输入目标IP地址',
        target_port: '目标端口',
        enter_target_port: '请输入目标端口',
        start_scan: '开始扫描',
        scan_again: '再次扫描',
        scan_result: '扫描结果',
        scan_started: '扫描已开始',
        scan_failed: '扫描失败',
        scan_task_submitted: '扫描任务已提交，任务ID: {taskId}',
        invalid_ip_format: 'IP地址格式不正确',
        invalid_port_format: '端口号必须在1-65535之间',
        unknown_command: '未知命令',
        command_execution_history: '命令执行历史',
        execution_time: '执行时间',
        command_type: '命令类型',
        execution_status: '执行状态',
        infection_history: '感染历史记录',
        export_data: '导出数据',
        infection_time: '感染时间',
        message_1: '设备当前不在线，无法执行命令',
        message_2: '获取命令历史失败',
        message_3: '设备不在线',
        detail: {
          title: '命令执行详情',
          execution_time: '执行时间',
          execution_success: '执行成功',
          execution_failed: '执行失败',
          command_parameters: '命令参数',
          execute_command: '执行命令',
          device_id: '设备ID',
          execution_response: '执行响应',
          status_code: '状态码',
          status: '状态',
          response_message: '响应消息',
          response_data: '响应数据',
          no_parameters: '无参数'
        }
      },
      notification: {
        title: '消息通知',
        subtitle: '查看和管理您的所有通知',
        mark_read: '标记已读',
        mark_all_as_read: '全部标记为已读',
        news: '新消息',
        p1: '当有新的通知时会显示在这里',
        message_1: '已标记为已读',
        message_2: '已全部标记为已读',
        message_3: '操作失败',
        message_4: '获取通知列表失败'
      },
      profile: {
        title: '个人信息',
        update_avatar: '更换头像',
        role: '角色',
        last_login: '最后登录',
        change_password: '修改密码',
        current_password: '当前密码',
        new_password: '新密码',
        weak: '弱',
        medium: '中等',
        powerful: '强',
        confirm_new_password: '确认新密码',
        rules_new_required: '请输入新密码',
        rules_current_required: '请输入当前密码',
        rules_new_min: '密码长度不能少于8位',
        rules_confirm_required: '请再次输入新密码',
        under_modification: '正在修改...',
        message_1: '获取用户信息失败',
        rules_new_number: '密码必须包含数字',
        rules_new_letter: '密码必须包含字母',
        rules_new_Inconsistency: '两次输入的密码不一致'
      },
      settings: {
        title: '系统设置',
        basic_settings: '基本设置',
        system_name: '系统名称',
        system_logo: '系统Logo',
        upload_logo: '上传Logo',
        copyright_information: '版权信息',
        system_time_zone: '系统时区',
        utc8_beijing_time: 'UTC+8 (北京时间)',
        utc0_greenwich_mean_time: 'UTC+0 (格林尼治标准时间)',
        date_format: '日期格式',
        security_settings: '安全设置',
        password_policy: '密码策略',
        minimum_password_length: '最小密码长度',
        must_contain_uppercase_letters: '必须包含大写字母',
        must_contain_numbers: '必须包含数字',
        must_contain_special_characters: '必须包含特殊字符',
        session_timeout_minutes: '会话超时时间（分钟）',
        maximum_number_of_login_attempts: '最大登录尝试次数',
        save_settings: '保存设置',
        message_1: '获取系统设置失败'
      },
      table: {
        exercise_name: '演练名称',
        status: '状态',
        start_time: '开始时间',
        end_time: '结束时间',
        action: '操作',
        details: '查看详情',
        avatar: '头像',
        username: '用户名',
        email: '邮箱',
        company: '公司',
        password: '密码',
        phone: '电话',
        create_time: '创建时间',
        asset_name: '资产名称',
        asset_type: '资产类型',
        ip_address: 'IP地址',
        mac_address: 'MAC地址',
        user_group: '所属用户',
        group: '所属组',
        asset: '资产',
        asset_group: '资产组',
        name: '名称',
        description: '描述',
        number_of_assets: '资产数量',
        type: '类型',
        strategy_name: '策略名称',
        interface_type: '接口类型',
        sender_email: '发件人邮箱',
        email_server_address: '邮箱服务器地址',
        whether_to_ignore_certificate_errors: '是否忽略证书错误',
        email_header: '邮件头',
        content: '内容',
        template_name: '模板名称',
        email_subject: '邮件主题',
        file_name: '文件名称',
        file_size: '文件大小',
        page_name: '页面名称',
        capture_submission_data: '捕获提交数据',
        capture_password: '捕获密码',
        task_name: '任务名称',
        email_templates: '邮件模版',
        phishing_page: '钓鱼页面',
        sending_strategy: '发送策略',
        family_name: '病毒家族名称',
        family_logo: '病毒家族logo',
        first_appearance_time: '首次出现时间',
        encryption_algorithm: '加密算法',
        ransom_method: '勒索方式',
        infection_type: '感染类型',
        wallet_address: '钱包地址',
        public_decryptor: '公开解密器',
        update_time: '更新时间',
        recipient: '收件人',
        send_time: '发送时间',
        click_status: '点击状态',
        template_type: '模板类型',
        template_type_system: '系统模板',
        template_type_custom: '自定义模板',
      }
    },
    en: {
      menu: {
        dashboard: 'Dashboard',
        users: 'Users',
        assets: 'Assets',
        config: 'Exercise Config',
        virus: 'Virus',
        phishing: 'Phishing',
        negotiation: 'Negotiation',
        exercise: 'Exercise',
        family: 'Virus Family'
      },
      unit: {
        days: 'd',
        hours: 'h',
        minutes: 'min',
        seconds: 's'
      },
      all: {
        reset: 'Reset',
        search: 'Search',
        close: 'Close',
        enabled: 'Enabled',
        disabled: 'Disabled',
        online: 'Online',
        offline: 'Offline',
        create: 'Create',
        edit: 'Edit',
        copy: 'Copy',
        delete: 'Delete',
        cancel: 'Cancel',
        save: 'Save',
        confirm: 'Confirm',
        create_success: 'Create successful!',
        create_failed: 'Create failed!',
        update_success: 'Update successful!',
        update_failed: 'Update failed!',
        delete_success: 'Delete successful!',
        delete_failed: 'Delete failed!',
        upload_success: 'Upload successful!',
        upload_failed: 'Upload failed!',
        import_success: 'Import successful! ',
        import_failed: 'Import failed! ',
        prev: 'Prev',
        next: 'Next',
        total_1: 'Showing',
        total_2: 'to',
        total_3: ', a total of',
        total_4: 'records',
        import_multiple: 'Import multiple',
        administrator: 'Administrator',
        loading: 'Loading...',
        processing: 'Processing...',
        no_data: 'No data',
        field_required: 'This field is required',
        other: 'Other',
        back: 'Back',
        current_file: 'Current file',
        click_upload: 'Click Upload',
        uploaded: 'Uploaded',
        or_drag_and_drop_files: ' Or drag and drop files here',
        support_formats: 'Supported {suffix} formats',
        restriction_format: 'Restriction format',
        limit_size: 'Limit size',
        no_restrictions: 'No restrictions',
        upload_type_message: 'Incorrect file type',
        upload_size_message: 'File size exceeds limit',
        send: 'Send',
        rich_text: 'Rich Text',
        source_code: 'Source Code',
        preview: 'Preview',
        import: 'Import',
        export: 'Export',
        clone: 'Clone',
        random_generation: 'Random',
        select_time: 'Select time',
        select_date: 'Select date',
        form_reset: 'The form has been reset',
        yes: 'Yes',
        no: 'No',
        download: 'Download',
        saving: 'Saving...',
        need_help: 'Need help?',
        view_documentation: 'View usage documentation',
        success: 'Success',
        failed: 'Failed',
        today: 'Today',
        ago: 'ago',
        delete_all: 'Batch delete',
        delete_all_message: 'Are you sure you want to delete all selected items?',
        select_one: 'Please select at least one item! ',
      },
      home: {
        sierting: 'Think and Listen',
        home: 'Home',
        features: 'Features',
        services: 'Service Content',
        stats: 'Statistics',
        about: 'About Us',
        tag: 'New generation network security platform',
        start_now: 'Start now',
        learn_more: 'Learn more',
        company: 'Think and Listen Network Technology Co., Ltd.',
        friendly_links: 'Friendly Links',
        contact_us: 'Contact Us',
        first: {
          title_1: 'Think and Listen Anti-Ransomware',
          title_2: 'Simulation and Exercise Platform',
          description: 'Accurately simulate ransomware scenarios, evaluate protection capabilities, and integrate EDR testing, emergency assessment and security training, ultimately optimize protection strategies, strengthen corporate security lines in multiple dimensions, and effectively respond to ransomware threats.',
          card_1: '7*24 hours security monitoring',
          description_1: '24/7 uninterrupted monitoring, timely detection and response to security threats',
          card_2: '24/7 professional after-sales service',
          description_2: 'Professional team provides all-round technical support and problem solving',
          card_3: 'New product technical documentation',
          description_3: 'Detailed product usage guide and technical description documents',
          card_4: 'Technical expert service',
          description_4: 'Senior technical experts provide professional consultation and solutions',
        },
        second: {
          tag: 'Core Advantages',
          title: 'Powerful Platform Advantages',
          description: 'Comprehensive virus database, professional analysis capabilities, and efficient response mechanisms',
          card_1: 'Virus Family Database',
          description_1: 'Covering 200+ mainstream ransomware virus families, providing professional analysis reports such as IOC features, attack methods, and encryption algorithms. In-depth analysis of virus behavior patterns to provide enterprises with precise protection strategies. ',
          card_2: 'Multi-level attack chain analysis',
          description_2: "Based on the MITRE ATT&CK framework, a complete attack chain map is constructed. Restore the attacker's perspective, deeply analyze attack techniques and tactics, and help enterprises discover protection shortcomings. ",
          card_3: 'Quick response and recovery',
          description_3: 'Provide quick response capabilities such as one-click isolation, process termination, and sample removal. Cooperate with professional emergency plans to achieve minute-level threat disposal and minimize the impact of attacks. ',
          card_4: 'Customized samples',
          description_4: 'Supports customization of ransomware samples of different variants, compatible with mainstream EDR/XDR products. Through real attack scenarios, comprehensively evaluate the detection and protection capabilities of security products. ',
          card_5: 'Deep integration of threat intelligence',
          description_5: 'Integrate the latest global ransomware threat intelligence, and update attack tactics and technical features in real time. Predict attack trends and help companies build a forward-looking protection system. ',
          card_6: 'Drill report scoring',
          description_6: 'Based on a multi-dimensional indicator system, quantitatively evaluate the protection effect. Generate professional improvement suggestion reports to help companies continuously improve their security capabilities from the technical and management levels. '
        },
        third: {
          tag: 'Service process',
          title: 'Professional drill service system',
          description: 'Provide full-process professional service support from preparation to implementation to summary',
          preparation_phase: {
            title: 'Preparation phase',
            card_1: 'Customization of drill plan',
            description_1: "According to the customer's on- site environment and actual needs, formulate the drill process, clarify the participants and responsibilities, set the drill goals, list the precautions, and plan the time period and key nodes to ensure that the drill process is safe, controllable and practical. ",
            card_2: 'Requirement confirmation',
            description_2: 'Prepare the materials required for the drill, including virus samples, attack tools, email scripts and other auxiliary tools, to ensure that all materials can effectively support the implementation of the drill and achieve the expected results. ',
            card_3: 'Drill project settings',
            description_3: 'Configure drill parameters according to customer needs, including key elements such as asset information and mailbox information, to ensure that the scenario matches the actual environment, optimize details, and improve the drill effect. ',
            card_4: 'Emergency response team establishment',
            description_4: 'Establish an emergency response team, clarify the division of responsibilities, and coordinate with members to ensure efficient collaboration to complete the exercise tasks. After the exercise, score and feedback will be given based on performance to help improve capabilities. '
          },
          implementation_phase: {
            title: 'Implementation phase',
            card_1: 'Attack reach',
            description_1: 'Use customized virus samples to perform simulated attacks according to the planned attack path, complete latent behaviors and preset tasks, and accurately restore real threat scenarios. ',
            card_2: 'Infection and spread',
            description_2: "Simulate the ransomware behavior and large-scale horizontal spread of virus samples, and display the infection status of the customer's network architecture in real time through the platform's large screen, and fully present the threat impact and propagation path. ",
            card_3: 'Emergency response and disposal',
            description_3: 'The emergency response team responds and disposes according to the predetermined emergency plan, quickly blocks the attack, and controls the spread range. The platform evaluates and scores based on the efficiency of the disposal, provides quantitative feedback, and helps optimize emergency response capabilities. ',
            card_4: 'One-click recovery/offline',
            description_4: 'Start the one-click recovery and offline functions, efficiently restore all infected files, and completely remove virus samples from all infected devices to ensure that the environment is fully restored to a safe state. '
          },
          summary_phase: {
            title: 'Summary phase',
            card_1: 'Summary report export',
            description_1: 'Based on the results of the exercise, the platform generates an overall scoring report based on various evaluation indicators and provides detailed feedback to help customers optimize their protection strategies and emergency response capabilities. ',
            card_2: 'Staff awareness training',
            description_2: "In conjunction with this exercise, the Zhixing and Tianshu platforms will conduct staff awareness training, combining real cases with simulated shooting ranges to enhance employees' safety awareness and actual combat response capabilities. ",
            card_3: 'Security Construction Suggestions',
            description_3: 'Based on the summary report, provide targeted network security optimization and improvement solutions to assist customers in strengthening the security deployment of network architecture, improving protection measures, and providing professional answers to difficult questions. '
          }
        },
        fourth: {
          tag: 'Data speaks',
          title: 'Trustworthy security drill platform',
          description: 'Helping many companies improve their network security protection capabilities',
          tag_1: 'Professional security team support',
          tag_2: '7*24 hours technical support',
          tag_3: 'Customized solutions',
          card_1: 'Virus family characteristics',
          description_1: 'Comprehensive virus feature library',
          card_2: 'Number of virus samples',
          description_2: 'Continuously update the virus library',
          card_3: 'Number of service companies',
          description_3: 'Covering major key industries',
          card_4: 'Realistic simulation range',
          description_4: 'Multi-scenario actual combat drill',
          start_button: 'Start security drill immediately'
        },
        fifth: {
          tag: 'About us',
          title: 'Think and Listen (Shandong) Network Technology Co., Ltd.',
          description: 'Deeply cultivating the field of emergency response, providing you with comprehensive network security protection',
          card_1: 'Rich practical experience',
          description_1: 'Deeply cultivating the field of emergency response for many years, with rich practical experience and professional technical team, it has won the widespread trust of individual users and large enterprises. ',
          card_2: 'Professional solutions',
          description_2: 'Focus on providing efficient, safe and reliable solutions, especially in the field of preventing ransomware viruses, with unique advantages, providing tailor-made full-process services. ',
          card_3: 'Comprehensive security protection',
          description_3: 'From rapid recovery of data compromised by ransomware to source tracing analysis to comprehensive backdoor investigation and system security reinforcement, ensure data recovery, source repair, and eliminate the risk of secondary infection. ',
          card_4: 'Our mission',
          description_4: 'Committed to providing customers with excellent emergency response support and comprehensive security protection, and become your trusted network security partner. '
        }
      },
      header: {
        title: 'Ransomware Simulation Drill Platform',
        logout: 'Logout',
        admin: 'Administrator',
        super: 'Super Administrator',
        visitors: 'Visitors',
        visitor: 'Visitor'
      },
      login: {
        subtitle: 'Please log in to continue using the system',
        login_1: 'Login with account',
        login_2: 'Login with phone',
        remember: 'Remember password',
        code: 'Verification code',
        get_code: 'Get code',
        retry_code: 'Retry after {second} seconds',
        placeholder_code: 'Please enter verification code',
        login: 'Login',
        logging_in: 'Logging in...',
        register: "Don't have an account ?",
        click_to_register: 'Sign up',
        error_1: 'Incorrect username or password',
        error_2: 'Login failed, please try again later',
        error_3: 'Login failed, wrong mobile number or verification code',
        error_4: 'Please enter a correct mobile number',
        error_5: 'Failed to send verification code, please try again later',
        success_1: 'Verification code has been sent, please check it'
      },
      register: {
        subtitle: 'Create a new account',
        register_1: 'Register',
        register: 'Register',
        registering: 'Registering...',
        login: 'Already have an account ?',
        click_to_login: 'Sign in',
        confirm_password: 'Confirm Password',
        placeholder_confirm_password: 'Please enter your password again',
        invitation_code: 'Invitation code',
        placeholder_invitation_code: 'Please enter your invitation code',
        company_name: 'Company name',
        placeholder_company_name: 'Please enter your company name',
        error_1: 'Registration failed, the phone number may have been registered',
        error_2: 'Registration failed, please try again later',
        error_3: 'The passwords you entered twice do not match',
        success_1: 'Registration successful, please log in',
      },
      dashboard: {
        title: 'Dashboard',
        subtitle: 'Real-time monitoring of system status and key indicators',
        card: {
          total_number_of_drills: 'Total number of drills',
          ongoing_exercise: 'Ongoing exercise',
          infected_assets: 'Infected assets',
          infection_rate: 'Infection rate',
          exercise_trends: 'Exercise trends',
          exercise_trends_subtitle: 'Statistics of the last 7 days of drill data',
          asset_distribution: 'Asset distribution',
          asset_distribution_subtitle: 'Distribution of assets by asset group',
          total_assets: 'Total assets',
          online_assets: 'Online assets',
          infected: 'Infected',
          recent_walkthroughs: 'Recent walkthroughs',
          recent_walkthroughs_subtitle: 'Display the status of the last 5 drills'
        }
      },
      users: {
        user: ' Users',
        title: 'Users',
        subtitle: 'Manage system users, support adding, editing, and deleting users, as well as assigning user roles and permissions',
        placeholder_search: 'Search username/email/phone number',
        placeholder_username: 'Please enter username',
        description_username: '3-20 characters, supporting letters, numbers, underscores and hyphens',
        error_username_required: 'Username cannot be empty',
        error_username_minLength: 'Username must be at least 3 characters',
        error_username_maxLength: 'Username can be up to 20 characters',
        error_username_rules: 'Usernames can only contain letters, numbers, underscores, and hyphens',
        placeholder_email: 'Please enter email',
        error_email_required: 'Email address cannot be empty',
        error_email_rules: 'Please enter a valid email address',
        placeholder_password: 'Please enter password',
        placeholder_password_1: 'Leave blank to not change the password',
        description_password: 'The password must be between 6 and 20 characters long.',
        error_password_required: 'Password cannot be empty',
        error_password_minLength: 'Password must be at least 6 characters',
        error_password_maxLength: 'Password maximum 20 characters',
        placeholder_phone: 'Please enter phone number',
        error_phone_required: 'Phone cannot be empty',
        error_phone_rules: 'Please enter a valid phone number',
        placeholder_company: 'Please enter company name',
        error_company_required: 'Company name cannot be empty',
        error_company_rules: 'Company name cannot exceed 50 characters',
        enable_account: 'Enable account',
        delete_message: 'Are you sure you want to delete user {username}?',
        message_1: 'Failed to obtain user list'
      },
      assets: {
        asset: ' Assets',
        title: 'Assets',
        subtitle: 'Manage all terminal devices and server assets, support batch import and manual addition',
        asset_group_management: 'Asset Group Management',
        total_assets: 'Total assets',
        total_groups: 'Total number of asset groups',
        terminal_equipment: 'Terminal equipment',
        server: 'Server',
        email: 'Email',
        placeholder_search: 'Search asset name/IP/MAC address/username',
        group_title: 'Asset Group Management',
        group_subtitle: 'Manage asset group classification, group management and authority control of assets',
        asset_group: ' Asset Group',
        asset_list: 'Asset List',
        placeholder_search_group: 'Search asset group name',
        asset_group_name: 'Asset Group Name',
        placeholder_asset_group_name: 'Please enter an asset group name, for example: Development Server Group',
        group_description: 'Asset Group Description',
        placeholder_group_description: 'Please enter the purpose, characteristics and other descriptive information of the asset group',
        delete_asset_group_message: 'Are you sure you want to delete asset group {name}?',
        placeholder_asset: 'Search asset...',
        table_null: 'There are currently no assets under this asset group.',
        placeholder_type: 'Please enter the asset type',
        placeholder_asset_group: 'Please select an asset group',
        placeholder_select_search_group: 'Select asset group...',
        error_get_asset_group_list: 'Failed to obtain the asset group list',
        device_name: 'Device Name',
        placeholder_device_name: 'Please enter the device name',
        user: 'User',
        placeholder_device_user: 'Please enter the user name',
        placeholder_ip_address: 'Please enter the IP address',
        ipv6_address: 'IPv6 address',
        error_ip_address_rules: 'Please enter the correct {name} format',
        placeholder_ipv6_address: 'Please enter the IPv6 address',
        placeholder_mac_address: 'Please enter the MAC address (Format: 00-50-56-C0-00-08)',
        operating_system: 'Operating System',
        placeholder_operating_system: 'Please enter the operating system',
        win10_w: 'Windows 10 Netcom Edition',
        department: 'Department',
        placeholder_department: 'Please enter the department',
        remarks: 'Remarks',
        placeholder_remarks: 'Please enter remarks',
        delete_message: 'Are you sure you want to delete asset {name}?',
        asset_detail: 'Asset Details',
        basic_information: 'Basic Information',
        network_information: 'Network Information',
        email_information: 'Email Information',
        message_1: 'Failed to obtain the asset group list',
        import: {
          title: 'Import assets',
          description: 'Please use the standard template to import assets. Supported file types: .xlsx, .xls, .csv',
          placeholder_asset_type: 'Please select asset type',
          placeholder_asset_group: 'Please select asset group',
          importing_files: 'Import files',
          download_import_template: 'Download import template',
          importing: 'Importing...'
        }
      },
      virus: {
        virus: ' Virus',
        title: 'Virus Manage',
        subtitle: 'Manage and configure virus samples, including ransomware, Trojans, and worms',
        total_number_of_viruses: 'Total number of viruses',
        ransomware: 'Ransomware',
        trojan_virus: 'Trojan Virus',
        worm_virus: 'Worm Virus',

        virus_type: 'Virus Type',
        placeholder_search: 'Search for virus name or description...',
        delete_message: 'Are you sure you want to delete virus {name}?',
        delete_protected_error: 'Cannot delete this virus because it is being used by exercise projects. Please delete the related exercise projects first.',
        delete_protected_error_with_exercises: 'Cannot delete virus "{virusName}" because it is being used by the following exercise projects: {exercises}. Please delete these exercise projects first.',
        message_1: 'Failed to obtain virus list',
        message_2: 'Failed to obtain virus details',
        message_3: 'Failed to obtain virus family list',
        form: {
          virus_information: 'Virus Information',

          automated_infection: 'Automated infection',
          infection_information_configuration: 'Infection Information Configuration',
          virus_name: 'Virus Name',
          placeholder_virus_name: 'Please enter the virus name',
          reference_family: 'Reference Family',
          placeholder_reference_family: 'Please select the reference family',
          type_of_infection: 'Type of infection',
          placeholder_type_of_infection: 'Please select the infection type',
          encrypted_suffix: 'Encrypted suffix',
          placeholder_encrypted_suffix: 'Enter encrypted file suffix, e.g.: .locked, .encrypted, .virus',
          upload_encryptor: 'Upload encryptor',


          encryptor: 'Encryptor',
          placeholder_encryptor: 'Please upload the encryptor file',
          encrypto_key: 'Symmetric encryption key',
          placeholder_encrypto_key: 'Please enter the symmetric encryption key, length 16 or 32 bits',
          encrypto_iv: 'Symmetric encryption vector',
          placeholder_encrypto_iv: 'Please enter the symmetric encryption vector, length 16 or 32 bits',
          target_dir: 'Encryption path (if there are multiple paths, please use commas to separate)',
          placeholder_target_dir: 'Please enter the encryption path',
          encrypted_file_types: 'File types to encrypt',
          placeholder_encrypted_file_types: 'Please enter file types, e.g.: txt,doc,jpg (comma separated)',

          custom_suffix: 'Encrypted suffix',
          ransom_note_name: 'Ransom note filename',
          placeholder_ransom_note_name: 'Please enter the ransom note filename',
          upload_wallpaper: 'Upload wallpaper',
          wallpaper: 'Wallpaper',
          click_to_change_wallpaper: 'Click to change wallpaper',
          placeholder_wallpaper: 'Please upload the wallpaper',
          ransom_note_content: 'Ransom note content',
          placeholder_ransom_note_content: 'Please enter the ransom note content...'
        }
      },
      strategy: {
        strategy: ' Strategy',
        title: 'Sending strategy',
        subtitle: 'Configure the sending strategy of phishing emails, including sending time, frequency, etc.',
        placeholder_search: 'Search strategy name',
        delete_message: 'Are you sure you want to delete strategy {name}?',
        form: {
          p1: 'Email testing is required before creating a policy',
          p2: 'Please fill in the email configuration information and test it. You can create a policy only after the test is successful',
          placeholder_strategy_name: 'Please enter the strategy name',
          placeholder_interface_type: 'Please select interface type',
          placeholder_sender_email: 'Please enter the sender email',
          placeholder_email_server_address: 'Please enter the email server address',
          server_port: 'Server Port',
          placeholder_server_port: 'Please enter the server port',
          email_server_account: 'Email Server Account',
          placeholder_email_server_account: 'Please enter the email server account',
          email_server_password: 'Email Server Password',
          placeholder_email_server_password: 'Please enter the email server password',
          send_test_email: 'Send Test Email',
          send_test_email_desc: 'Please make sure that the email configuration is correct and the policy can be created only after the test is successful',
          target_email_address: 'Target Email Address',
          placeholder_target_email_address: 'Please enter the target email address',
          custom_email_header: 'Custom Email Header',
          test_success: '(Test Success)',
          sending: 'Send...',
          test_success_message: 'Test email sent successfully',
        }
      },
      template: {
        templates: ' Templates',
        title: 'Email Templates',
        subtitle: 'Manage phishing email templates, support custom email content and style',
        placeholder_search: 'Search Templates',
        delete_message: 'Are you sure you want to delete template {name}?',
        form: {
          placeholder_template_name: 'Please enter a template name',
          placeholder_email_subject: 'Please enter the email subject',
          whether_to_track_users: 'Whether to track users',
          importing_a_template: 'Importing a template',
          add_attachments: 'Add attachments',
          virus: 'Select virus',
          placeholder_virus: 'Please select a virus',
          whether_to_compress_attachments: 'Whether to compress attachments',
          compressed_file_name: 'Compressed file name',
          placeholder_compressed_file_name: 'Please enter the compressed file name',
          compressed_package_password: 'Compressed package password',
          placeholder_compressed_package_password: 'Please enter the compressed package password'
        }
      },
      page: {
        page: ' Page',
        title: 'Phishing Page',
        subtitle: 'Manage phishing page templates, support custom page content and style',
        placeholder_search: 'Search Page',
        delete_message: 'Are you sure you want to delete page {name}?',
        form: {
          placeholder_page_name: 'Please enter a page name',
          redirect_address: 'Redirect address',
          placeholder_redirect_address: 'Please enter the redirect address',
          clone_a_website: 'Clone a website',
          placeholder_url: 'Please enter the URL',
          placeholder_content: 'Please fill in the page content',
        }
      },
      task: {
        task: ' Task',
        title: 'Mail Tasks',
        subtitle: 'Manage phishing email sending tasks and view task execution status and results',
        placeholder_search: 'Search task name',
        delete_message: 'Are you sure you want to delete task {name}?',
        form: {
          placeholder_task_name: 'Please enter the task name',
          placeholder_email_templates: 'Please select an email template',
          placeholder_phishing_page: 'Please select the phishing page',
          placeholder_sending_strategy: 'Please select a delivery strategy',
          phishing_link: 'Phishing Link',
          placeholder_phishing_link: 'Please enter the phishing link'
        }
      },
      negotiation: {
        negotiation: ' Negotiation',
        title: 'Negotiation Management',
        subtitle: 'Manage negotiation strategies and rule configurations, support adding, editing, and deleting configuration items',
        placeholder_search: 'Search Configuration Name/Description',
        delete_message: 'Are you sure you want to delete negotiation {name}?',
        copied_to_clipboard: 'ID copied to clipboard',
        copy_failure: 'Copy Failure',
        recent_sessions: 'Recent Sessions',
        automatic_reply: 'Automatic Reply',
        manual_reply: 'Manual Reply',
        start_negotiations: 'Start negotiations',
        no_introduction_yet: 'No introduction yet',
        message_1: 'Failed to obtain configuration list',
        form: {
          id_page: 'ID Page',
          placeholder_id_page: 'Please enter ID',
          home_page: 'Home',
          platform_name: 'Platform name',
          placeholder_platform_name: 'Please enter the platform name',
          company_name: 'Company name',
          placeholder_company_name: 'Please enter the company name',
          official_website_link: 'Official website link',
          placeholder_official_website_link: 'Please enter the official website link',
          number_of_home_page_impressions: 'Number of home page impressions',
          placeholder_number_of_home_page_impressions: 'Please enter the number of home page impressions',
          company_introduction: 'Company introduction',
          placeholder_company_introduction: 'Please enter the company introduction',
          virus_family_details_page: 'Virus family details page',
          logo: 'Company LOGO',
          logo_required: 'Company LOGO (Required)',
          company_valuation: 'Company valuation',
          placeholder_company_valuation: 'Please enter the company valuation',
          amount_of_stolen_data: 'Amount of stolen data',
          placeholder_amount_of_stolen_data: 'Please enter the amount of stolen data',
          negotiation_page: 'Negotiation page',
          ransom_btc: 'Ransom (BTC)',
          placeholder_ransom_btc: 'Please enter the company valuation (BTC)',
          ransom_usdt: 'Ransom (USDT)',
          placeholder_ransom_usdt: 'Please enter the company valuation (USDT)',
          deadline: 'Deadline',
          placeholder_deadline: 'Please enter the deadline',
          select_time: 'Select time',
          auto_chat: 'Auto chat',
          auto_reply: 'Once enabled, it will automatically reply to user messages',
          btc_address: 'BTC address',
          placeholder_btc_address: 'Please enter the BTC address',
          usdt_address: 'USDT address',
          placeholder_usdt_address: 'Please enter the USDT address'
        }
      },
      family: {
        family: ' Virus Family',
        title: 'Virus Family Management',
        subtitle: 'Manage virus family information, support adding, editing and deleting virus family information',
        placeholder_search: 'Search for virus family names',
        delete_message: 'Are you sure you want to delete virus family {name}?',
        message_1: 'Failed to obtain virus family list',
        form: {
          basic_information: 'Basic Information',
          placeholder_family_name: 'Please enter the virus family name',
          placeholder_first_appearance_time: 'Please enter the first occurrence time',
          placeholder_encryption_algorithm: 'Please select an encryption algorithm',
          placeholder_ransom_method: 'Please select the ransom method',
          single_encryption: 'Single encryption',
          double_encryption: 'Double encryption',
          triple_encryption: 'Triple encryption',
          quadruple_encryption: 'Quadruple encryption',
          placeholder_infection_type: 'Please select the infection type',
          placeholder_wallet_address: 'Please enter your wallet address',
          placeholder_public_decryptor: 'Please select a public decryptor',
          encryption_suffix: 'Encryption suffix',
          placeholder_encryption_suffix: 'Please enter the encryption suffix',
          family_profile: 'Introduction to Virus Family',
          placeholder_family_profile: 'Please enter a brief introduction to the virus family',
          attack_route: 'Attack route',
          placeholder_attack_route: 'Please enter the attack route',
          ransom_note: 'Ransom note',
          add_ransom_note: 'Add ransom note',
          ransom_note_name: 'Ransom note name',
          placeholder_ransom_note_name: 'Please enter the Ransom note name',
          text_content: 'Text content',
          placeholder_text_content: 'Please enter text content',
          ransomware_address: 'Ransomware address',
          add_ransomware_address: 'Add Ransomware address',
          address_option: 'Address Options',
          placeholder_address_option: 'Please select an address option',
          common_tools: 'Common Tools',
          add_tool: 'Add tool',
          virus_sample: 'Virus Sample',
          add_sample: 'Add sample',
          negotiation_record: 'Negotiation Record',
          add_record: 'Add record',
          victim_information: 'Victim Information',
          add_information: 'Add information',
          ioc_information: 'IOC Information',
          delete_img: 'Delete Image',
          table: {
            tool_name: 'Tool Name',
            tool_type: 'Tool Type',
            tool_description: 'Tool Description',
            attachment: 'Attachment',
            sample_name: 'Sample Name',
            sample_file: 'Sample File',
            date: 'Date',
            initial_amount: 'Initial Amount',
            final_delivery: 'Final Delivery',
            whether_paid: 'Whether Paid',
            chat_record: 'Chat Record',
            victim: 'Victim',
            official_website: 'Official Website',
            location: 'Location',
            introduction: 'Introduction'
          },
          placeholder_tool_name: 'Please enter the tool name',
          placeholder_select: 'Please select',
          ransomware: 'Ransomware',
          intranet_scanning: 'Intranet scanning',
          placeholder_tool_description: 'Please enter the tool description',
          placeholder_sample_name: 'Please enter the sample name',
          placeholder_date: 'Please enter the date',
          placeholder_initial_amount: 'Please enter the initial amount',
          placeholder_final_delivery: 'Please enter the final delivery',
          placeholder_victim: 'Please enter the victim',
          placeholder_official_website: 'Please enter the official website',
          placeholder_location: 'Please enter the location',
          placeholder_introduction: 'Please enter the introduction'
        },
        detail: {
          title: 'Virus Family Details',
          subtitle: 'View virus family details',
          virus_family_identification: 'Virus family identification',
          address: 'Address',
          download_tool: 'Download tool',
          no_file: 'No file',
          related_documents: 'Related Documents',
          visit: 'Visit'
        }
      },
      exercise: {
        exercise: ' Exercise',
        title: 'Exercise Management',
        subtitle: 'Manage and configure exercise tasks, supporting multiple exercise scenarios',
        placeholder_search: 'Search for exercise name',
        delete_message: 'Are you sure you want to delete exercise {name}?',
        not_started: 'Not started',
        in_progress: 'In progress',
        completed: 'Completed',
        completion_progress: 'Completion Progress',
        exercise_report: 'Report',
        infection_situation: 'Infection',
        data_screen: 'Screen',
        phishing: 'Phishing',
        message_1: 'Failed to get the drill list',
        form: {
          exercise_name: 'Exercise name',
          placeholder_exercise_name: 'Please enter a name for the exercise',
          fishing_mission: 'Fishing Mission',
          placeholder_fishing_mission: 'Please select a fishing mission',
          virus_configuration: 'Virus Configuration',
          placeholder_virus_configuration: 'Please select virus configuration',
          negotiation_configuration: 'Negotiation Configuration',
          placeholder_negotiation_configuration: 'Please select negotiation configuration',
          email_information: 'Email Information',
          placeholder_email_information: 'Please select email information (multiple choices)',
          asset_information: 'Asset Information',
          placeholder_asset_information: 'Please select the asset information (multiple choices)',
          start_time: 'Start time',
          duration: 'Duration',
          end_time: 'End time',
          not_set: 'Not set'
        },
        detail: {
          exercise_details: 'Exercise details',
          h1: 'Phishing mission information',
          h2: 'Virus configuration information',
          h3: 'Negotiation configuration information',
          h4: 'Mailbox asset information',
          phishing_links: 'Phishing Links',
          encryptor_name: 'Encryptor name',
          encryptor_download: 'Encryptor download',
          attack_configuration: 'Attack configuration',
          infection_configuration: 'Infection configuration',
          ip_pool: 'IP Pool',
          function_configuration: 'Function Configuration',
          negotiation_function: 'Negotiation function',
          viral_evolution: 'Viral evolution',
          ransomware_information: 'Ransomware Information',
          time_information: 'Time Information'
        },
        report: {
          title: 'Network Security Exercise Report',
          exercise_time: 'Exercise Time',
          exercise_unit: 'Exercise Unit',
          exercise_status: 'Exercise Status',
          virus_name: 'Virus Name',
          h1: 'Exercise Results Statistics',
          total_number_of_targeted_devices: 'Total number of target devices',
          number_of_infected_devices: 'Number of infected devices',
          phishing_email_click_rate: 'Phishing Email Click Rate',
          total_number_of_sent_emails: 'Total number of sent emails',
          number_of_clicks: 'Number of clicks',
          h2: 'Exercise Timeline',
          h3: 'Target Asset Information',
          h4: 'Email Sending Record',
          exercise_begins: 'Exercise begins',
          send_email: ' Send email',
          click_email: 'Click email',
          submit_data: 'Submit data',
          not_clicked: 'Not clicked',
          clicked: 'Clicked'
        }
      },
      infection: {
        title: 'Infection status',
        subtitle: 'View and manage all virus infection records',
        placeholder_search: 'Search device ID',
        delete_message: 'Are you sure you want to delete the infection record {name}? ',
        not_infected: 'Not infected',
        infected: 'Infected',
        infection_status: 'Infection status',
        system_version: 'System version',
        location: 'Location',
        first_infection: 'First infection',
        last_infection: 'Last infection',
        device_infection_details: 'Device infection details',
        device_id: 'Device ID',
        hostname: 'Host name',
        program_path: 'Program path',
        remote_operation: 'remote operation',

        execute: 'Execute',
        executing: 'Executing...',
        offline_all: 'Offline All Devices',
        offline_all_success: 'All devices are offline',
        offline_all_failed: 'Offline operation failed',
        check_online_status: 'Check online status',
        change_wallpaper: 'Change wallpaper',
        modify_target_device_wallpaper: 'Modify target device wallpaper',
        restore_wallpaper: 'Restore wallpaper',
        restore_device_original_wallpaper: 'Restore device original wallpaper',
        command_execution: 'Command execution',
        turn_off_antivirus_software: 'Turn off antivirus software',
        maintaining_authority: 'Maintaining authority',
        maintain_device_authority: 'Maintaining virus authority',
        start_encryption: 'Start encryption',
        encrypt_target_device_files: 'Encrypt target device files',
        start_decryption: 'Start decryption',
        decrypt_target_device_files: 'Decrypt target device files',
        destroy_virus: 'Destroy virus',
        remove_virus_from_target_device: 'Remove virus from target device',
        lateral_scan: 'Lateral Scan',
        perform_lateral_scan: 'Perform lateral scan',
        target_ip: 'Target IP',
        enter_target_ip: 'Please enter target IP address',
        target_port: 'Target Port',
        enter_target_port: 'Please enter target port',
        start_scan: 'Start Scan',
        scan_again: 'Scan Again',
        scan_result: 'Scan Result',
        scan_started: 'Scan started',
        scan_failed: 'Scan failed',
        scan_task_submitted: 'Scan task submitted, Task ID: {taskId}',
        invalid_ip_format: 'Invalid IP address format',
        invalid_port_format: 'Port number must be between 1-65535',
        unknown_command: 'Unknown command',
        command_execution_history: 'Command execution history',
        execution_time: 'Execution time',
        command_type: 'Command type',
        execution_status: 'Execution status',
        infection_history: 'Infection history',
        export_data: 'Export data',
        infection_time: 'Infection time',
        message_1: 'The device is currently offline and cannot execute the command.',
        message_2: 'Failed to obtain command history',
        message_3: 'Device is not online',
        detail: {
          title: 'Command execution details',
          execution_time: 'Execution time',
          execution_success: 'Execution success',
          execution_failed: 'Execution failure',
          command_parameters: 'Command parameters',
          execute_command: 'Execution command',
          device_id: 'Device ID',
          execution_response: 'Execution response',
          status_code: 'Status Code',
          status: 'Status',
          response_message: 'Response message',
          response_data: 'Response data',
          no_parameters: 'No parameters'
        }
      },
      notification: {
        title: 'Message Notification',
        subtitle: 'View and manage all your notifications',
        mark_read: 'Mark as read',
        mark_all_as_read: 'Mark all as read',
        news: 'New news',
        p1: 'New notifications will appear here',
        message_1: 'Marked as read',
        message_2: 'All marked as read',
        message_3: 'Operation failed',
        message_4: 'Failed to obtain notification list'
      },
      profile: {
        title: 'Personal message',
        update_avatar: 'Change your profile picture',
        role: 'Role',
        last_login: 'Last login',
        change_password: 'Change password',
        current_password: 'Current password',
        new_password: 'New password',
        weak: 'Weak',
        medium: 'Medium',
        powerful: 'Powerful',
        confirm_new_password: 'Confirm new password',
        rules_new_required: 'Please enter new password',
        rules_current_required: 'Please enter current password',
        rules_new_min: 'Password length cannot be less than 8 characters',
        rules_confirm_required: 'Please enter new password again',
        under_modification: 'Under Modification...',
        message_1: 'Failed to obtain user information',
        rules_new_number: 'The password must contain numbers',
        rules_new_letter: 'The password must contain letters',
        rules_new_Inconsistency: 'The two passwords you entered do not match'
      },
      settings: {
        title: 'System Settings',
        basic_settings: 'Basic Settings',
        system_name: 'System Name',
        system_logo: 'System Logo',
        upload_logo: 'Upload Logo',
        copyright_information: 'Copyright Information',
        system_time_zone: 'System Time Zone',
        utc8_beijing_time: 'UTC+8 (Beijing Time)',
        utc0_greenwich_mean_time: 'UTC+0 (Greenwich Mean Time)',
        date_format: 'Date Format',
        security_settings: 'Security Settings',
        password_policy: 'Password Policy',
        minimum_password_length: 'Minimum Password Length',
        must_contain_uppercase_letters: 'Must contain uppercase letters',
        must_contain_numbers: 'Must contain numbers',
        must_contain_special_characters: 'Must contain special characters',
        session_timeout_minutes: 'Session timeout (minutes)',
        maximum_number_of_login_attempts: 'Maximum number of login attempts',
        save_settings: 'Save settings'
      },
      table: {
        exercise_name: 'Exercise name',
        status: 'Status',
        start_time: 'Start time',
        end_time: 'End time',
        action: 'Action',
        details: 'View details',
        avatar: 'Avatar',
        username: 'Username',
        email: 'Email',
        company: 'Company',
        password: 'Password',
        phone: 'Phone',
        create_time: 'Create time',
        asset_name: 'Asset name',
        asset_type: 'Asset type',
        ip_address: 'IP address',
        mac_address: 'MAC address',
        user_group: 'User group',
        group: 'Group',
        asset: 'Asset',
        asset_group: 'Asset group',
        name: ' Name',
        description: ' Description',
        number_of_assets: 'Number of assets',
        type: ' Type',
        strategy_name: 'Strategy name',
        interface_type: 'Interface type',
        sender_email: 'Sender email',
        email_server_address: 'Email server address',
        whether_to_ignore_certificate_errors: 'Whether to ignore certificate errors',
        email_header: 'Email header',
        content: 'Content',
        template_name: 'Template name',
        email_subject: 'Email subject',
        file_name: 'File name',
        file_size: 'File size',
        page_name: 'Page name',
        capture_submission_data: 'Capture submission data',
        capture_password: 'Capture Password',
        task_name: 'Task name',
        email_templates: 'Email Templates',
        phishing_page: 'Phishing Page',
        sending_strategy: 'Sending strategy',
        family_name: 'Virus Family Name',
        family_logo: 'Virus Family Logo',
        first_appearance_time: 'First appearance time',
        encryption_algorithm: 'Encryption Algorithm',
        ransom_method: 'Ransom Method',
        infection_type: 'Infection Type',
        wallet_address: 'Wallet Address',
        public_decryptor: 'Public Decryptor',
        update_time: 'Update Time',
        recipient: 'recipient',
        send_time: 'sending time',
        click_status: 'click status',
        template_type: 'Template Type',
        template_type_system: 'System Template',
        template_type_custom: 'Custom Template'
      }
    }
  }
}))