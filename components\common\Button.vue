<template>
  <button
    :type="type"
    :class="[
      'inline-flex items-center justify-center rounded-lg text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed',
      sizeClasses[size],
      variantClasses[variant],
      block ? 'w-full' : '',
      className
    ]"
    :disabled="loading || disabled"
    v-bind="$attrs"
  >
    <!-- 加载状态图标 -->
    <LoadingSpinner
      v-if="loading"
      class="w-4 h-4 mr-2"
    />
    <!-- 自定义图标 -->
    <slot name="icon"></slot>
    <!-- 按钮文本 -->
    <span>
      <slot>{{ loading ? loadingText : text }}</slot>
    </span>
  </button>
</template>

<script setup lang="ts">
import LoadingSpinner from '~/components/common/LoadingSpinner.vue'

interface Props {
  // 按钮类型
  type?: 'button' | 'submit' | 'reset'
  // 按钮变体
  variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'link'
  // 按钮尺寸
  size?: 'sm' | 'md' | 'lg'
  // 是否加载中
  loading?: boolean
  // 加载中文本
  loadingText?: string
  // 按钮文本
  text?: string
  // 是否禁用
  disabled?: boolean
  // 是否块级元素
  block?: boolean
  // 自定义类名
  className?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'button',
  variant: 'primary',
  size: 'md',
  loading: false,
  loadingText: '加载中...',
  text: '',
  disabled: false,
  block: false,
  className: ''
})

// 尺寸样式映射
const sizeClasses = {
  sm: 'px-3 py-1.5 text-sm',
  md: 'px-4 py-2',
  lg: 'px-5 py-2.5 text-lg'
}

// 变体样式映射
const variantClasses = {
  primary: 'text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500',
  secondary: 'text-gray-700 bg-white border border-gray-300 hover:bg-gray-50 focus:ring-gray-500',
  success: 'text-white bg-green-600 hover:bg-green-700 focus:ring-green-500',
  warning: 'text-white bg-yellow-600 hover:bg-yellow-700 focus:ring-yellow-500',
  danger: 'text-white bg-red-600 hover:bg-red-700 focus:ring-red-500',
  link: 'text-blue-600 hover:text-blue-900 underline bg-transparent'
}
</script>