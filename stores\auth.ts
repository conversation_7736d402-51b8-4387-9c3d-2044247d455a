import { defineStore } from 'pinia'
import { useApi } from '~/composables/useApi'
import { useToast } from '~/composables/useToast'
import type { User, LoginCredentials, RegisterCredentials, PhoneLoginCredentials } from '~/types'
import { navigateTo } from '#app'

interface AuthState {
  user: User | null
  token: string | null
  refresh_token: string | null
  isAuthenticated: boolean
  isCheckingAuth: boolean
}

export const useAuthStore = defineStore('auth', {
  state: (): AuthState => {
    const auth = process.client ? JSON.parse(localStorage.getItem('auth') || 'null') : null
    return {
      user: auth?.user || null,
      token: auth?.token || null,
      refresh_token: auth?.refresh_token || null,
      isAuthenticated: !!auth?.token,
      isCheckingAuth: false
    }
  },

  getters: {
    isAdmin: (state: AuthState) => state.user?.role === 'admin',
    userRole: (state: AuthState) => state.user?.role
  },

  actions: {
    async login(credentials: LoginCredentials) {
      const api = useApi()
      try {
        const response = await api.login(credentials)

        if (!response) {
          console.error('Login error: Invalid response')
          return { success: false, error: 'invalid_response' }
        }

        const authData = {
          token: response.access,
          refresh_token: response.refresh,
          user: response.user
        }

        // 更新store中的状态
        this.$patch({
          ...authData,
          isAuthenticated: true
        })

        // 保存到 localStorage
        if (process.client) {
          localStorage.setItem('auth', JSON.stringify(authData))
        }

        return { success: true }
      } catch (error: any) {
        console.error('Login failed:', error)

        // 根据错误类型返回不同的错误信息
        if (error.type === 'auth_error') {
          return { success: false, error: 'auth_error', message: error.message }
        } else if (error.type === 'network_error') {
          return { success: false, error: 'network_error', message: error.message }
        } else {
          return { success: false, error: 'unknown_error', message: '登录失败，请稍后重试' }
        }
      }
    },

    async logout() {
      // 只有在用户确实已登录的情况下才显示登出成功提示
      const wasAuthenticated = this.isAuthenticated

      // 清除状态
      this.$patch({
        user: null,
        token: null,
        refresh_token: null,
        isAuthenticated: false,
        isCheckingAuth: false
      })

      // 清除localStorage
      if (process.client) {
        localStorage.removeItem('auth')
      }

      // 只有在用户确实已登录的情况下才显示登出成功提示
      if (wasAuthenticated) {
        const { success } = useToast()
        success('已成功登出')
      }

      // 重定向到登录页
      navigateTo('/login')
    },

    async refreshToken() {
      const api = useApi()
      try {
        if (!this.refresh_token) {
          throw new Error('No refresh token')
        }

        const response = await api.refreshToken(this.refresh_token)

        if (!response || !response.access) {
          throw new Error('Invalid response')
        }

        const authData = {
          token: response.access,
          refresh_token: response.refresh,
          user: response.user
        }

        // 更新store中的状态
        this.$patch({
          ...authData,
          isAuthenticated: true
        })

        // 更新 localStorage
        if (process.client) {
          localStorage.setItem('auth', JSON.stringify(authData))
        }

        return true
      } catch (error) {
        console.error('Token refresh failed:', error)
        // 如果刷新失败，登出用户
        await this.logout()
        return false
      }
    },

    async checkAuth() {
      if (!this.token) {
        return false
      }

      // 防止并发检查
      if (this.isCheckingAuth) {
        return this.isAuthenticated
      }

      this.isCheckingAuth = true

      try {
        const success = await this.fetchUserInfo()
        this.isAuthenticated = success
        return success
      } catch (error) {
        console.error('Auth check failed:', error)
        // 尝试刷新 token
        const refreshSuccess = await this.refreshToken()
        if (!refreshSuccess) {
          await this.logout()
          return false
        }
        return true
      } finally {
        this.isCheckingAuth = false
      }
    },

    async fetchUserInfo() {
      const api = useApi()
      try {
        const response = await api.getCurrentUser()
        if (response) {
          this.user = response
          return true
        }
        return false
      } catch (error: any) {
        console.error('Failed to fetch user info:', error)
        if (error.response?.status === 401) {
          await this.logout()
        }
        return false
      }
    },

    async phoneLogin(credentials: PhoneLoginCredentials) {
      const api = useApi()
      try {
        const response = await api.phoneLogin(credentials)

        if (!response) {
          console.error('Phone login error: Invalid response')
          return { success: false, error: 'invalid_response' }
        }

        const authData = {
          token: response.access,
          refresh_token: response.refresh,
          user: response.user
        }

        this.$patch({
          ...authData,
          isAuthenticated: true
        })

        if (process.client) {
          localStorage.setItem('auth', JSON.stringify(authData))
        }

        return { success: true }
      } catch (error: any) {
        console.error('Phone login failed:', error)

        // 根据错误类型返回不同的错误信息
        if (error.type === 'auth_error') {
          return { success: false, error: 'auth_error', message: error.message }
        } else if (error.type === 'network_error') {
          return { success: false, error: 'network_error', message: error.message }
        } else {
          return { success: false, error: 'unknown_error', message: '登录失败，请稍后重试' }
        }
      }
    },

    async sendVerificationCode(phone: string) {
      const api = useApi()
      try {
        const response = await api.sendVerificationCode({ phone })
        return { success: true, message: response.message }
      } catch (error: any) {
        console.error('Send verification code failed:', error)

        // 根据错误类型返回不同的错误信息
        if (error.type === 'verification_error') {
          return { success: false, error: 'verification_error', message: error.message }
        } else if (error.type === 'network_error') {
          return { success: false, error: 'network_error', message: error.message }
        } else {
          return { success: false, error: 'unknown_error', message: '发送验证码失败，请稍后重试' }
        }
      }
    },

    async register(credentials: RegisterCredentials) {
      const api = useApi()
      try {
        const response = await api.register(credentials)

        if (!response) {
          console.error('Registration error: Invalid response')
          return { success: false, error: 'invalid_response' }
        }

        // const authData = {
        //   token: response.access,
        //   refresh_token: response.refresh,
        //   user: response.user
        // }

        // this.$patch({
        //   ...authData,
        //   isAuthenticated: true
        // })

        // if (process.client) {
        //   localStorage.setItem('auth', JSON.stringify(authData))
        // }

        return {
          success: true,
          data: response
        }
      } catch (error: any) {
        console.error('Registration failed:', error)

        // 根据错误类型返回不同的错误信息
        if (error.type === 'register_error') {
          return { success: false, error: 'register_error', message: error.message }
        } else if (error.type === 'network_error') {
          return { success: false, error: 'network_error', message: error.message }
        } else {
          return { success: false, error: 'unknown_error', message: '注册失败，请稍后重试' }
        }
      }
    }
  }
})