from config.models import BaseModel
from django.contrib.auth.models import AbstractUser
from django.db import models
from django.utils.translation import gettext_lazy as _


class User(AbstractUser, BaseModel):
    """自定义用户模型"""
    phone = models.CharField("手机号", max_length=11, blank=True)
    avatar = models.ImageField("头像", upload_to="avatar/", null=True, blank=True)
    # 公司名称
    company_name = models.CharField(verbose_name="公司名称", null=True, blank=True, max_length=300)
    # 是否是试用用户
    is_trial = models.BooleanField(default=False)
    last_login_ip = models.GenericIPAddressField(_("最后登录IP"), null=True, blank=True)

    class Meta:
        verbose_name = _("用户")
        verbose_name_plural = verbose_name

    def __str__(self):
        return self.username

    def has_permission(self, permission):
        """检查用户是否有指定权限"""
        if self.is_superuser:
            return True
        if self.is_staff:
            return True
        return False


class SMSLog(BaseModel):
    """短信发送记录"""
    phone = models.CharField(max_length=11, verbose_name="手机号")
    code = models.CharField(max_length=6, verbose_name="验证码")
    template_code = models.CharField(max_length=20, verbose_name="模板编号")
    status = models.CharField(max_length=50, verbose_name="发送状态")
    message = models.TextField(verbose_name="状态信息", null=True, blank=True)
    request_id = models.CharField(
        max_length=50, verbose_name="请求ID", null=True, blank=True
    )
    biz_id = models.CharField(
        max_length=50, verbose_name="业务ID", null=True, blank=True
    )

    class Meta:
        verbose_name = "短信记录"
        verbose_name_plural = verbose_name
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.phone} - {self.status}"


class InvitationCode(BaseModel):
    """邀请码，只能用一次"""
    code = models.CharField(verbose_name="邀请码", max_length=100)
    company = models.CharField(max_length=255, blank=True, null=True)
    is_used = models.BooleanField(default=False)
    used_by = models.ForeignKey(User, null=True, blank=True, on_delete=models.SET_NULL, related_name='invitations')
    used_at = models.DateTimeField(verbose_name="使用时间", null=True, blank=True)

    class Meta:
        db_table = 'ls_invitations'
        verbose_name = "邀请码管理"
        verbose_name_plural = verbose_name
        ordering = ["-created_at"]

    def __str__(self):
        return str(self.code)


class InvitationUserTemplate(BaseModel):
    """
    邀请用户模板
    """
    virus = models.JSONField(verbose_name='病毒配置', default=dict)
    negotiation = models.JSONField(verbose_name='谈判记录', default=dict)
    asset_count = models.IntegerField(verbose_name="资产数量", default=1)


    class Meta:
        db_table = 'ls_invitation_template'
        verbose_name = "邀请码管理"
        verbose_name_plural = verbose_name
        ordering = ["-created_at"]

    def __str__(self):
        return str(self.asset_count)
