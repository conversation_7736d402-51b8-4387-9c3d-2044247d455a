#!/usr/bin/env python
"""
测试演练资产冲突检查的脚本
用于验证修复后的逻辑是否正确工作
"""

import os
import sys
import django
from datetime import datetime, timedelta

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from django.utils import timezone
from apps.exercise.models import Exercise
from apps.assets.models import Asset, AssetGroup


def test_exercise_conflict_logic():
    """测试演练冲突检查逻辑"""
    
    print("=== 演练资产冲突检查测试 ===\n")
    
    # 1. 显示当前所有演练的状态
    print("1. 当前演练状态:")
    exercises = Exercise.objects.all().order_by('-created_at')
    
    if not exercises.exists():
        print("   没有找到任何演练")
        return
    
    now = timezone.now()
    
    for exercise in exercises:
        status_display = exercise.get_status_display()
        
        # 计算动态状态（前端逻辑）
        if exercise.status == 'FI':
            dynamic_status = '已完成'
        elif not exercise.start_time:
            dynamic_status = '待开始'
        elif now < exercise.start_time:
            dynamic_status = '待开始'
        elif exercise.end_time and now > exercise.end_time:
            dynamic_status = '已完成（时间过期）'
        elif now >= exercise.start_time:
            dynamic_status = '进行中'
        else:
            dynamic_status = status_display
        
        print(f"   - {exercise.name} (ID: {exercise.id})")
        print(f"     数据库状态: {status_display}")
        print(f"     动态状态: {dynamic_status}")
        print(f"     开始时间: {exercise.start_time}")
        print(f"     结束时间: {exercise.end_time}")
        print()
    
    # 2. 测试资产冲突检查逻辑
    print("2. 测试资产冲突检查逻辑:")
    
    # 获取状态为待开始或进行中的演练
    potential_active_exercises = Exercise.objects.filter(status__in=["PE", "RU"])
    print(f"   数据库中状态为PE或RU的演练数量: {potential_active_exercises.count()}")
    
    # 应用时间过滤
    active_exercises = []
    for exercise in potential_active_exercises:
        if not exercise.end_time or now <= exercise.end_time:
            active_exercises.append(exercise.id)
            print(f"   - {exercise.name} 被认为是活跃的")
        else:
            print(f"   - {exercise.name} 已过期，不被认为是活跃的")
    
    print(f"   经过时间过滤后的活跃演练数量: {len(active_exercises)}")
    
    # 3. 显示资产组信息
    print("\n3. 资产组信息:")
    asset_groups = AssetGroup.objects.all()
    for group in asset_groups:
        asset_count = group.asset.count()
        print(f"   - {group.name}: {asset_count} 个资产")
    
    print("\n=== 测试完成 ===")


if __name__ == '__main__':
    test_exercise_conflict_logic()
