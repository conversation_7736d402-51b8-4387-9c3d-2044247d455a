<template>
  <div class="bg-white dark:bg-gray-800 rounded-lg shadow w-full">
    <!-- 标题栏 -->
    <div class="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700">
      <!-- 左侧标题 -->
      <div>
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">最近感染</h3>
        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">最新检测到的病毒感染事件</p>
      </div>

      <!-- 右侧内容 -->
      <div class="flex items-center space-x-4">
        <span class="text-sm text-gray-500 dark:text-gray-400">
          {{ infections.length }} 条记录
        </span>
        <button class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 text-sm font-medium">
          查看全部
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="flex items-center justify-center p-8">
      <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-red-500"></div>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="p-4 text-center text-red-500 dark:text-red-400">
      <svg class="w-8 h-8 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
          d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <p>{{ error }}</p>
    </div>

    <!-- 空状态 -->
    <div v-else-if="infections.length === 0" class="flex flex-col items-center justify-center p-8">
      <div class="w-16 h-16 mb-4 text-gray-400 dark:text-gray-600">
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" 
            d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      </div>
      <p class="text-gray-500 dark:text-gray-400">暂无感染记录</p>
    </div>

    <!-- 感染记录列表 -->
    <div v-else class="divide-y divide-gray-100 dark:divide-gray-700">
      <div v-for="infection in infections" :key="infection.id"
        class="group hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors duration-200">
        <div class="px-6 py-4">
          <div class="flex items-center space-x-4">
            <!-- 图标 -->
            <div class="flex-shrink-0 relative">
              <div class="w-9 h-9 rounded-lg bg-red-50 dark:bg-red-900/20 flex items-center justify-center">
                <Icon name="heroicons:exclamation-triangle" class="w-5 h-5 text-red-600 dark:text-red-400" />
              </div>
              <div class="absolute -top-1 -right-1 w-2.5 h-2.5 bg-red-500 rounded-full border-2 border-white dark:border-gray-800"></div>
            </div>

            <!-- 内容 -->
            <div class="flex-1 min-w-0">
              <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3 overflow-hidden">
                  <h4 class="text-sm font-medium text-gray-900 dark:text-white truncate leading-6">
                    {{ infection.asset_name }}
                  </h4>
                  <div class="flex items-center space-x-3 flex-shrink-0">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium leading-4"
                      :class="statusClass(infection.status)">
                      {{ statusLabel(infection.status) }}
                    </span>
                    <div class="flex items-center space-x-2 text-gray-500 dark:text-gray-400">
                      <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                          d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9" />
                      </svg>
                      <span class="text-xs leading-4">{{ infection.ip_address }}</span>
                    </div>
                  </div>
                </div>
                <time :datetime="infection.infection_time" 
                  class="text-xs text-gray-500 dark:text-gray-400 whitespace-nowrap ml-3 flex-shrink-0 leading-4">
                  {{ formatTime(infection.infection_time) }}
                </time>
              </div>
            </div>

            <!-- 操作按钮 -->
            <div class="flex-shrink-0">
              <button class="p-1.5 text-gray-400 rounded-lg opacity-0 group-hover:opacity-100 hover:text-gray-900 hover:bg-gray-100 dark:hover:text-white dark:hover:bg-gray-700 transition-all duration-200">
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 刷新提示 -->
    <div class="px-6 py-3 text-xs text-center text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700">
      <div class="flex items-center justify-center space-x-1">
        <svg class="w-3 h-3 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
        </svg>
        <span>每 30 秒自动刷新</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { dashboardApi } from '~/api/dashboard'

const infections = ref([])
const loading = ref(false)
const error = ref(null)

// 状态映射
const STATUS_MAP = {
  IN: {
    label: '已感染',
    class: 'bg-red-50 text-red-700 dark:bg-red-900/20 dark:text-red-400'
  },
  EN: {
    label: '已加密',
    class: 'bg-yellow-50 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400'
  },
  OF: {
    label: '已下线',
    class: 'bg-gray-50 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400'
  },
  RC: {
    label: '已恢复',
    class: 'bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-400'
  }
}

const statusClass = (status) => STATUS_MAP[status]?.class || 'bg-gray-50 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400'
const statusLabel = (status) => STATUS_MAP[status]?.label || status

const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`
  
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

const fetchRecentInfections = async () => {
  try {
    loading.value = true
    error.value = null
    const data = await dashboardApi.getRecentInfections({
      hours: 24,
      limit: 10
    })
    infections.value = data
  } catch (err) {
    console.error('Failed to fetch recent infections:', err)
    error.value = '获取感染记录失败'
    infections.value = []
  } finally {
    loading.value = false
  }
}

// 自动刷新
let refreshTimer = null

onMounted(() => {
  fetchRecentInfections()
  refreshTimer = setInterval(fetchRecentInfections, 30000)
})

onUnmounted(() => {
  if (refreshTimer) {
    clearInterval(refreshTimer)
  }
})
</script>
