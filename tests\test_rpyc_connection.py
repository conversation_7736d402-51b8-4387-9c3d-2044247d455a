#!/usr/bin/env python3
"""
RPyC服务连接测试脚本
用于验证RPyC服务是否正常启动和运行
"""

import rpyc
import sys
import time

def test_rpyc_connection(host='localhost', port=18861, max_retries=5):
    """
    测试RPyC服务连接
    
    Args:
        host: 服务器地址
        port: 服务器端口
        max_retries: 最大重试次数
    """
    print(f"正在测试RPyC服务连接: {host}:{port}")
    
    for attempt in range(max_retries):
        try:
            print(f"尝试连接 (第{attempt + 1}次)...")
            
            # 连接RPyC服务器
            conn = rpyc.connect(host, port, config={
                "allow_all_attrs": True,
                "allow_pickle": True,
                "allow_getattr": True,
                "allow_setattr": True,
                "allow_delattr": True,
                "sync_request_timeout": 30
            })
            
            print("✅ 连接成功!")
            
            # 测试基本功能
            print("正在测试服务功能...")
            
            # 获取服务器状态
            status = conn.root.exposed_get_status()
            print(f"服务器状态: {status}")
            
            # 获取连接的客户端列表
            clients = conn.root.exposed_get_connected_clients()
            print(f"连接的客户端: {clients}")
            
            # 关闭连接
            conn.close()
            print("✅ RPyC服务测试完成，服务运行正常!")
            return True
            
        except ConnectionRefusedError:
            print(f"❌ 连接被拒绝 (第{attempt + 1}次)")
            if attempt < max_retries - 1:
                print("等待5秒后重试...")
                time.sleep(5)
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            if attempt < max_retries - 1:
                print("等待5秒后重试...")
                time.sleep(5)
    
    print("❌ RPyC服务连接失败，请检查服务是否正常启动")
    return False

def main():
    """主函数"""
    if len(sys.argv) > 1:
        host = sys.argv[1]
    else:
        host = 'localhost'
    
    if len(sys.argv) > 2:
        port = int(sys.argv[2])
    else:
        port = 18861
    
    success = test_rpyc_connection(host, port)
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
