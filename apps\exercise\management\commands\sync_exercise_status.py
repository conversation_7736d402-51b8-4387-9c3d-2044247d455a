from django.core.management.base import BaseCommand
from django.utils import timezone
from apps.exercise.models import Exercise


class Command(BaseCommand):
    help = '同步演练状态 - 将已过结束时间的演练状态更新为已完成'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='只显示需要更新的演练，不实际更新',
        )

    def handle(self, *args, **options):
        now = timezone.now()
        dry_run = options['dry_run']
        
        # 查找需要更新状态的演练
        exercises_to_update = Exercise.objects.filter(
            status__in=['PE', 'RU'],  # 待开始或进行中
            end_time__lt=now  # 结束时间已过
        )
        
        if not exercises_to_update.exists():
            self.stdout.write(
                self.style.SUCCESS('没有需要更新状态的演练')
            )
            return
        
        self.stdout.write(f'找到 {exercises_to_update.count()} 个需要更新状态的演练:')
        
        for exercise in exercises_to_update:
            self.stdout.write(
                f'  - {exercise.name} (ID: {exercise.id}) '
                f'状态: {exercise.get_status_display()} -> 已完成 '
                f'结束时间: {exercise.end_time}'
            )
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('这是预览模式，没有实际更新数据库')
            )
            return
        
        # 实际更新状态
        updated_count = exercises_to_update.update(status=Exercise.Status.FINISHED)
        
        self.stdout.write(
            self.style.SUCCESS(f'成功更新了 {updated_count} 个演练的状态为已完成')
        )
