import time

from django.utils import timezone
from rest_framework import serializers
from django.contrib.auth import get_user_model
from django.contrib.auth.password_validation import validate_password
from .models import SMSLog, InvitationCode
from drf_spectacular.utils import extend_schema_field
from django.urls import reverse
from rest_framework_simplejwt.tokens import RefreshToken
from urllib.parse import quote

User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    last_login = serializers.SerializerMethodField()
    date_joined = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = [
            'id', 'username', 'email',
            'phone', 'avatar',
            'is_active', 'last_login', 'date_joined',
            'is_staff', 'is_superuser', 'avatar'
        ]
        read_only_fields = ['id', 'date_joined', 'last_login']

    def get_last_login(self, obj):
        if obj.last_login:
            return obj.last_login.strftime('%Y-%m-%d %H:%M:%S')
        return None

    def get_date_joined(self, obj):
        if obj.date_joined:
            return obj.date_joined.strftime('%Y-%m-%d %H:%M:%S')
        return None


class UserCreateSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, required=True, validators=[validate_password])

    class Meta:
        model = User
        fields = [
            'username', 'password', 'email',
            'phone', 'is_active'
        ]

    def create(self, validated_data):
        user = User.objects.create_user(**validated_data)

        refresh = RefreshToken.for_user(user)

        user.tokens = {
            'refresh': str(refresh),
            'access': str(refresh.access_token),
        }

        return user

    def to_representation(self, instance):
        if hasattr(instance, 'tokens'):
            return instance.tokens
        return super().to_representation(instance)


class UserUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ['email', 'phone', 'is_active']


class ChangePasswordSerializer(serializers.Serializer):
    old_password = serializers.CharField(required=True)
    new_password = serializers.CharField(required=True, validators=[validate_password])
    new_password2 = serializers.CharField(required=True)

    def validate(self, attrs):
        if attrs['new_password'] != attrs['new_password2']:
            raise serializers.ValidationError({"new_password": "两次密码不一致"})
        return attrs


class UserProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = [
            'id', 'username', 'email',
            'phone', 'avatar',
            'created_at', 'last_login'
        ]
        read_only_fields = ['id', 'username', 'created_at', 'last_login']


class SMSLogSerializer(serializers.ModelSerializer):
    class Meta:
        model = SMSLog
        fields = ['id', 'phone', 'status', 'message', 'created_at']


class ResetPasswordSerializer(serializers.Serializer):
    new_password = serializers.CharField(required=True)


class PhoneLoginSerializer(serializers.Serializer):
    phone = serializers.CharField(required=True)
    verification_code = serializers.CharField(required=True)


class SendVerificationCodeSerializer(serializers.Serializer):
    phone = serializers.CharField(required=True)


class UpdateAvatarSerializer(serializers.Serializer):
    avatar = serializers.ImageField(required=True)


class RegisterSerializer(serializers.ModelSerializer):
    invitation_code = serializers.CharField(write_only=True, label="邀请码")
    code = serializers.CharField(write_only=True, label="短信验证码")

    class Meta:
        model = User
        fields = ('username', 'password', 'email', 'invitation_code', 'company_name', 'phone', 'code')
        extra_kwargs = {'password': {'write_only': True}}

    def validate_invitation_code(self, code):
        try:
            inv = InvitationCode.objects.get(code=code, is_used=False)
        except InvitationCode.DoesNotExist:
            raise serializers.ValidationError("邀请码无效或已被使用")
        company = self.initial_data.get('company')
        user = self.initial_data.get('username')
        return inv

    def validate_code(self, code):
        from django.core.cache import cache
        # 验证验证码
        phone = self.initial_data.get("phone")
        cache_key = f"phone_verification_{phone}"
        cached_code = cache.get(cache_key)
        if not cached_code or cached_code != code:
            raise serializers.ValidationError("验证码错误")
        return code

    def create(self, validated_data):
        validated_data.pop('code')
        inv: InvitationCode = validated_data.pop('invitation_code')
        # 创建用户
        user = super().create(validated_data)
        # 标记邀请码已用
        inv.is_used = True
        inv.used_by = user
        password = validated_data.get("password", None)
        user.set_password(password)  # 核心：加密密码
        inv.used_at = timezone.now()
        inv.save()
        from apps.virus.models import Virus, NegotiationModel
        from apps.users.models import InvitationUserTemplate
        inv_template = InvitationUserTemplate.objects.last()
        # 配置基础病毒
        virus_obj_dic = inv_template.virus
        virus_obj_dic.pop("id", None)
        family_id = virus_obj_dic.pop("family", None)
        virus_obj_dic["family_id"] = family_id
        virus_obj_dic["created_by_id"] = user.id
        Virus.objects.create(**virus_obj_dic)
        # 配置试用谈判模型
        negotiation_obj_dic = inv_template.negotiation
        negotiation_obj_dic.pop("id", None)
        chat_id = negotiation_obj_dic.pop("chat_id", None)
        conversation_id = negotiation_obj_dic.pop("conversation_id", None)
        conversation_id = negotiation_obj_dic.pop("company_logo_url", None)
        negotiation_obj_dic["n_id"] = str(int(time.time() * 100))
        negotiation_obj_dic["created_by_id"] = user.id
        NegotiationModel.objects.create(**negotiation_obj_dic)

        # 标记该用户为-试用用户
        user.is_trial = True
        user.save()
        return user
