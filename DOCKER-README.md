# 防勒索病毒模拟演练平台 - Docker部署指南

## 项目概述

这是一个基于Docker Compose的防勒索病毒模拟演练平台，包含以下服务：

- **前端服务** (Nuxt.js): 用户界面，端口3000
- **后端服务** (Django): API服务，端口8000
- **数据库** (PostgreSQL): 数据存储，端口5432
- **缓存** (Redis): 缓存服务，端口6379
- **数据库管理** (pgAdmin): 数据库管理工具，端口5050

## 快速开始

### 1. 环境准备

确保你的系统已安装：
- Docker (版本 20.0+)
- Docker Compose (版本 2.0+)

### 2. 配置环境变量

复制并编辑环境变量文件：
```bash
cp .env.docker .env.docker.local
```

根据你的实际情况修改 `.env.docker.local` 中的配置，特别是：
- `SERVER_IP`: 你的服务器IP地址
- 数据库密码
- Redis密码
- 管理员账户信息

### 3. 启动服务

使用管理脚本启动所有服务：
```bash
./docker-manage.sh start
```

或者直接使用Docker Compose：
```bash
docker-compose --env-file .env.docker up -d
```

### 4. 访问服务

服务启动后，你可以通过以下地址访问：

- **前端界面**: http://localhost:3000
- **后端API**: http://localhost:8000
- **API文档**: http://localhost:8000/api/docs/
- **pgAdmin**: http://localhost:5050
  - 邮箱: <EMAIL>
  - 密码: admin123

## 管理脚本使用

项目提供了便捷的管理脚本 `docker-manage.sh`：

```bash
# 启动所有服务
./docker-manage.sh start

# 停止所有服务
./docker-manage.sh stop

# 重启所有服务
./docker-manage.sh restart

# 查看所有服务日志
./docker-manage.sh logs

# 查看特定服务日志
./docker-manage.sh logs backend
./docker-manage.sh logs frontend

# 查看服务状态
./docker-manage.sh status

# 清理所有资源（危险操作）
./docker-manage.sh clean

# 显示帮助信息
./docker-manage.sh help
```

## 环境变量说明

### 必需配置

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `SERVER_IP` | 服务器IP地址 | ************* |
| `POSTGRES_PASSWORD` | 数据库密码 | FW5255WA125p5Sa4pHbS6jw |
| `REDIS_PASSWORD` | Redis密码 | redis_e2SZWp6a8 |
| `ADMIN_PASSWORD` | 管理员密码 | Admin@123456 |

### 可选配置

#### 阿里云OSS存储
如需使用阿里云OSS存储文件，请配置：
- `ALIYUN_OSS_ACCESS_KEY_ID`
- `ALIYUN_OSS_ACCESS_KEY_SECRET`
- `ALIYUN_OSS_BUCKET_NAME`
- `ALIYUN_OSS_ENDPOINT`

#### 阿里云短信服务
如需使用短信功能，请配置：
- `ALIYUN_SMS_ACCESS_KEY_ID`
- `ALIYUN_SMS_ACCESS_KEY_SECRET`
- `ALIYUN_SMS_SIGN_NAME`
- `ALIYUN_SMS_TEMPLATE_CODE`

#### AI功能
如需使用AI功能，请配置：
- `AI_AUTH_TYPE`
- `AI_ACCESS_TOKEN`
- `AI_BOT_ID`
- `DASHSCOPE_API_KEY`

## 数据持久化

项目使用Docker卷来持久化数据：

- `postgres_data`: PostgreSQL数据
- `redis_data`: Redis数据
- `pgadmin_data`: pgAdmin配置
- `./media`: 上传的媒体文件
- `./static`: 静态文件
- `./logs`: 应用日志

## 故障排除

### 常见问题

1. **端口冲突**
   - 检查端口3000、8000、5432、6379、5050是否被占用
   - 修改docker-compose.yml中的端口映射

2. **服务启动失败**
   ```bash
   # 查看服务日志
   ./docker-manage.sh logs
   
   # 查看特定服务状态
   docker-compose ps
   ```

3. **数据库连接失败**
   - 检查PostgreSQL服务是否正常启动
   - 验证数据库配置是否正确

4. **前端无法访问后端API**
   - 检查CORS配置
   - 确认API地址配置正确

### 重置环境

如果遇到严重问题，可以完全重置环境：

```bash
# 停止并删除所有容器和数据
./docker-manage.sh clean

# 重新启动
./docker-manage.sh start
```

## 生产环境部署

在生产环境部署时，请注意：

1. **安全配置**
   - 修改所有默认密码
   - 设置 `DEBUG=False`
   - 配置HTTPS
   - 限制数据库和Redis的外部访问

2. **性能优化**
   - 根据服务器配置调整容器资源限制
   - 配置日志轮转
   - 使用外部数据库和缓存服务

3. **监控和备份**
   - 设置服务监控
   - 定期备份数据库
   - 配置日志收集

## 技术支持

如有问题，请查看：
- 项目文档
- Docker日志
- 应用日志文件

或联系技术支持团队。
