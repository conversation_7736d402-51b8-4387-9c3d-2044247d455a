from django.apps import AppConfig
import os


class InfectionConfig(AppConfig):
    default_auto_field = 'django.db.models.BigAutoField'
    name = 'apps.infection'

    def ready(self):
        # 注释掉自动启动RPyC服务器的代码，由supervisor管理
        # 只在主进程中启动RPyC服务器，避免在runserver的重载进程中重复启动
        # if os.environ.get('RUN_MAIN') == 'true':
        #     self.start_rpyc_server()
        pass

    def start_rpyc_server(self):
        """启动RPyC服务器"""
        try:
            from .rpyc_server import rpyc_manager
            import threading
            import time

            def delayed_start():
                # 延迟启动，确保Django完全初始化
                time.sleep(2)
                success = rpyc_manager.start_server()
                if success:
                    print("[Django] RPyC服务器自动启动成功")
                else:
                    print("[Django] RPyC服务器自动启动失败")

            # 在后台线程中启动RPyC服务器
            thread = threading.Thread(target=delayed_start, daemon=True)
            thread.start()

        except Exception as e:
            print(f"[Django] RPyC服务器启动异常: {e}")
