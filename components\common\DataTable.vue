<template>
  <div class="overflow-x-auto">
    <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
      <thead class="bg-gray-50 dark:bg-gray-800">
        <tr>
          <th scope="col" class="p-4">
            <div class="flex items-center">
              <input id="checkbox-all" type="checkbox" @change="(e) => toggleSelectAll(e)"
                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
              <label for="checkbox-all" class="sr-only">checkbox</label>
            </div>
          </th>
          <th v-for="column in columns" :key="column.key"
            class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider whitespace-nowrap"
            :style="column.width ? { width: column.width + 'px' } : {}">
            {{ column.title }}
          </th>
        </tr>
      </thead>
      <tbody class="bg-white divide-y divide-gray-200 dark:bg-gray-900 dark:divide-gray-700">
        <tr v-for="row in data" :key="row.id">
          <td class="w-4 p-4">
            <div class="flex items-center">
              <input :id="'checkbox-table-' + row.id" type="checkbox" @change="toggleSelect(row.id)"
                class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded-sm focus:ring-blue-500 dark:focus:ring-blue-600 dark:ring-offset-gray-800 dark:focus:ring-offset-gray-800 focus:ring-2 dark:bg-gray-700 dark:border-gray-600">
              <label :for="'checkbox-table-' + row.id" class="sr-only">checkbox</label>
            </div>
          </td>

          <td v-for="column in columns" :key="column.key"
            class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300">
            <!-- 如果列有插槽，使用插槽 -->
            <slot v-if="column.slot" :name="column.slot" :row="row">
              {{ row[column.key] }}
            </slot>
            <!-- 如果列有格式化函数 -->
            <template v-else-if="column.formatter">
              {{ column.formatter(row) }}
            </template>
            <!-- 默认显示 -->
            <template v-else>
              {{ row[column.key] }}
            </template>
          </td>
        </tr>
      </tbody>
    </table>

    <!-- 分页组件 -->
    <div v-if="pagination" class="px-6 py-4 bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
      <Pagination v-model:currentPage="pagination.currentPage" :pageSize="pagination.pageSize" :total="pagination.total"
        @update:currentPage="handlePageClick" />
    </div>
  </div>
</template>

<script>
import Pagination from './Pagination.vue';

export default {
  components: {
    Pagination
  },
  props: {
    columns: {
      type: Array,
      required: true
    },
    data: {
      type: Array,
      required: true
    },
    loading: {
      type: Boolean,
      default: false
    },
    pagination: {
      type: Object,
      default: () => ({
        currentPage: 1,
        pageSize: 10,
        total: 0
      })
    },
    rowSelection: {
      type: Object,
      default: () => ({
        selectedRowKeys: [],
        onChange: (selectedRowKeys) => {
          rowSelection.value.selectedRowKeys = selectedRowKeys
        }
      })
    }
  },
  emits: ['page-change', 'update:selected'],
  methods: {
    handlePageClick(page) {
      this.$emit('page-change', page);
    },
    toggleSelectAll(e) {
      const isChecked = e.target.checked;
      if (isChecked) {
        this.rowSelection.selectedRowKeys = this.data.map(row => row.id);
        this.$emit('update:selected', this.rowSelection.selectedRowKeys);
        document.querySelectorAll('table input[type="checkbox"]').forEach(checkbox => {
          checkbox.checked = true;
        });
      } else {
        this.rowSelection.selectedRowKeys = [];
        this.$emit('update:selected', []);
        document.querySelectorAll('table input[type="checkbox"]').forEach(checkbox => {
          checkbox.checked = false;
        });
      }
    },
    toggleSelect(id) {
      const index = this.rowSelection.selectedRowKeys.indexOf(id);
      if (index === -1) {
        this.rowSelection.selectedRowKeys.push(id);
      } else {
        this.rowSelection.selectedRowKeys.splice(index, 1);
      }
    }
  }
};
</script>
