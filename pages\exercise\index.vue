<template>
  <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-2xl font-bold bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent">
        {{ $t('exercise.title') }}
      </h1>
      <p class="mt-2 text-xs text-gray-500 dark:text-gray-400">
        {{ $t('exercise.subtitle') }}
      </p>
    </div>

    <!-- 页面操作按钮 -->
    <div class="flex justify-between items-center mb-6">
      <div></div>
      <div>
        <button @click="showExerciseForm = true"
          class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
            stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
          </svg>
          {{ $t('all.create') }}{{ $t('exercise.exercise') }}
        </button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow mb-6">
      <div class="p-4 border-b border-gray-200 dark:border-gray-700">
        <SearchFilter v-model="filters" :search-value="searchQuery" @update:search-value="val => searchQuery = val"
          @search="handleSearch" :show-search-button="true" :search-placeholder="$t('exercise.placeholder_search')"
          :selects="[
            {
              key: 'status',
              placeholder: $t('table.status'),
              'options': [
                { value: 'PE', label: $t('exercise.not_started') },
                { value: 'RU', label: $t('exercise.in_progress') },
                { value: 'FI', label: $t('exercise.completed') }
              ]
            }
          ]" />
      </div>

      <!-- 病毒卡片网格 -->
      <div class="p-4">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div v-for="items in exercises" :key="items.id"
            class="w-full bg-white border border-gray-200 rounded-lg shadow dark:bg-gray-800 dark:border-gray-700">
            <!-- 卡片头部 -->
            <div class="p-4 border-b border-gray-200 dark:border-gray-700">
              <div class="flex items-center justify-between mb-2">
                <div class="flex items-center space-x-2">
                  <h5 class="text-base font-bold text-gray-900 dark:text-white">{{ items.name }}</h5>
                  <span :class="[
                    'text-[10px] font-medium px-2.5 py-0.5 rounded',
                    {
                      'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-400': getActualStatus(items) === 'PE',
                      'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-400': getActualStatus(items) === 'RU',
                      'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-400': getActualStatus(items) === 'FI'
                    }
                  ]">
                    {{ getStatus(getActualStatus(items)) }}
                  </span>
                </div>
                <div class="flex items-center space-x-1">
                  <template v-if="items.virus_name">
                    <span
                      class="inline-flex items-center px-2 py-1 text-[10px] font-medium text-gray-600 bg-gray-100 rounded dark:bg-gray-700 dark:text-gray-300">
                      {{ $t('virus.virus') }}: {{ items.virus_name }}
                    </span>
                  </template>

                  <span
                    class="inline-flex items-center px-2 py-1 text-[10px] font-medium text-gray-600 bg-gray-100 rounded dark:bg-gray-700 dark:text-gray-300">
                    {{ $t('exercise.phishing') }}: {{ items.email_task_name }}
                  </span>

                  <span
                    class="inline-flex items-center px-2 py-1 text-[10px] font-medium text-gray-600 bg-gray-100 rounded dark:bg-gray-700 dark:text-gray-300">
                    {{ $t('negotiation.negotiation') }}: {{ items.negotiation_name }}
                  </span>
                </div>
              </div>
            </div>

            <div class="p-4 space-y-4">
              <!-- 时间信息 -->
              <div class="grid grid-cols-3 gap-4">
                <div class="p-3 bg-gray-50 rounded-lg dark:bg-gray-700/50">
                  <div class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {{ $t('exercise.form.start_time') }}</div>
                  <div class="text-[10px] text-gray-600 dark:text-gray-400">
                    {{ items.start_time || $t('exercise.form.not_set') }}</div>
                </div>
                <div class="p-3 bg-gray-50 rounded-lg dark:bg-gray-700/50">
                  <div class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {{ $t('exercise.form.duration') }}
                  </div>
                  <div class="text-[10px] text-gray-600 dark:text-gray-400">
                    {{ getDuration(items.start_time, items.end_time) }}</div>
                </div>
                <div class="p-3 bg-gray-50 rounded-lg dark:bg-gray-700/50">
                  <div class="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                    {{ $t('exercise.form.end_time') }}
                  </div>
                  <div class="text-[10px] text-gray-600 dark:text-gray-400">
                    {{ items.end_time || $t('exercise.form.not_set') }}</div>
                </div>
              </div>

              <!-- 进度条 -->
              <div>
                <div class="flex justify-between mb-1">
                  <span class="text-xs font-medium text-gray-700 dark:text-gray-300">
                    {{ $t('exercise.completion_progress') }}
                  </span>
                  <span class="text-xs font-medium text-gray-700 dark:text-gray-300">{{ getProgress(items) }}%</span>
                </div>
                <div class="w-full bg-gray-200 rounded-full h-2.5 dark:bg-gray-700">
                  <div :class="[
                    'h-2.5 rounded-full transition-all duration-500',
                    {
                      'bg-blue-600': getActualStatus(items) === 'PE',
                      'bg-yellow-500': getActualStatus(items) === 'RU',
                      'bg-green-600': getActualStatus(items) === 'FI'
                    }
                  ]" :style="{ width: getProgress(items) + '%' }"></div>
                </div>
              </div>

              <!-- 操作按钮 -->
              <div class="flex items-center justify-between pt-3 border-t border-gray-200 dark:border-gray-600">

                <div class="flex items-center space-x-2">
                  <NuxtLink :to="'exercise/detail/' + items.id" type="button"
                    class="inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700">
                    <Icon name="heroicons:eye" class="w-4 h-4 mr-1.5" />
                    {{ $t('table.details') }}
                  </NuxtLink>

                  <NuxtLink :to="'exercise/report/' + items.id" type="button"
                    class="cursor-pointer inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700">
                    <Icon name="heroicons:clipboard-document" class="w-4 h-4 mr-1.5 text-gray-700 dark:text-gray-300" />
                    {{ $t('exercise.exercise_report') }}
                  </NuxtLink>

                  <template v-if="getActualStatus(items) === 'FI' || getActualStatus(items) === 'RU'">
                    <NuxtLink :to="'exercise/infection?page=1&&id=' + items.id" type="button"
                      class="cursor-pointer inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700">
                      <Icon name="heroicons:clipboard-document-list"
                        class="w-4 h-4 mr-1.5 text-gray-700 dark:text-gray-300" />
                      {{ $t('exercise.infection_situation') }}
                    </NuxtLink>

                    <NuxtLink :to="'exercise/screen/' + items.id" type="button" target="_blank"
                      class="cursor-pointer inline-flex items-center px-3 py-1.5 text-xs font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-600 dark:hover:bg-gray-700">
                      <Icon name="heroicons:computer-desktop" class="w-4 h-4 mr-1.5 text-gray-700 dark:text-gray-300" />
                      {{ $t('exercise.data_screen') }}
                    </NuxtLink>
                  </template>
                </div>

                <div class="flex items-center space-x-2">

                  <div class="flex items-center space-x-2 border-l border-gray-200 dark:border-gray-600 ml-2 pl-2">
                    <!-- <template v-if="items.status === 'PE'">
                      <button @click="currentExercise = items; showExerciseForm = true; isEdit = true"
                        class="inline-flex items-center p-1.5 text-gray-500 bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      </button>
                    </template> -->

                    <template v-if="getActualStatus(items) === 'PE' || getActualStatus(items) === 'FI'">
                      <button @click="handleDelete(items)"
                        class="inline-flex items-center p-1.5 text-gray-500 bg-white rounded-lg border border-gray-200 hover:bg-gray-100 hover:text-gray-700 dark:bg-gray-800 dark:text-gray-400 dark:border-gray-600 dark:hover:text-white dark:hover:bg-gray-700">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </template>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="mt-6">
          <Pagination v-model:currentPage="currentPage" :pageSize="pageSize" :total="total"
            @update:currentPage="handlePageChange" />
        </div>
      </div>

    </div>

    <ConfirmDialog :show="showDeleteConfirm" :title="`${$t('all.delete')}${$t('exercise.exercise')}`"
      :message="deleteConfirmMessage" type="danger" @confirm="confirmDelete" @cancel="showDeleteConfirm = false" />

    <!-- 确认操作弹窗 -->
    <!-- <ConfirmDialog v-model:show="showConfirmDialog" :title="confirmDialogConfig.title"
      :message="confirmDialogConfig.message" :type="confirmDialogConfig.type"
      @confirm="confirmDialogConfig.onConfirm" /> -->

    <ExerciseFormModal v-if="showExerciseForm" :exercise="currentExercise" :isEdit="isEdit" @close="closeExerciseModal"
      @submit="handleFormSubmit" />
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { exerciseApi } from '~/api/exercises'
import { useToast } from '~/composables/useToast'
import SearchFilter from '~/components/common/SearchFilter.vue'
import Pagination from '~/components/common/Pagination.vue'
import ConfirmDialog from '~/components/common/ConfirmDialog.vue'
import ExerciseFormModal from '~/components/exercise/ExerciseFormModal.vue'
import { debounce } from 'lodash-es'

const { t } = useI18n()
const toast = useToast()

// 状态变量
const exercises = ref([])
const loading = ref(false)
const searchQuery = ref('')
const filters = ref({
  search: '',
  status: ''
})
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const showExerciseForm = ref(false)
const currentExercise = ref(null)
const isEdit = ref(false)
// const showConfirmDialog = ref(false)
// const confirmDialogConfig = ref({
//   title: '',
//   message: '',
//   type: 'warning',
//   onConfirm: () => { }
// })

const getStatus = computed(() => {
  return (value) => {
    switch (value) {
      case 'PE':
        return t('exercise.not_started')
      case 'RU':
        return t('exercise.in_progress')
      case 'FI':
        return t('exercise.completed')
    }
  }
})

// 获取实际状态（基于时间的动态状态）
const getActualStatus = (exercise) => {
  // // 如果后端状态是已完成，直接返回
  // if (exercise.status === 'FI') return 'FI'

  // // 如果没有开始时间，返回未开始
  // if (!exercise.start_time) return 'PE'

  // const now = new Date()
  // const startTime = new Date(exercise.start_time)
  // const endTime = exercise.end_time ? new Date(exercise.end_time) : null

  // // 如果当前时间还没到开始时间
  // if (now < startTime) return 'PE'

  // // 如果有结束时间且当前时间已过结束时间
  // if (endTime && now > endTime) return 'FI'

  // // 如果当前时间在开始时间之后（且在结束时间之前或没有结束时间）
  // if (now >= startTime) return 'RU'

  return exercise.status
}

// ==================== 搜索功能 ====================
// 使用防抖的搜索处理函数
const debouncedSearch = debounce((value) => {
  filters.value.search = value
  fetchExercises()
}, 300)

// 处理搜索
const handleSearch = (value) => {
  debouncedSearch(value)
}

// ==================== 删除功能 ====================
// 删除相关的状态
const showDeleteConfirm = ref(false)
const toDeleteData = ref(null)
const deleteLoading = ref(false)

// 删除确认消息
const deleteConfirmMessage = computed(() => {
  if (!toDeleteData.value) return ''
  return t('exercise.delete_message', { name: toDeleteData.value.name })
})

// 处理删除
const handleDelete = (row) => {
  toDeleteData.value = row
  showDeleteConfirm.value = true
}

// 确认删除
const confirmDelete = async () => {
  if (!toDeleteData.value?.id) return

  deleteLoading.value = toDeleteData.value.id
  try {
    await exerciseApi.deleteExercisesApi(toDeleteData.value.id)
    await fetchExercises(currentPage.value)
    toast.success(t('all.delete_success'))
  } catch (error) {
    console.error('Failed to delete family:', error)
    toast.error(t('all.delete_failed'))
  } finally {
    showDeleteConfirm.value = false
    toDeleteData.value = null
    deleteLoading.value = null
  }
}

// ==================== 页面初始化 ====================
// 获取演练列表
const fetchExercises = async (page = 1) => {
  loading.value = true
  try {
    const data = await exerciseApi.getExercisesListApi({
      page,
      page_size: pageSize.value,
      ...filters.value,
      name: filters.value.search
    })
    exercises.value = data.results
    total.value = data.count
  } catch (error) {
    toast.error(t('exercise.message_1'))
    console.error('获取演练列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 演练操作方法
const viewDetails = (exercise) => {
  navigateTo(`/exercise/${exercise.id}`)
}

// 演练操作方法
const viewReports = (exercise) => {
  navigateTo(`/exercise/report?id=${exercise.id}`)
}

const handlePageChange = (page) => {
  currentPage.value = page
  fetchExercises(page)
}

// 监听筛选条件变化
watch(filters, () => {
  currentPage.value = 1
  fetchExercises()
}, { deep: true })

// 初始化
onMounted(() => {
  fetchExercises()
})

// 添加新的计算属性
const getDuration = computed(() => {
  return (startTime, endTime) => {
    if (!startTime) return t('exercise.not_started')
    if (!endTime) return t('exercise.in_progress')

    const start = new Date(startTime)
    const end = new Date(endTime)
    const diff = end - start

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minute = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((diff % (1000 * 60)) / 1000)

    return `${days} ${t('unit.days')} ${hours} ${t('unit.hours')} ${minute} ${t('unit.minutes')} ${seconds} ${t('unit.seconds')}`
  }
})

const getProgress = computed(() => {
  return (exercise) => {
    const actualStatus = getActualStatus(exercise)

    // 如果是已完成状态，进度条显示100%
    if (actualStatus === 'FI') return 100

    // 如果是未开始状态，进度条显示0%
    if (actualStatus === 'PE') return 0

    // 如果没有开始时间，返回0
    if (!exercise.start_time) return 0

    const start = new Date(exercise.start_time)
    const end = exercise.end_time ? new Date(exercise.end_time) : new Date()
    const now = new Date()

    // 如果当前时间还没到开始时间，返回0
    if (now < start) return 0

    const total = end - start
    const current = now - start
    const progress = Math.floor((current / total) * 100)

    return Math.min(Math.max(progress, 0), 100)
  }
})

const closeExerciseModal = () => {
  showExerciseForm.value = false
  currentExercise.value = null
  isEdit.value = false
}

// 处理表单提交
const handleFormSubmit = async (formData) => {
  try {
    const params = formData

    if (isEdit.value) {
      // 更新
      await exerciseApi.updateExercisesApi(currentExercise.value.id, params)
      toast.success(t('all.update_success'))
    } else {
      // 创建
      await exerciseApi.createExercisesApi(params)
      toast.success(t('all.create_success'))
    }
    closeExerciseModal()
    fetchExercises()
  } catch (error) {
    console.error('演练操作失败:', error)
    // 错误信息已经在 useApi 中处理和显示了，这里只需要记录日志
    // 不需要再次显示错误信息，避免重复提示
  }
}
</script>