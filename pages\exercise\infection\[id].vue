<template>
  <div class="max-w-7xl mx-auto px-4">
    <!-- 设备信息 -->
    <DeviceInfo :device-id="deviceId" :is-online="isOnline" :current-infection="currentInfection" />

    <!-- 操作功能 -->
    <OperationPanel :device-id="deviceId" :exercise-id="exerciseId" v-model:loading="loading" @command-executed="handleCommandExecuted" />

    <!-- 命令执行历史 -->
    <CommandHistory ref="commandHistoryRef" :device-id="deviceId" :exercise-id="exerciseId"/>

    <!-- 感染历史记录 -->
    <InfectionHistory :device-id="deviceId" :exercise-id="exerciseId" v-model:current-infection="currentInfection" />
  </div>
</template>

<script setup>
import { infectionApi } from '~/api/infection'
import DeviceInfo from '~/components/infection/DeviceInfo.vue'
import OperationPanel from '~/components/infection/OperationPanel.vue'
import CommandHistory from '~/components/infection/CommandHistory.vue'
import InfectionHistory from '~/components/infection/InfectionHistory.vue'

const route = useRoute()
const deviceId = route.params.id
const exerciseId = route.query.exercise_id

// 状态管理
const loading = ref(false)
const currentInfection = ref(null)
const isOnline = ref(false)
const statusTimer = ref(null)

// 组件引用
const commandHistoryRef = ref(null)

// 检查设备在线状态
const checkOnlineStatus = async () => {
  try {
    const response = await infectionApi.executeCommand({
      command: 'all',
      args: {}
    })
    const onlineDevices = response.response?.data || []
    isOnline.value = onlineDevices.includes(deviceId)
  } catch (error) {
    console.error('检查设备在线状态失败:', error)
    isOnline.value = false
  }
}

// 处理命令执行完成事件
const handleCommandExecuted = () => {
  // 刷新命令历史
  if (commandHistoryRef.value) {
    commandHistoryRef.value.refreshHistory()
  }
  // 同时检查设备在线状态
  checkOnlineStatus()
}

// 初始化
onMounted(() => {
  checkOnlineStatus()

  // 设置10秒自动检查在线状态
  statusTimer.value = setInterval(async () => {
    await checkOnlineStatus()
  }, 10000)
})

// 组件卸载时清除定时器
onUnmounted(() => {
  if (statusTimer.value) {
    clearInterval(statusTimer.value)
    statusTimer.value = null
  }
})
</script>
