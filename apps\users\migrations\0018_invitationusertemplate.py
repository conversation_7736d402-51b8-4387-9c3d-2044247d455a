# Generated by Django 5.1.3 on 2025-07-30 11:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0017_invitationcode"),
    ]

    operations = [
        migrations.CreateModel(
            name="InvitationUserTemplate",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                ("virus", models.JSONField(default=dict, verbose_name="病毒配置")),
                (
                    "negotiation",
                    models.JSONField(default=dict, verbose_name="谈判记录"),
                ),
                (
                    "asset_count",
                    models.IntegerField(default=1, verbose_name="资产数量"),
                ),
            ],
            options={
                "verbose_name": "邀请码管理",
                "verbose_name_plural": "邀请码管理",
                "db_table": "ls_invitation_template",
                "ordering": ["-created_at"],
            },
        ),
    ]
