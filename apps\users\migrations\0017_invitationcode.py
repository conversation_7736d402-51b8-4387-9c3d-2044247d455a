# Generated by Django 5.1.3 on 2025-07-29 15:42

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ("users", "0016_user_is_trial"),
    ]

    operations = [
        migrations.CreateModel(
            name="InvitationCode",
            fields=[
                (
                    "id",
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name="ID",
                    ),
                ),
                (
                    "created_at",
                    models.DateTimeField(auto_now_add=True, verbose_name="创建时间"),
                ),
                (
                    "updated_at",
                    models.DateTimeField(auto_now=True, verbose_name="更新时间"),
                ),
                ("code", models.CharField(max_length=100, verbose_name="邀请码")),
                ("company", models.CharField(blank=True, max_length=255, null=True)),
                ("is_used", models.BooleanField(default=False)),
                (
                    "used_at",
                    models.DateTimeField(
                        blank=True, null=True, verbose_name="使用时间"
                    ),
                ),
                (
                    "used_by",
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name="invitations",
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                "verbose_name": "邀请码管理",
                "verbose_name_plural": "邀请码管理",
                "db_table": "ls_invitations",
                "ordering": ["-created_at"],
            },
        ),
    ]
