<template>
  <div class="min-h-screen bg-gray-50 dark:bg-gray-900">
    <!-- 顶部导航栏 -->
    <nav
      class="fixed top-0 z-50 w-full bg-white/80 backdrop-blur-xl border-b border-gray-200 dark:bg-gray-800/80 dark:border-gray-700">
      <div class="px-6 py-4 lg:px-8">
        <div class="flex items-center justify-between">
          <div class="flex items-center justify-start">
            <button @click="toggleSidebar" type="button"
              class="inline-flex items-center p-2 text-blue-600 bg-gradient-to-br from-blue-100 to-blue-50 hover:from-blue-200 hover:to-blue-100 rounded-xl dark:from-blue-500/20 dark:to-blue-600/20 dark:hover:from-blue-500/30 dark:hover:to-blue-600/30 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200"
              aria-controls="logo-sidebar" aria-expanded="false">
              <span class="sr-only">切换侧边栏</span>
              <Icon name="heroicons:bars-3" class="w-6 h-6" />
            </button>
            <a href="/" class="flex items-center ml-4 md:mr-24">
              <div class="relative bg-gradient-to-br from-blue-600 to-blue-400 rounded-lg p-2 mr-3 group w-12 min-w-12">
                <img v-if="settings?.logo" :src="settings.logo"
                  class="w-8 h-8 object-contain transform group-hover:scale-110 transition-transform duration-200"
                  :alt="settings?.systemName || '系统Logo'">
                <Icon v-else name="heroicons:shield-check"
                  class="w-8 h-8 text-white transform group-hover:scale-110 transition-transform duration-200" />
                <div
                  class="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800">
                </div>
              </div>
              <div>
                <span
                  class="self-center text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300 bg-clip-text text-transparent">
                  {{ settings?.systemName || $t('header.title') }}
                </span>
                <p class="text-sm text-blue-600 dark:text-blue-400 font-medium">Enterprise Edition</p>
              </div>
            </a>
          </div>
          <div class="flex items-center gap-4">
            <!-- 通知按钮 -->
            <div class="relative">
              <NotificationButton :count="unreadCount" @click="handleNotificationClick" />
            </div>

            <div>
              <button v-if="language === 'zh'" @click="setLocale('en'); language = 'en'"
                class="inline-flex items-center justify-center w-10 h-10 text-blue-600 bg-gradient-to-br from-blue-100 to-blue-50 hover:from-blue-200 hover:to-blue-100 rounded-xl dark:from-blue-500/20 dark:to-blue-600/20 dark:hover:from-blue-500/30 dark:hover:to-blue-600/30 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200">
                <span class="text-sm font-semibold">EN</span>
              </button>

              <button v-else @click="setLocale('zh'); language = 'zh'"
                class="inline-flex items-center justify-center w-10 h-10 text-blue-600 bg-gradient-to-br from-blue-100 to-blue-50 hover:from-blue-200 hover:to-blue-100 rounded-xl dark:from-blue-500/20 dark:to-blue-600/20 dark:hover:from-blue-500/30 dark:hover:to-blue-600/30 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-all duration-200">
                <span class="text-sm font-semibold">中</span>
              </button>
            </div>

            <!-- 主题切换按钮 -->
            <ThemeToggle />

            <!-- 用户下拉菜单 -->
            <div class="relative user-menu">
              <button type="button" @click="toggleUserMenu"
                class="flex items-center gap-3 p-2 pr-4 bg-gradient-to-br from-purple-100 to-blue-50 hover:from-purple-200 hover:to-blue-100 rounded-xl dark:from-purple-500/20 dark:to-blue-600/20 dark:hover:from-purple-500/30 dark:hover:to-blue-600/30 transition-all duration-200">
                <div class="flex items-center gap-2">
                  <div class="relative">
                    <img
                      :src="userAvatar"
                      class="w-8 h-8 rounded-lg ring-2 ring-purple-500/30" :alt="displayUsername">
                    <div
                      class="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white dark:border-gray-800">
                    </div>
                  </div>
                  <div class="flex flex-col items-start">
                    <span class="text-sm font-medium text-gray-900 dark:text-white">
                      {{ displayUsername }}
                    </span>
                    <span class="text-xs text-purple-600 dark:text-purple-400" v-if="userInfo">
                      {{ displayUserRole }}
                    </span>
                  </div>
                </div>
                <Icon name="heroicons:chevron-down" class="w-4 h-4 text-gray-600 dark:text-gray-400" />
              </button>

              <!-- 下拉菜单 -->
              <div v-if="isUserMenuOpen"
                class="absolute right-0 mt-2 w-56 rounded-xl bg-white shadow-lg ring-1 ring-black ring-opacity-5 dark:bg-gray-800 dark:ring-gray-700 z-50">
                <div class="py-1">
                  <NuxtLink to="/profile" @click="isUserMenuOpen = false"
                    class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700/50">
                    <Icon name="heroicons:user" class="w-4 h-4 mr-3 text-gray-500 dark:text-gray-400" />
                    {{ $t('profile.title') }}
                  </NuxtLink>
                  <template v-if="userInfo?.is_superuser">
                    <NuxtLink to="/settings" @click="isUserMenuOpen = false"
                      class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700/50">
                      <Icon name="heroicons:cog-6-tooth" class="w-4 h-4 mr-3 text-gray-500 dark:text-gray-400" />
                      {{ $t('settings.title') }}
                    </NuxtLink>
                  </template>
                  <hr class="my-1 border-gray-200 dark:border-gray-700">
                  <button @click="authStore.logout"
                    class="w-full flex items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100 dark:text-red-400 dark:hover:bg-gray-700/50">
                    <Icon name="heroicons:arrow-right-on-rectangle" class="w-4 h-4 mr-3" />
                    {{ $t('header.logout') }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </nav>

    <!-- 侧边栏 -->
    <aside id="logo-sidebar" :class="[
      'fixed top-0 left-0 z-40 w-72 h-screen pt-20 transition-transform bg-white/80 backdrop-blur-xl border-r border-gray-200 dark:bg-gray-800/80 dark:border-gray-700',
      isSidebarOpen ? 'translate-x-0' : '-translate-x-full'
    ]" aria-label="Sidebar">
      <div class="h-full px-4 pb-4 overflow-y-auto flex flex-col">
        <SidebarMenu class="flex-grow" />
        <!-- 版权信息 -->
        <div class="pt-4 mt-4 border-t border-gray-200 dark:border-gray-700">
          <p class="text-sm text-gray-500 dark:text-gray-400">
            {{ settings?.copyright || `© ${new Date().getFullYear()} 勒索病毒模拟演练平台. 保留所有权利.` }}
          </p>
        </div>
      </div>
    </aside>

    <!-- 主内容区域 -->
    <div :class="[
      'transition-all duration-200',
      isSidebarOpen ? 'ml-72' : 'ml-0',
      'pt-20'
    ]">
      <div class="px-6 py-4 max-w-7xl mx-auto">
        <slot />
      </div>
    </div>
  </div>
</template>

<script setup>
import { useAuthStore } from '~/stores/auth'
import { useSystemSettings } from '~/composables/useSystemSettings'
import ThemeToggle from '~/components/ThemeToggle.vue'
import NotificationButton from '~/components/common/NotificationButton.vue'
import { notificationApi } from '@/api/notification'

const { setLocale, t } = useI18n() // 获取国际化设置的方法
const authStore = useAuthStore() // 获取认证状态的store
const { settings } = useSystemSettings() // 获取系统设置
const userInfo = computed(() => authStore.user) // 计算用户信息
const isSidebarOpen = ref(window?.innerWidth >= 1024) // 控制侧边栏是否打开
const isUserMenuOpen = ref(false) // 控制用户菜单是否打开
const language = ref(useI18n().locale.value) // 当前语言
const unreadCount = ref(0) // 未读通知数量

// 用户显示名称的计算属性
const displayUsername = computed(() => {
  return userInfo.value?.username || t('header.visitor')
})

// 用户角色显示的计算属性
const displayUserRole = computed(() => {
  if (!userInfo.value) return ''
  return userInfo.value.role === 'SA' ? t('header.super') : t('header.admin')
})

// 用户头像的计算属性
const userAvatar = computed(() => {
  const username = userInfo.value?.username || 'default'
  return userInfo.value?.avatar || `https://api.dicebear.com/6.x/avataaars/svg?seed=${username}`
})

const toggleSidebar = () => {
  isSidebarOpen.value = !isSidebarOpen.value // 切换侧边栏的显示状态
}

const toggleUserMenu = () => {
  isUserMenuOpen.value = !isUserMenuOpen.value // 切换用户菜单的显示状态
}

const closeUserMenu = (e) => {
  const target = e.target
  if (!target.closest('.user-menu')) {
    isUserMenuOpen.value = false // 点击菜单外部时关闭用户菜单
  }
}

const handleResize = () => {
  if (window.innerWidth >= 1024) {
    isSidebarOpen.value = true // 窗口宽度大于等于1024时，侧边栏打开
  } else {
    isSidebarOpen.value = false // 窗口宽度小于1024时，侧边栏关闭
  }
}

const handleNotificationClick = () => {
  // 打开通知页面
  navigateTo('/notify')
}

const fetchUnreadCount = async () => {
  try {
    const response = await notificationApi.getUnreadCount() // 获取未读通知数量
    unreadCount.value = response.count // 更新未读通知数量
  } catch (error) {
    console.error('获取未读通知数量失败:', error) // 处理错误
  }
}

const intervalId = setInterval(fetchUnreadCount, 10000) // 每10秒获取一次未读通知数量

// 监听认证状态变化
watch(() => authStore.isAuthenticated, async (newValue, oldValue) => {
  if (newValue && !oldValue) {
    // 从未认证变为已认证，获取用户信息
    if (!authStore.user) {
      try {
        await authStore.fetchUserInfo()
      } catch (error) {
        console.error('获取用户信息失败:', error)
      }
    }
  } else if (!newValue && oldValue) {
    // 从已认证变为未认证，清除用户菜单状态
    isUserMenuOpen.value = false
  }
})

onMounted(async () => {
  document.addEventListener('click', closeUserMenu) // 添加点击事件监听器以关闭用户菜单
  window.addEventListener('resize', handleResize) // 添加窗口大小变化事件监听器

  // 检查认证状态（简化版，避免重复检查）
  if (!authStore.isAuthenticated) {
    navigateTo('/login') // 如果未认证，跳转到登录页面
    return
  }

  // 确保用户信息已加载
  if (!authStore.user && authStore.isAuthenticated) {
    try {
      await authStore.fetchUserInfo()
    } catch (error) {
      console.error('获取用户信息失败:', error)
      // 如果获取用户信息失败，可能是token过期，尝试重新认证
      if (!authStore.isAuthenticated) {
        navigateTo('/login')
        return
      }
    }
  }

  fetchUnreadCount() // 获取未读通知数量
})

onUnmounted(() => {
  document.removeEventListener('click', closeUserMenu) // 移除点击事件监听器
  window.removeEventListener('resize', handleResize) // 移除窗口大小变化事件监听器
  clearInterval(intervalId) // 清除定时器
})
</script>

<style>
/* 添加全局过渡效果 */
.page-enter-active,
.page-leave-active {
  transition: all 0.3s ease-in-out;
}

.page-enter-from,
.page-leave-to {
  opacity: 0;
  transform: translateY(20px);
}

/* 添加侧边栏过渡效果 */
.transition-transform {
  transition-property: transform, margin;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 300ms;
}
</style>
