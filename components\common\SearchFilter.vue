<template>
  <div class="flex flex-wrap gap-4">
    <!-- 搜索输入框 -->
    <div class="flex-1 min-w-[200px] relative">
      <input type="text" :value="searchValue" @input="$emit('update:searchValue', $event.target.value)"
        @keyup.enter="$emit('search', searchValue)" :placeholder="searchPlaceholder"
        class="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white" />
      <!-- 清空按钮 -->
      <button v-if="searchValue" @click="handleClearSearch" type="button"
        class="absolute inset-y-0 right-0 flex items-center pr-3 text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300">
        <Icon name="heroicons:x-mark" class="w-5 h-5" />
      </button>
    </div>

    <!-- 筛选下拉框 -->
    <div v-for="select in selects" :key="select.key" class="w-36">
      <select :value="modelValue[select.key]" @change="handleSelectChange(select.key, $event)"
        class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
        <option value="">{{ select.placeholder }}</option>
        <option v-for="option in select.options" :key="option.value" :value="option.value">
          {{ option.label }}
        </option>
      </select>
    </div>

    <!-- 按钮组 -->
    <div class="flex gap-2">
      <button @click="$emit('search', searchValue)"
        class="px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        {{ $t('all.search') }}
      </button>

      <template v-if="showDeleteButton">
        <button @click="$emit('delete-all')"
          class="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
          {{ $t('all.delete_all') }}
        </button>
      </template>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  },
  searchValue: {
    type: String,
    default: ''
  },
  searchPlaceholder: {
    type: String,
    default: '搜索'
  },
  showSearchButton: {
    type: Boolean,
    default: false
  },
  searchButtonText: {
    type: String,
    default: '搜索'
  },
  selects: {
    type: Array,
    default: () => []
  },
  showDeleteButton: {
    type: Boolean,
    default: false
  },
})

const emit = defineEmits(['update:modelValue', 'update:searchValue', 'search', 'delete-all'])

const handleSelectChange = (key, event) => {
  emit('update:modelValue', {
    ...props.modelValue,
    [key]: event.target.value
  })
}

const handleClearSearch = () => {
  // 清空搜索输入框
  emit('update:searchValue', '')
  // 触发搜索以更新结果
  emit('search', '')
}

const handleReset = () => {
  // 重置所有下拉框的值
  const resetModelValue = {}
  props.selects.forEach(select => {
    resetModelValue[select.key] = ''
  })

  // 同时更新所有值，只触发一次搜索
  emit('update:modelValue', resetModelValue)
  emit('update:searchValue', '')
}

const handleSearch = () => {
  emit('search', props.searchValue)
}

const handleSearchInput = (e) => {
  emit('update:searchValue', e.target.value)
}
</script>