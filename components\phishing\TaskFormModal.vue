<template>
  <div class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
      <!-- 背景遮罩 -->
      <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" aria-hidden="true"></div>

      <!-- Modal面板 -->
      <div
        class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg md:max-w-2xl sm:w-full">
        <div class="absolute right-0 top-0 pr-4 pt-4">
          <button type="button" @click="$emit('close')"
            class="rounded-md bg-white dark:bg-gray-800 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2">
            <span class="sr-only">关闭</span>
            <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="handleSubmit">
          <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
            <div class="mb-4">
              <h3 class="text-lg font-medium leading-6 text-gray-900">
                {{ task ? $t('all.edit') : $t('all.create') }}{{ $t('task.task') }}
              </h3>
            </div>

            <div class="space-y-4">
              <!-- 任务名称 -->
              <div>
                <Input :label="$t('table.task_name')" :placeholder="$t('task.form.placeholder_task_name')"
                  :error="$t('task.form.placeholder_task_name')" required name="name" id="name" v-model="form.name" />
              </div>

              <div>
                <Select :label="$t('table.email_templates')" :placeholder="$t('task.form.placeholder_email_templates')"
                  :error="$t('task.form.placeholder_email_templates')" required name="email_template"
                  id="email_template" v-model="form.email_template" :options="templateList"
                  :fieldNames="{ label: 'name', value: 'id' }" @focus="getTemplateList()" />
              </div>

              <div>
                <Select :label="$t('table.sending_strategy')"
                  :placeholder="$t('task.form.placeholder_sending_strategy')"
                  :error="$t('task.form.placeholder_sending_strategy')" required name="strategy" id="strategy"
                  v-model="form.strategy" :options="strategyList" :fieldNames="{ label: 'name', value: 'id' }"
                  @focus="getStrategyList()" />
              </div>

              <div>
                <Select :label="$t('table.phishing_page')" :placeholder="phishingPagePlaceholder"
                  name="phishing_page" id="phishing_page" v-model="form.phishing_page" :options="pageList"
                  :fieldNames="{ label: 'name', value: 'id' }" @focus="getPageList()" :disabled="isPhishingPageDisabled"
                  :helpText="isPhishingPageDisabled ? '已填写钓鱼链接，无法选择钓鱼页面' : ''" />
              </div>

              <div>
                <Input :label="$t('task.form.phishing_link')" :placeholder="phishingLinkPlaceholder"
                  :error="$t('task.form.placeholder_phishing_link')" name="phishing_link" id="phishing_link"
                  v-model="form.phishing_link" :disabled="isPhishingLinkDisabled"
                  :helpText="phishingLinkHelpText" />
              </div>

              <!-- 钓鱼目标选择提示 -->
              <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-start">
                  <div class="flex-shrink-0">
                    <svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <div class="ml-3">
                    <h4 class="text-sm font-medium text-blue-800">钓鱼目标选择说明</h4>
                    <div class="mt-1 text-sm text-blue-700">
                      <p>请选择以下其中一种方式作为钓鱼目标：</p>
                      <ul class="mt-2 list-disc list-inside space-y-1">
                        <li><strong>钓鱼页面</strong>：选择预设的钓鱼页面模板</li>
                        <li><strong>钓鱼链接</strong>：直接输入自定义的钓鱼链接地址</li>
                      </ul>
                      <p class="mt-2 text-xs text-blue-600">
                        💡 两种方式互斥，选择其中一种后另一种将自动禁用
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 当前选择状态提示 -->
              <div v-if="form.phishing_page || form.phishing_link" class="bg-green-50 border border-green-200 rounded-lg p-3">
                <div class="flex items-center">
                  <svg class="h-4 w-4 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                  </svg>
                  <span class="text-sm text-green-700">
                    <template v-if="form.phishing_page">
                      已选择钓鱼页面作为目标
                    </template>
                    <template v-else-if="form.phishing_link">
                      已填写钓鱼链接作为目标
                    </template>
                  </span>
                </div>
              </div>

              <div class="h-6"></div>
            </div>
          </div>

          <!-- 按钮组 -->
          <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
            <button type="submit" :disabled="!isFormValid && !task"
              class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed">
              {{ task ? $t('all.save') : $t('all.create') }}
            </button>
            <button type="button" @click="$emit('close')"
              class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
              {{ $t('all.cancel') }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import Input from '~/components/common/Input.vue'
import Select from '~/components/common/Select.vue'
import { phishingApi } from '~/api/phishing'

const props = defineProps({
  task: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['close', 'submit'])

// 表单数据
const form = ref({
  name: props.task?.name || '',
  email_template: props.task?.email_template || '',
  phishing_page: props.task?.phishing_page || '',
  strategy: props.task?.strategy || '',
  phishing_link: props.task?.phishing_link || ''
})

// ========================= 互斥选择逻辑 =========================
// 计算钓鱼页面是否禁用（当钓鱼链接有值时禁用）
const isPhishingPageDisabled = computed(() => {
  return !!form.value.phishing_link
})

// 计算钓鱼链接是否禁用（当钓鱼页面有值时禁用）
const isPhishingLinkDisabled = computed(() => {
  return !!form.value.phishing_page
})

// 钓鱼页面的动态占位符文本
const phishingPagePlaceholder = computed(() => {
  if (isPhishingPageDisabled.value) {
    return '已填写钓鱼链接，无法选择钓鱼页面'
  }
  // 使用原始的占位符文本
  return '请选择钓鱼页面'
})

// 钓鱼链接的动态占位符文本
const phishingLinkPlaceholder = computed(() => {
  if (isPhishingLinkDisabled.value) {
    return '已选择钓鱼页面，无法填写钓鱼链接'
  }
  // 使用原始的占位符文本
  return '请输入钓鱼链接'
})

// 钓鱼链接的帮助文本
const phishingLinkHelpText = computed(() => {
  if (isPhishingLinkDisabled.value) {
    return '已选择钓鱼页面，无法填写钓鱼链接'
  }
  return '若值为空，则使用默认地址'
})

// 监听钓鱼页面变化，当有值时清空钓鱼链接
watch(() => form.value.phishing_page, (newValue) => {
  if (newValue && form.value.phishing_link) {
    form.value.phishing_link = ''
  }
})

// 监听钓鱼链接变化，当有值时清空钓鱼页面
watch(() => form.value.phishing_link, (newValue) => {
  if (newValue && form.value.phishing_page) {
    form.value.phishing_page = ''
  }
})

// ========================= 获取数据源 =========================
const templateList = ref([])
const pageList = ref([])
const strategyList = ref([])

const getTemplateList = () => phishingApi.getEmailTemplateListApi({ page_size: 100 }).then(res => {
  templateList.value = res?.results
})

const getPageList = () => phishingApi.getPhishingPageListApi({ page_size: 100 }).then(res => {
  pageList.value = res?.results
})

const getStrategyList = () => phishingApi.getStrategyListApi({ page_size: 100 }).then(res => {
  strategyList.value = res?.results
})

onMounted(() => {
  if (props.isEdit) {
    getTemplateList()
    getPageList()
    getStrategyList()
  }
})

// 修改表单验证逻辑
const isFormValid = computed(() => {
  // 检查必填字段（除了钓鱼页面和钓鱼链接）
  const requiredFieldsValid = Object.keys(form.value).every(key => {
    // 钓鱼页面和钓鱼链接是互斥的，不在这里验证
    if (key === 'phishing_page' || key === 'phishing_link') {
      return true;
    }
    // 其他字段必须有值
    return !!form.value[key];
  });

  // 确保钓鱼页面和钓鱼链接至少选择其中一个
  const phishingTargetValid = !!(form.value.phishing_page || form.value.phishing_link);

  return requiredFieldsValid && phishingTargetValid;
});

// 提交表单
const handleSubmit = () => {
  emit('submit', { ...form.value })
}
</script>