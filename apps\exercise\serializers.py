from django_q.models import Schedule
from drf_writable_nested import WritableNestedModelSerializer
from rest_framework import serializers

from extensions import constants
from .models import Exercise
from django_q.tasks import async_task, schedule
from apps.assets.serializers import AssetSerializer, AssetGroupSerializer
from apps.virus.serializers import VirusSerializer
from django.utils import timezone
from django.core.exceptions import ValidationError
from drf_spectacular.utils import extend_schema_field

from ..assets.models import Asset
from ..infection.models import Device, InfectionRecord


class ExerciseSerializer(WritableNestedModelSerializer):
    """演练项目序列化器"""

    email_task_name = serializers.SerializerMethodField()
    virus_name = serializers.SerializerMethodField()
    negotiation_name = serializers.SerializerMethodField()
    target_group_names = serializers.SerializerMethodField()

    class Meta:
        model = Exercise
        fields = ["id", "name", "virus", "email_task", "negotiation", "target_groups",
                  "status", "target_asset", "start_time", "end_time", 
                  "email_task_name", "virus_name", "negotiation_name", "target_group_names"]
        read_only_fields = ('created_by', 'created_at', 'updated_at')

    def get_email_task_name(self, obj):
        """获取邮件任务名称"""
        return obj.email_task.name if obj.email_task else None
    
    def get_virus_name(self, obj):
        """获取病毒名称"""
        return obj.virus.name if obj.virus else None
    
    def get_negotiation_name(self, obj):
        """获取谈判配置名称"""
        return obj.negotiation.platform_name if obj.negotiation else None

    def get_target_group_names(self, obj):
        """获取目标资产组名称列表"""
        return [group.name for group in obj.target_groups.all()]

    def create(self, validated_data):
        from django.utils import timezone

        user = self.context["request"].user
        validated_data["created_by"] = user

        # 校验 - 检查真正活跃的演练（考虑时间因素）
        now = timezone.now()

        # 获取状态为待开始或进行中的演练
        potential_active_exercises = Exercise.objects.filter(status__in=["PE", "RU"])

        # 进一步过滤：排除已经过了结束时间的演练
        active_exercises = []
        for exercise in potential_active_exercises:
            # 如果演练没有结束时间，或者当前时间还没超过结束时间，则认为是活跃的
            if not exercise.end_time or now <= exercise.end_time:
                active_exercises.append(exercise.id)

        # 如果没有真正活跃的演练，就不需要检查冲突
        if not active_exercises:
            pass  # 继续创建演练
        else:
            # 当前要插入的所有资产ID
            target_groups = validated_data.get("target_groups", [])
            target_assets = Asset.objects.filter(group__in=target_groups)
            target_asset_ids = target_assets.values_list('id', flat=True)

            conflict_assets = Asset.objects.filter(
                group__groups_exercises__id__in=active_exercises,
                id__in=target_asset_ids
            ).distinct()

            if conflict_assets.exists():
                conflict_asset_names = conflict_assets.values_list('name', flat=True)
                raise serializers.ValidationError(
                    f"以下资产正在其他演练中，无法重复创建: {','.join(conflict_asset_names)}"
                )
        instance = super().create(validated_data)
        task = schedule(
            'apps.exercise.tasks.send_exercise_emails',
            instance.id,
            schedule_type=Schedule.ONCE,
            next_run=instance.start_time,
            name=f'exercise_send_email_{instance.id}'
        )
        instance.task_id = task.id
        instance.save()
        devices_data = Asset.objects.filter(
            asset_type__in=('EP', 'SV', 'NW'),
            group__in=instance.target_groups.all()  # 过滤出属于演练的目标资产组
        ).values('name', 'username', 'os_type', 'ip_address_v4', 'mac_address')  # 获取所有资产的设备名称
        devices_to_create = []
        infection_records = []
        for data in devices_data:
            device = Device(
                exercise=instance,
                hostname=data['name'],
                device_id=None,  # 设备ID暂时为空
                infection_count=0,  # 初始感染次数为0
                mac_address=data['mac_address']
            )
            # infection_record = InfectionRecord(
            #     exercise=instance,
            #     status='OF',
            #     device=device,
            #     hostname=data['name'],
            #     username=data['username'],
            #     ip_address=data['ip_address_v4'],
            #     system_version=constants.ASSET_OS_TO_TYPE.get(data['os_type'], ''),
            # )
            devices_to_create.append(device)
            # infection_records.append(infection_record)
        Device.objects.bulk_create(devices_to_create)
        # InfectionRecord.objects.bulk_create(infection_records)
        return instance


class ExerciseCreateSerializer(serializers.ModelSerializer):
    """演练项目创建序列化器"""
    target_asset_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        write_only=True,
        help_text="目标资产ID列表"
    )
    target_group_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        write_only=True,
        help_text="目标资产组ID列表"
    )
    virus_id = serializers.IntegerField(
        required=True,
        write_only=True,
        help_text="使用的病毒样本ID"
    )

    class Meta:
        model = Exercise
        fields = [
            'name', 'description', 'virus_id',
            'target_asset_ids', 'target_group_ids',
            'start_time', 'end_time'
        ]

    def validate(self, data):
        """验证演练数据"""
        # 验证时间
        start_time = data.get('start_time')
        end_time = data.get('end_time')
        now = timezone.now()

        if start_time and end_time:
            if start_time >= end_time:
                raise ValidationError("结束时间必须晚于开始时间")
            if start_time < now:
                raise ValidationError("开始时间不能早于当前时间")

        # 验证目标资产
        target_asset_ids = data.get('target_asset_ids', [])
        target_group_ids = data.get('target_group_ids', [])
        if not target_asset_ids and not target_group_ids:
            raise ValidationError("必须指定至少一个目标资产或资产组")

        return data

    def create(self, validated_data):
        """创建演练项目"""
        # 提取ID列表
        asset_ids = validated_data.pop('target_asset_ids', [])
        group_ids = validated_data.pop('target_group_ids', [])
        virus_id = validated_data.pop('virus_id')

        # 创建演练项目
        validated_data['virus_id'] = virus_id
        exercise = Exercise.objects.create(**validated_data)

        # 设置多对多关系
        if asset_ids:
            exercise.target_assets.set(asset_ids)
        if group_ids:
            exercise.target_groups.set(group_ids)

        return exercise


class ExerciseDetailSerializer(ExerciseSerializer):
    """演练项目详细信息序列化器"""
    virus = VirusSerializer(read_only=True)  # 嵌套序列化病毒信息
    total_assets = serializers.SerializerMethodField()  # 总资产数
    infected_assets = serializers.SerializerMethodField()  # 已感染资产数
    progress = serializers.SerializerMethodField()  # 演练进度

    class Meta(ExerciseSerializer.Meta):
        fields = '__all__'

    @extend_schema_field(int)
    def get_total_assets(self, obj: Exercise) -> int:
        """计算总资产数（包括资产组中的资产）"""
        direct_assets = obj.target_assets.count()
        group_assets = sum(group.assets.count() for group in obj.target_groups.all())
        return direct_assets + group_assets

    @extend_schema_field(int)
    def get_infected_assets(self, obj: Exercise) -> int:
        """计算已感染的资产数"""
        return 0  # 由于移除了日志功能，这里返回0

    @extend_schema_field(float)
    def get_progress(self, obj: Exercise) -> float:
        """计算演练进度（0-100）"""
        if obj.status == Exercise.Status.PENDING:
            return 0
        elif obj.status == Exercise.Status.FINISHED:
            return 100
        return 0  # 由于移除了日志功能，这里返回0
