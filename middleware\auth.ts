import { useAuthStore } from '~/stores/auth'

export default defineNuxtRouteMiddleware(async (to) => {
  const authStore = useAuthStore()
  const token = useCookie('token')

  // 如果是登录页面且已经认证，重定向到仪表盘
  if (to.path === '/login' && authStore.isAuthenticated) {
    return navigateTo('/dashboard')
  }

  // 如果没有token且不是登录页面，重定向到登录页
  if (!token.value && to.path !== '/login') {
    return navigateTo('/login')
  }

  // 如果有token但未认证，尝试验证token（避免并发检查）
  if (token.value && !authStore.isAuthenticated && !authStore.isCheckingAuth) {
    const isValid = await authStore.checkAuth()
    if (!isValid && to.path !== '/login') {
      return navigateTo('/login')
    }
  }
})
