<template>
  <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative"
    style="background: linear-gradient(135deg, #f0f4f8 0%, #e2e8f0 50%, #f8fafc 100%)">
    <!-- Back to Home Link -->
    <div class="absolute top-4 left-4">
      <NuxtLink to="/"
        class="flex items-center text-gray-700 hover:text-gray-900 transition-colors backdrop-blur-sm bg-white/80 px-3 py-2 rounded-lg shadow-sm">
        <Icon name="heroicons:arrow-left" class="w-5 h-5 mr-2" />
        {{ $t('all.back') }}
      </NuxtLink>
    </div>

    <div
      class="max-w-md w-full space-y-8 bg-white/95 backdrop-blur-lg p-8 rounded-xl shadow-2xl border border-white/20">
      <div>
        <h2 class="text-center text-2xl font-bold tracking-tight text-gray-900">
          {{ settings?.systemName || ($t('home.first.title_1') + ' ' + $t('home.first.title_2')) }}
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          {{ $t('login.subtitle') }}
        </p>
      </div>

      <div class="mb-4 border-b border-gray-200">
        <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" role="tablist">
          <li class="mr-2" role="presentation">
            <button class="inline-block p-4 border-b-2 rounded-t-lg" :class="[
              activeTab === 'account'
                ? 'text-blue-600 border-blue-600'
                : 'hover:text-gray-600 hover:border-gray-300 border-transparent'
            ]" type="button" role="tab" @click="activeTab = 'account'">
              {{ $t('login.login_1') }}
            </button>
          </li>
          <li class="mr-2" role="presentation">
            <button class="inline-block p-4 border-b-2 rounded-t-lg" :class="[
              activeTab === 'phone'
                ? 'text-blue-600 border-blue-600'
                : 'hover:text-gray-600 hover:border-gray-300 border-transparent'
            ]" type="button" role="tab" @click="activeTab = 'phone'">
              {{ $t('login.login_2') }}
            </button>
          </li>
        </ul>
      </div>

      <!-- 账号密码登录表单 -->
      <form v-show="activeTab === 'account'" class="mt-8 space-y-6" @submit.prevent="handleAccountSubmit">
        <Alert v-if="error" type="error" :message="error" class="mb-4" />
        <Alert v-if="success" type="success" :message="success" class="mb-4" />

        <div>
          <label for="username" class="block mb-2 text-sm font-medium text-gray-900">
            {{ $t('table.username') }}
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Icon name="heroicons:user" class="w-4 h-4 text-gray-500" />
            </div>
            <input id="username" v-model="accountForm.username" type="text" required
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5"
              :placeholder="$t('users.placeholder_username')">
          </div>
        </div>

        <div>
          <label for="password" class="block mb-2 text-sm font-medium text-gray-900">
            {{ $t('table.password') }}
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Icon name="heroicons:lock-closed" class="w-4 h-4 text-gray-500" />
            </div>
            <input id="password" v-model="accountForm.password" :type="showPassword ? 'text' : 'password'" required
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 pr-10 p-2.5"
              :placeholder="$t('users.placeholder_password')">
            <button type="button" class="absolute inset-y-0 right-0 flex items-center pr-3"
              @click="showPassword = !showPassword">
              <Icon v-if="showPassword" name="heroicons:eye" class="w-4 h-4 text-gray-500 hover:text-gray-700" />
              <Icon v-else name="heroicons:eye-slash" class="w-4 h-4 text-gray-500 hover:text-gray-700" />
            </button>
          </div>
        </div>

        <div class="flex items-center">
          <input id="remember" v-model="accountForm.remember" type="checkbox"
            class="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2">
          <label for="remember" class="ml-2 text-sm font-medium text-gray-900">
            {{ $t('login.remember') }}
          </label>
        </div>

        <div>
          <button type="submit" :disabled="loading"
            class="w-full flex justify-center py-2.5 px-5 text-sm font-medium text-white bg-blue-700 rounded-lg border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 disabled:opacity-50 disabled:cursor-not-allowed">
            <LoadingSpinner v-if="loading" class="mr-2" />
            {{ loading ? $t('login.logging_in') : $t('login.login') }}
          </button>
        </div>

        <div class="text-sm text-gray-600 text-center">
          {{ $t('login.register') }}
          <NuxtLink to="/register" class="text-blue-600 hover:underline">
            {{ $t('login.click_to_register') }}
          </NuxtLink>
        </div>
      </form>

      <!-- 手机号登录表单 -->
      <form v-show="activeTab === 'phone'" class="mt-8 space-y-6" @submit.prevent="handlePhoneSubmit">
        <Alert v-if="error" type="error" :message="error" class="mb-4" />
        <Alert v-if="success" type="success" :message="success" class="mb-4" />

        <div>
          <label for="phone" class="block mb-2 text-sm font-medium text-gray-900">
            {{ $t('table.phone') }}
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Icon name="heroicons:phone" class="w-4 h-4 text-gray-500" />
            </div>
            <input id="phone" v-model="phoneForm.phone" type="tel" required
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5"
              :placeholder="$t('users.placeholder_phone')">
          </div>
        </div>

        <div>
          <label for="code" class="block mb-2 text-sm font-medium text-gray-900">
            {{ $t('login.code') }}
          </label>
          <div class="relative flex space-x-2">
            <div class="relative flex-1">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Icon name="heroicons:key" class="w-4 h-4 text-gray-500" />
              </div>
              <input id="code" v-model="phoneForm.code" type="text" required
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5"
                :placeholder="$t('login.placeholder_code')">
            </div>
            <button type="button" :disabled="countdown > 0"
              class="px-4 py-2 text-sm font-medium text-blue-700 bg-blue-100 rounded-lg hover:bg-blue-200 focus:ring-4 focus:outline-none focus:ring-blue-300 disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap"
              @click="sendCode">
              {{ countdown > 0 ? $t('login.retry_code', { second: countdown }) : $t('login.get_code') }}
            </button>
          </div>
        </div>

        <div>
          <button type="submit" :disabled="loading"
            class="w-full flex justify-center py-2.5 px-5 text-sm font-medium text-white bg-blue-700 rounded-lg border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 disabled:opacity-50 disabled:cursor-not-allowed">
            <LoadingSpinner v-if="loading" class="mr-2" />
            {{ loading ? $t('login.logging_in') : $t('login.login') }}
          </button>
        </div>

        <div class="text-sm text-gray-600 text-center">
          {{ $t('login.register') }}
          <NuxtLink to="/register" class="text-blue-600 hover:underline">
            {{ $t('login.click_to_register') }}
          </NuxtLink>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'empty'
})

import { useI18n } from '#imports'
import { useAuthStore } from '~/stores/auth'
import { useSystemSettings } from '~/composables/useSystemSettings'
import Alert from '~/components/common/Alert.vue'
import LoadingSpinner from '~/components/common/LoadingSpinner.vue'

const { t } = useI18n()
const authStore = useAuthStore()
const router = useRouter()
const { settings } = useSystemSettings()

// 设置页面标题
const pageTitle = computed(() => {
  const systemName = settings.value?.systemName
  if (systemName) {
    return `${t('login.login')} - ${systemName}`
  }
  return `${t('login.login')} - ${t('header.title')}`
})

useHead({
  title: pageTitle
})

const loading = ref(false)
const error = ref('')
const success = ref('')
const activeTab = ref('account')
const countdown = ref(0)
const showPassword = ref(false)

// 账号密码登录表单
const accountForm = ref({
  username: '',
  password: '',
  remember: false
})

// 手机号登录表单
const phoneForm = ref({
  phone: '',
  code: ''
})

const handleAccountSubmit = async () => {
  loading.value = true
  error.value = ''
  success.value = ''

  try {
    console.log('Submitting credentials:', accountForm.value) // 调试日志
    const result = await authStore.login({
      username: accountForm.value.username,
      password: accountForm.value.password
    })

    console.log('Login result:', result) // 调试日志
    if (result.success) {
      router.push('/dashboard')
    } else {
      // 根据错误类型显示不同的错误消息
      if (result.error === 'auth_error') {
        error.value = t('login.error_1') // 用户名或密码错误
      } else if (result.error === 'network_error') {
        error.value = result.message || t('login.error_3') // 网络错误
      } else {
        error.value = result.message || t('login.error_2') // 其他错误
      }
    }
  } catch (e) {
    console.error('Login error:', e?.response?.data || e) // 增强错误日志
    error.value = t('login.error_2') // 未知错误
  } finally {
    loading.value = false
  }
}

const handlePhoneSubmit = async () => {
  loading.value = true
  error.value = ''
  success.value = ''

  try {
    const result = await authStore.phoneLogin({
      phone: phoneForm.value.phone,
      verification_code: phoneForm.value.code
    })

    if (result.success) {
      router.push('/dashboard')
    } else {
      // 根据错误类型显示不同的错误消息
      if (result.error === 'auth_error') {
        error.value = t('login.error_3') // 手机号或验证码错误
      } else if (result.error === 'network_error') {
        error.value = result.message || t('login.error_3') // 网络错误
      } else {
        error.value = result.message || t('login.error_2') // 其他错误
      }
    }
  } catch (e) {
    console.error('Phone login error:', e)
    error.value = t('login.error_2') // 未知错误
  } finally {
    loading.value = false
  }
}

const sendCode = async () => {
  if (countdown.value > 0) return

  // 验证手机号格式
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(phoneForm.value.phone)) {
    error.value = t('login.error_4')
    success.value = ''
    return
  }

  error.value = ''
  success.value = ''
  try {
    const isSuccess = await authStore.sendVerificationCode(phoneForm.value.phone)
    if (isSuccess) {
      // 开始倒计时
      countdown.value = 60
      const timer = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
          clearInterval(timer)
        }
      }, 1000)
      success.value = t('login.success_1')
    } else {
      error.value = t('login.error_5')
    }
  } catch (e) {
    error.value = t('login.error_5')
  }
}

onMounted(() => {
  // 如果已经认证，直接跳转到仪表盘
  // 避免重复的认证检查，因为 plugins/auth.ts 已经处理了
  if (authStore.isAuthenticated) {
    router.push('/dashboard')
  }
})
</script>