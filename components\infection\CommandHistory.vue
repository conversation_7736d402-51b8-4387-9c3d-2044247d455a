<template>
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 mb-8">
    <h2 class="text-lg font-medium mb-6 dark:text-white">
      {{ $t('infection.command_execution_history') }}
    </h2>

    <DataTable :columns="[
      { title: $t('infection.execution_time'), key: 'created_at', width: 180 },
      { title: $t('infection.command_type'), key: 'command', slot: 'command', width: 150 },
      { title: $t('infection.execution_status'), key: 'status', slot: 'status', width: 120 },
      { title: $t('table.action'), key: 'actions', slot: 'actions', width: 100 }
    ]" :data="commandHistory" :loading="loading" :pagination="pagination" @page-change="handlePageChange">
      <!-- 执行时间列 -->
      <template #created_at="{ row }">
        <div class="flex items-center space-x-2">
          <Icon name="heroicons:clock" class="w-4 h-4 text-gray-400 dark:text-gray-500" />
          <span class="dark:text-gray-200">{{ formatTime(row.created_at) }}</span>
        </div>
      </template>

      <!-- 命令类型列 -->
      <template #command="{ row }">
        <div class="flex items-center space-x-2">
          <span :class="[
            'px-3 py-1.5 rounded-full text-xs flex items-center space-x-1.5 font-medium',
            getCommandStyle(row.command).bgColor,
            getCommandStyle(row.command).textColor
          ]">
            <Icon :name="getCommandIcon(row.command)" class="w-3.5 h-3.5" />

            <span>{{ $t(getCommandText(row.command)) }}</span>
          </span>
        </div>
      </template>

      <!-- 执行状态列 -->
      <template #status="{ row }">
        <div class="flex items-center space-x-2">
          <div :class="[
            'w-2 h-2 rounded-full relative',
            row.status ? 'bg-green-500' : 'bg-red-500'
          ]">
            <div :class="[
              'absolute inset-0 rounded-full animate-ping',
              row.status ? 'bg-green-400/30' : 'bg-red-400/30'
            ]"></div>
          </div>
          <span :class="[
            'text-sm font-medium',
            row.status ? 'text-green-500' : 'text-red-500'
          ]">
            {{ row.status ? $t('all.success') : $t('all.failed') }}
          </span>
        </div>
      </template>

      <!-- 操作列 -->
      <template #actions="{ row }">
        <div class="flex items-center space-x-2">
          <button @click="viewCommandDetail(row)" class="inline-flex items-center space-x-1 px-2.5 py-1.5 text-sm font-medium rounded-md
                   text-blue-600 hover:text-blue-900 hover:bg-blue-50
                   dark:text-blue-400 dark:hover:text-blue-300 dark:hover:bg-blue-900/50
                   transition-colors duration-200">
            <Icon name="heroicons:eye" class="w-4 h-4" />
            <span>{{ $t('table.details') }}</span>
          </button>
        </div>
      </template>
    </DataTable>

    <!-- 命令详情Modal -->
    <Modal v-model="showCommandDetail" :title="$t('infection.detail.title')" size="xl">
      <template v-if="currentCommand">
        <div class="space-y-6 dark:text-gray-200">
          <!-- 基本信息 -->
          <div class="space-y-4 bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-2">
                <Icon name="heroicons:clock" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
                <span class="text-gray-500 dark:text-gray-400">{{ $t('infection.detail.title') }}:</span>
                <span class="dark:text-gray-200 font-medium">{{ formatTime(currentCommand.created_at) }}</span>
              </div>
              <div :class="[
                'px-3 py-1.5 rounded-full text-xs font-medium flex items-center space-x-1',
                currentCommand.status ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200'
              ]">
                <div :class="[
                  'w-1.5 h-1.5 rounded-full',
                  currentCommand.status ? 'bg-green-500' : 'bg-red-500'
                ]"></div>
                <span>
                  {{ currentCommand.status ? $t('infection.detail.execution_success') :
                    $t('infection.detail.execution_failed') }}
                </span>
              </div>
            </div>

            <div class="flex items-center space-x-2">
              <Icon name="heroicons:play-circle" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
              <span class="text-gray-500 dark:text-gray-400">{{ $t('infection.command_type') }}:</span>
              <span :class="[
                'px-3 py-1.5 rounded-full text-xs font-medium flex items-center space-x-1.5',
                getCommandStyle(currentCommand.command).bgColor,
                getCommandStyle(currentCommand.command).textColor
              ]">
                <Icon :name="getCommandIcon(currentCommand.command)" class="w-3.5 h-3.5" />

                <span>{{ $t(getCommandText(currentCommand.command)) }}</span>
              </span>
            </div>
          </div>

          <!-- 命令参数 -->
          <div class="space-y-2">
            <h3 class="text-sm font-medium flex items-center space-x-2">
              <Icon name="heroicons:document-text" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
              <span>{{ $t('infection.detail.command_parameters') }}</span>
            </h3>
            <div class="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4 space-y-3">
              <div v-if="currentCommand.args" class="space-y-2">
                <div v-for="(value, key) in currentCommand.args" :key="key" class="flex items-start space-x-2">
                  <span class="text-gray-500 dark:text-gray-400 min-w-0 flex-shrink-0">{{ key }}:</span>
                  <code class="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-sm font-mono flex-1 break-all">
                    {{ Array.isArray(value) ? value.join(', ') : (typeof value === 'object' ? JSON.stringify(value) : value) }}
                  </code>
                </div>
              </div>
              <div v-else class="text-gray-500 dark:text-gray-400 text-sm">
                {{ $t('infection.detail.no_parameters') }}
              </div>
            </div>
          </div>

          <!-- 执行响应 -->
          <div class="space-y-2">
            <h3 class="text-sm font-medium flex items-center space-x-2">
              <Icon name="heroicons:chat-bubble-left-ellipsis" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
              <span>{{ $t('infection.detail.execution_response') }}</span>
            </h3>
            <div class="bg-gray-50 dark:bg-gray-900/50 rounded-lg p-4 space-y-3">
              <div class="flex items-center space-x-2">
                <span class="text-gray-500 dark:text-gray-400">{{ $t('infection.detail.status') }}:</span>
                <span :class="[
                  'px-2 py-1 rounded text-xs font-medium',
                  currentCommand.response?.status === 'success' ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200'
                ]">
                  {{ currentCommand.response?.status }}
                </span>
              </div>
              <div class="space-y-1">
                <span class="text-gray-500 dark:text-gray-400">{{ $t('infection.detail.response_message') }}:</span>
                <p class="text-sm dark:text-gray-200 bg-gray-100 dark:bg-gray-800 p-2 rounded">{{
                  currentCommand.response?.message
                }}</p>
              </div>
              <div v-if="currentCommand.response?.data" class="space-y-1">
                <span class="text-gray-500 dark:text-gray-400">{{ $t('infection.detail.response_data') }}:</span>
                <pre class="text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto font-mono">{{
                  JSON.stringify(currentCommand.response.data, null, 2) }}</pre>
              </div>
            </div>
          </div>
        </div>
      </template>
    </Modal>
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { formatTime } from '~/utils/format'
import { infectionApi } from '~/api/infection'
import DataTable from '~/components/common/DataTable.vue'
import Modal from '~/components/common/Modal.vue'

const { t } = useI18n()

const props = defineProps({
  deviceId: {
    type: String,
    required: true
  },
  exerciseId: {
    type: String,
    required: true
  }
})

// 状态管理
const loading = ref(false)
const commandHistory = ref([])
const pagination = ref({ currentPage: 1, pageSize: 10, total: 0 })
const showCommandDetail = ref(false)
const currentCommand = ref(null)

// 获取命令历史
const fetchCommandHistory = async (page = 1) => {
  try {
    loading.value = true
    const params = {
      page,
      page_size: pagination.value.pageSize,
      device_id: props.deviceId,
      exercise_id: props.exerciseId
    }
    const response = await infectionApi.getDeviceCommands(params)
    commandHistory.value = response.results
    pagination.value.total = response.count
  } catch (error) {
    toast.error(t('infection.message_2'))
    console.error('获取命令历史失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理分页
const handlePageChange = (page) => {
  pagination.value.currentPage = page
  fetchCommandHistory(page)
}

// 查看命令详情
const viewCommandDetail = async (command) => {
  const detail = await infectionApi.getDeviceCommandDetail(command.id)
  currentCommand.value = detail
  showCommandDetail.value = true
}

// 命令类型判断辅助函数
const isChangeWallpaperCommand = (command) => {
  return command === 'change_wallpaper' || command === 'sc'
}

const isRestoreWallpaperCommand = (command) => {
  return command === 'restore_wallpaper' || command === 'rc'
}

const isKillCommand = (command) => {
  return command === 'kill_antivirus' || command === 'kill'
}

const isEncryptCommand = (command) => {
  return command === 'encrypt_files' || command === 'enc'
}

const isDecryptCommand = (command) => {
  return command === 'decrypt_files' || command === 'dec'
}

const isDestroyCommand = (command) => {
  return command === 'destroy_virus' || command === 'exit'
}

const isMaintainPersistenceCommand = (command) => {
  return command === 'maintain_persistence' || command === 'keep'
}

// 获取命令对应的图标名称
const getCommandIcon = (command) => {
  if (isChangeWallpaperCommand(command)) {
    return 'heroicons:photo'
  } else if (isRestoreWallpaperCommand(command)) {
    return 'heroicons:check-circle'
  } else if (isKillCommand(command)) {
    return 'heroicons:shield-check'
  } else if (isEncryptCommand(command)) {
    return 'heroicons:lock-closed'
  } else if (isDecryptCommand(command)) {
    return 'heroicons:lock-open'
  } else if (command === 'all') {
    return 'heroicons:check-circle'
  } else if (isDestroyCommand(command)) {
    return 'heroicons:x-mark'
  } else if (command === 'scan' || command === 'lateral_scan') {
    return 'heroicons:magnifying-glass-plus'
  } else {
    return 'heroicons:plus'
  }
}

// 获取命令文本
const getCommandText = (command) => {
  const commandMap = {
    'change_wallpaper': 'infection.change_wallpaper',
    'restore_wallpaper': 'infection.restore_wallpaper',
    'kill_antivirus': 'infection.command_execution',
    'encrypt_files': 'infection.start_encryption',
    'decrypt_files': 'infection.start_decryption',
    'all': 'infection.check_online_status',
    'destroy_virus': 'infection.destroy_virus',
    'maintain_persistence': 'infection.maintaining_authority',
    'show_ransom_note': 'infection.show_ransom_note',
    'collect_system_info': 'infection.collect_system_info',
    'scan': 'infection.lateral_scan',
    'lateral_scan': 'infection.lateral_scan',
    // 兼容旧命令
    'sc': 'infection.change_wallpaper',
    'rc': 'infection.restore_wallpaper',
    'kill': 'infection.command_execution',
    'enc': 'infection.start_encryption',
    'dec': 'infection.start_decryption',
    'exit': 'infection.destroy_virus',
    'keep': 'infection.maintaining_authority'
  }
  return commandMap[command] || 'infection.unknown_command'
}

// 获取命令样式
const getCommandStyle = (command) => {
  const styleMap = {
    'change_wallpaper': {
      bgColor: 'bg-blue-100 dark:bg-blue-900',
      textColor: 'text-blue-800 dark:text-blue-200',
      icon: 'image'
    },
    'restore_wallpaper': {
      bgColor: 'bg-green-100 dark:bg-green-900',
      textColor: 'text-green-800 dark:text-green-200',
      icon: 'refresh'
    },
    'kill_antivirus': {
      bgColor: 'bg-orange-100 dark:bg-orange-900',
      textColor: 'text-orange-800 dark:text-orange-200',
      icon: 'refresh'
    },
    'encrypt_files': {
      bgColor: 'bg-purple-100 dark:bg-purple-900',
      textColor: 'text-purple-800 dark:text-purple-200',
      icon: 'lock'
    },
    'decrypt_files': {
      bgColor: 'bg-amber-100 dark:bg-amber-900',
      textColor: 'text-amber-800 dark:text-amber-200',
      icon: 'unlock'
    },
    'all': {
      bgColor: 'bg-gray-100 dark:bg-gray-900',
      textColor: 'text-gray-800 dark:text-gray-200',
      icon: 'check'
    },
    'destroy_virus': {
      bgColor: 'bg-red-100 dark:bg-red-900',
      textColor: 'text-red-800 dark:text-red-200',
      icon: 'x'
    },
    'maintain_persistence': {
      bgColor: 'bg-lime-100 dark:bg-lime-900',
      textColor: 'text-lime-800 dark:text-lime-200',
      icon: 'shield'
    },
    'show_ransom_note': {
      bgColor: 'bg-rose-100 dark:bg-rose-900',
      textColor: 'text-rose-800 dark:text-rose-200',
      icon: 'warning'
    },
    'collect_system_info': {
      bgColor: 'bg-cyan-100 dark:bg-cyan-900',
      textColor: 'text-cyan-800 dark:text-cyan-200',
      icon: 'info'
    },
    'scan': {
      bgColor: 'bg-sky-100 dark:bg-sky-900',
      textColor: 'text-sky-800 dark:text-sky-200',
      icon: 'scan'
    },
    'lateral_scan': {
      bgColor: 'bg-sky-100 dark:bg-sky-900',
      textColor: 'text-sky-800 dark:text-sky-200',
      icon: 'scan'
    },
    // 兼容旧命令
    'sc': {
      bgColor: 'bg-blue-100 dark:bg-blue-900',
      textColor: 'text-blue-800 dark:text-blue-200',
      icon: 'image'
    },
    'rc': {
      bgColor: 'bg-green-100 dark:bg-green-900',
      textColor: 'text-green-800 dark:text-green-200',
      icon: 'refresh'
    },
    'kill': {
      bgColor: 'bg-orange-100 dark:bg-orange-900',
      textColor: 'text-orange-800 dark:text-orange-200',
      icon: 'refresh'
    },
    'enc': {
      bgColor: 'bg-purple-100 dark:bg-purple-900',
      textColor: 'text-purple-800 dark:text-purple-200',
      icon: 'lock'
    },
    'dec': {
      bgColor: 'bg-amber-100 dark:bg-amber-900',
      textColor: 'text-amber-800 dark:text-amber-200',
      icon: 'unlock'
    },
    'exit': {
      bgColor: 'bg-red-100 dark:bg-red-900',
      textColor: 'text-red-800 dark:text-red-200',
      icon: 'x'
    },
    'keep': {
      bgColor: 'bg-lime-100 dark:bg-lime-900',
      textColor: 'text-lime-800 dark:text-lime-200',
      icon: 'shield'
    }
  }
  return styleMap[command] || {
    bgColor: 'bg-gray-100 dark:bg-gray-900',
    textColor: 'text-gray-800 dark:text-gray-200',
    icon: 'question'
  }
}

// 暴露刷新函数给父组件
const refreshHistory = () => {
  fetchCommandHistory(pagination.value.currentPage)
}

// 暴露给父组件使用
defineExpose({
  refreshHistory
})

// 初始化
onMounted(() => {
  fetchCommandHistory()
})
</script>