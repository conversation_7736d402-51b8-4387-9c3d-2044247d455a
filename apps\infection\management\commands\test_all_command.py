from django.core.management.base import BaseCommand
from apps.infection.models import DeviceCommand
from apps.infection.serializers import DeviceCommandSerializer


class Command(BaseCommand):
    help = '测试all命令是否不再创建数据库记录'

    def handle(self, *args, **options):
        self.stdout.write("=== 测试all命令修改 ===")
        
        # 记录调用前的任务数量
        before_count = DeviceCommand.objects.count()
        all_commands_before = DeviceCommand.objects.filter(command='all').count()
        
        self.stdout.write(f"测试前数据库中的任务数量: {before_count}")
        self.stdout.write(f"测试前all命令记录数量: {all_commands_before}")
        
        # 测试序列化器验证
        self.stdout.write("\n--- 测试序列化器验证 ---")
        data = {"command": "all", "args": {}}
        serializer = DeviceCommandSerializer(data=data)
        
        if serializer.is_valid():
            self.stdout.write("✅ 序列化器验证通过")
            
            # 检查validated_data
            command = serializer.validated_data.get('command')
            self.stdout.write(f"命令类型: {command}")
            
            if command == "all":
                self.stdout.write("✅ 检测到all命令")
                
                # 模拟视图逻辑：all命令不应该调用serializer.save()
                self.stdout.write("模拟视图逻辑：直接处理all命令，不创建数据库记录")
                
                # 模拟响应
                response_data = {
                    "command": "all",
                    "args": {},
                    "response": {
                        "status": "success",
                        "message": "获取在线设备成功，共 0 个设备",
                        "data": [],
                    },
                }
                self.stdout.write(f"模拟响应: {response_data}")
            else:
                self.stdout.write("❌ 未检测到all命令")
        else:
            self.stdout.write(f"❌ 序列化器验证失败: {serializer.errors}")
        
        # 记录调用后的任务数量
        after_count = DeviceCommand.objects.count()
        all_commands_after = DeviceCommand.objects.filter(command='all').count()
        
        self.stdout.write(f"\n测试后数据库中的任务数量: {after_count}")
        self.stdout.write(f"测试后all命令记录数量: {all_commands_after}")
        
        # 验证结果
        if after_count == before_count and all_commands_after == all_commands_before:
            self.stdout.write(
                self.style.SUCCESS("🎉 测试通过：没有创建新的数据库记录")
            )
        else:
            self.stdout.write(
                self.style.ERROR(f"❌ 测试失败：创建了 {after_count - before_count} 个新记录")
            )
        
        # 额外检查：确保没有all命令记录
        if all_commands_after == 0:
            self.stdout.write(
                self.style.SUCCESS("✅ 确认：数据库中没有all命令记录")
            )
        else:
            self.stdout.write(
                self.style.WARNING(f"⚠️  警告：数据库中仍有 {all_commands_after} 个all命令记录")
            )
