import { ref } from 'vue'

interface Toast {
  id: number
  message: string
  type: 'success' | 'error' | 'warning' | 'info'
  duration: number
}

const toasts = ref<Toast[]>([])
let nextId = 0

export const useToast = () => {
  const addToast = (toast: Omit<Toast, 'id'>) => {
    // 检查是否已存在相同消息的toast
    const existingToast = toasts.value.find(t =>
      t.message === toast.message && t.type === toast.type
    )

    if (existingToast) {
      // 如果已存在相同消息，则不创建新的toast
      return existingToast.id
    }

    const id = nextId++
    const newToast = { ...toast, id }
    toasts.value.push(newToast)

    if (toast.duration > 0) {
      setTimeout(() => {
        removeToast(id)
      }, toast.duration)
    }

    return id
  }

  const removeToast = (id: number) => {
    const index = toasts.value.findIndex(t => t.id === id)
    if (index > -1) {
      toasts.value.splice(index, 1)
    }
  }

  const success = (message: string, duration = 3000) => {
    return addToast({
      message,
      type: 'success',
      duration
    })
  }

  const error = (message: string, duration = 5000) => {
    return addToast({
      message,
      type: 'error',
      duration
    })
  }

  const warning = (message: string, duration = 4000) => {
    return addToast({
      message,
      type: 'warning',
      duration
    })
  }

  const info = (message: string, duration = 3000) => {
    return addToast({
      message,
      type: 'info',
      duration
    })
  }

  return {
    toasts,
    success,
    error,
    warning,
    info,
    remove: removeToast
  }
}
