<template>
  <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- 页面标题 -->
    <div class="mb-8 flex justify-between items-center">
      <div>
        <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent">
          {{ isEdit ? (type === 'copy' ? $t('all.copy') : $t('all.edit')) : $t('all.create') }}{{
            $t('template.templates') }}
        </h1>
        <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
          {{ $t('template.subtitle') }}
        </p>
      </div>

      <NuxtLink to="/config/phishing/template"
        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
          stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
        </svg>
        {{ $t('all.back') }}
      </NuxtLink>
    </div>
    <!-- 页面标题和操作按钮 -->
    <Card className="w-full mb-4">
      <form action="" class="w-full" @submit.prevent="handleSubmit">
        <div class="grid grid-cols-1 sm:grid-cols-2 gap-8 mb-6">
          <Input :label="$t('table.template_name')" :placeholder="$t('template.form.placeholder_template_name')"
            :error="$t('template.form.placeholder_template_name')" required id="name" name="name" v-model="form.name" />

          <Input :label="$t('table.email_subject')" :placeholder="$t('template.form.placeholder_email_subject')"
            :error="$t('template.form.placeholder_email_subject')" required id="email_subject" name="email_subject"
            v-model="form.email_subject" />
        </div>
        <!-- 操作栏 -->
        <div class="w-full flex justify-between">
          <div>
            <button type="button" :class="['p-2', activeTab === 'editor' ? 'text-[#1c64f2]' : 'text-black']"
              @click="toggleTabs('editor')">
              {{ $t('all.rich_text') }}
            </button>
            <button type="button" :class="['p-2', activeTab === 'text' ? 'text-[#1c64f2]' : 'text-black']"
              @click="toggleTabs('text')">
              {{ $t('all.source_code') }}
            </button>
            <button type="button" :class="['p-2', activeTab === 'html' ? 'text-[#1c64f2]' : 'text-black']"
              @click="toggleTabs('html')">
              {{ $t('all.preview') }}
            </button>
          </div>

          <button type="button" class="text-right text-[#1c64f2] text-sm hover:text-[#76A9FA]"
            @click="showImportModal = true">
            {{ $t('template.form.importing_a_template') }}
          </button>
        </div>

        <!-- 内容区域 -->
        <div class="w-full rounded border p-2 mb-2">
          <div v-if="activeTab === 'text'" ref="editorContainer" class="editor w-full h-96"></div>
          <div v-if="activeTab === 'editor'" ref="divRef" class="editor w-full h-96"></div>
          <div v-if="activeTab === 'html'" class="preview h-96 overflow-auto">
            <iframe :key="activeTab" ref="previewFrame" class="w-full h-full border-0"></iframe>
          </div>
        </div>
        <div class="flex items-center gap-6">
          <Checkbox :label="$t('template.form.whether_to_track_users')" id="is_track_user"
            v-model="form.is_track_user" />
        </div>

        <!-- 选择邮件 -->
        <div class="mb-6">
          <button type="button" class="p-2 float-right text-[#1c64f2] text-sm hover:text-[#76A9FA]"
            @click="showUploadModal = true">
            {{ $t('template.form.add_attachments') }}
          </button>
          <table class="w-full border">
            <thead>
              <tr class="h-10 border-b bg-gray-100">
                <th class="w-1/3 text-center">
                  {{ $t('table.file_name') }}
                </th>
                <th class="w-1/3 text-center">
                  {{ $t('table.file_size') }}
                </th>
                <th class="w-1/3 text-center">
                  {{ $t('table.action') }}
                </th>
              </tr>
            </thead>
          </table>
          <template v-if="form.email_template_file.length > 0">
            <div class="w-full border border-t-0 h-40 overflow-y-auto">
              <table class="w-full">
                <tbody>
                  <tr v-for="items in form.email_template_file" :key="items.email_file" class="max-h-12 h-12 border-b">
                    <td class="w-1/3 text-center" hidden>{{ items.email_file }}</td>
                    <td class="w-1/3 text-center">{{ items.file_name }}</td>
                    <td class="w-1/3 text-center">{{ formatFileSize(items.file_size) }}</td>
                    <td class="w-1/3 text-center cursor-pointer text-sm text-[#ff4d4f]"
                      @click="handleDelete(items.email_file)">
                      {{ $t('all.delete') }}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </template>
          <template v-else>
            <div class="w-full border border-t-0 h-40 py-6 pb-4">
              <div class="h-20">
                <svg class="w-full h-full mb-1" viewBox="0 0 184 152">
                  <g fill="none" fill-rule="evenodd">
                    <g transform="translate(24 31.67)">
                      <ellipse fill-opacity=".8" fill="#F5F5F7" cx="67.797" cy="106.89" rx="67.797" ry="12.668">
                      </ellipse>
                      <path
                        d="M122.034 69.674L98.109 40.229c-1.148-1.386-2.826-2.225-4.593-2.225h-51.44c-1.766 0-3.444.839-4.592 2.225L13.56 69.674v15.383h108.475V69.674z"
                        fill="#AEB8C2"></path>
                      <path
                        d="M101.537 86.214L80.63 61.102c-1.001-1.207-2.507-1.867-4.048-1.867H31.724c-1.54 0-3.047.66-4.048 1.867L6.769 86.214v13.792h94.768V86.214z"
                        fill="url(#linearGradient-1)" transform="translate(13.56)"></path>
                      <path d="M33.83 0h67.933a4 4 0 0 1 4 4v93.344a4 4 0 0 1-4 4H33.83a4 4 0 0 1-4-4V4a4 4 0 0 1 4-4z"
                        fill="#F5F5F7"></path>
                      <path
                        d="M42.678 9.953h50.237a2 2 0 0 1 2 2V36.91a2 2 0 0 1-2 2H42.678a2 2 0 0 1-2-2V11.953a2 2 0 0 1 2-2zM42.94 49.767h49.713a2.262 2.262 0 1 1 0 4.524H42.94a2.262 2.262 0 0 1 0-4.524zM42.94 61.53h49.713a2.262 2.262 0 1 1 0 4.525H42.94a2.262 2.262 0 0 1 0-4.525zM121.813 105.032c-.775 3.071-3.497 5.36-6.735 5.36H20.515c-3.238 0-5.96-2.29-6.734-5.36a7.309 7.309 0 0 1-.222-1.79V69.675h26.318c2.907 0 5.25 2.448 5.25 5.42v.04c0 2.971 2.37 5.37 5.277 5.37h34.785c2.907 0 5.277-2.421 5.277-5.393V75.1c0-2.972 2.343-5.426 5.25-5.426h26.318v33.569c0 .617-.077 1.216-.221 1.789z"
                        fill="#DCE0E6"></path>
                    </g>
                    <path
                      d="M149.121 33.292l-6.83 2.65a1 1 0 0 1-1.317-1.23l1.937-6.207c-2.589-2.944-4.109-6.534-4.109-10.408C138.802 8.102 148.92 0 161.402 0 173.881 0 184 8.102 184 18.097c0 9.995-10.118 18.097-22.599 18.097-4.528 0-8.744-1.066-12.28-2.902z"
                      fill="#DCE0E6"></path>
                    <g transform="translate(149.65 15.383)" fill="#FFF">
                      <ellipse cx="20.654" cy="3.167" rx="2.849" ry="2.815"></ellipse>
                      <path d="M5.698 5.63H0L2.898.704zM9.259.704h4.985V5.63H9.259z"></path>
                    </g>
                  </g>
                </svg>

                <div class="text-center text-sm text-[#00000073]">
                  {{ $t('all.no_data') }}
                </div>
              </div>
            </div>
          </template>
        </div>

        <!-- 按钮组 -->
        <div class="px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
          <button type="submit" :disabled="!isFormValid && !id"
            class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50 disabled:cursor-not-allowed">
            {{ id ? $t('all.save') : $t('all.create') }}
          </button>
          <NuxtLink to="/config/phishing/template"
            class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
            {{ $t('all.cancel') }}
          </NuxtLink>
        </div>

      </form>
    </Card>

    <TemplateImportModal v-if="showImportModal" v-model="html" @close="closeModal" />
    <TemplateUploadModal v-if="showUploadModal" v-model="templateFile" @close="closeModal" />
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import * as monaco from 'monaco-editor'
import Card from '~/components/common/Card.vue'
import Input from '~/components/common/Input.vue'
import Checkbox from '~/components/common/Checkbox.vue'
import TemplateImportModal from '~/components/phishing/TemplateImportModal.vue'
import TemplateUploadModal from '~/components/phishing/TemplateUploadModal.vue'
import { phishingApi } from '~/api/phishing'
import "aieditor/dist/style.css"

let aiEditor = null

const { t } = useI18n()
const toast = useToast()
const route = useRoute()
const id = route.query.id
const type = route.query.type
const isEdit = computed(() => !!id)
const activeTab = ref('editor')
const editorContainer = ref(null)
const previewFrame = ref(null)
const divRef = ref(null)
const showImportModal = ref(false)
const showUploadModal = ref(false)
const html = ref('')
const templateFile = ref([])
const form = ref({ name: '', email_subject: '', content: '', is_track_user: false, email_template_file: [] })

// 获取编辑初始值
if (id) {
  const { data } = await useAsyncData(() => phishingApi.getEmailTemplateDetailApi(Number(route.query.id)))
  form.value = data.value
}

// 切换标签页
const toggleTabs = (value) => {
  activeTab.value = value
}

// 格式化文件大小
const formatFileSize = (size) => {
  let value = Number(size);
  if (size && !isNaN(value)) {
    const units = ['B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB', 'BB'];
    let index = 0;
    let k = value;
    if (value >= 1024) {
      while (k > 1024) {
        k = k / 1024;
        index++;
      }
    }
    return `${(k).toFixed(2)}${units[index]}`;
  }
  return '-';
}

// 删除文件
const handleDelete = (value) => {
  form.value.email_template_file = form.value.email_template_file.filter(items => items.email_file !== value)
}

// 修改表单验证逻辑
const isFormValid = computed(() => {
  return Object.keys(form.value).every(key => {
    if (key === 'is_track_user') return true;
    return !!form.value[key];
  });
});

// 表单提交事件
const handleSubmit = () => {
  if (id && !type) {
    phishingApi.updateEmailTemplateApi(id, form.value).then(res => {
      toast.success(t('all.update_success'))
    })
    return
  }

  phishingApi.createEmailTemplateApi(form.value).then(res => {
    toast.success(t('all.create_success'))
    navigateTo('/config/phishing/template')
  })
}

// 关闭Modal
const closeModal = () => {
  showImportModal.value = false
  showUploadModal.value = false
}

// 初始化富文本编辑器
const initEditor = async () => {
  try {
    const { AiEditor } = await import('aieditor')
    aiEditor = new AiEditor({
      element: divRef.value,
      placeholder: "点击输入内容...",
      content: form.value.content || '',
      toolbarKeys: [
        "undo", "redo", "brush", "eraser",
        "|",
        "bold", "italic", "underline", "strike",
        "|",
        "bullet-list", "ordered-list", "indent-decrease", "indent-increase",
        "|",
        "code-block", "table",
        "|",
        "source-code", "fullscreen",
        "|",
        "ai"
      ],
      toolbarSize: 'medium',
      ai: {
        models: {
          openai: {
            endpoint: useRuntimeConfig().public.aiEndpoint,
            model: useRuntimeConfig().public.aiModel,
            apiKey: useRuntimeConfig().public.aiApiKey,
            protocol: "sse",
            stream: false,
            temperature: 0
          }
        }
      },
      onChange: (editor) => {
        console.log(editor.getHtml());
        form.value.content = editor.getHtml()
      },
      onCreated: () => {
        // 编辑器创建完成后，如果是编辑模式则获取详情
        if (isEdit.value) {
          // getDetail()
        }
      }
    })
  } catch (error) {
    console.error('初始化编辑器失败:', error)
    toast.error('初始化编辑器失败')
  }
}

// 监听 templateFile
watch(templateFile, (newValue) => {
  if (newValue) {
    form.value.email_template_file.push(newValue)
  }
})

// 导入模板
// 监听 html 和 编辑器内容
watch(html, (newValue) => {
  if (newValue) {
    const editor = monaco.editor.getModels()[0]

    // // 创建一个新的DOMParser实例
    // const parser = new DOMParser();
    // const doc = parser.parseFromString(newValue, 'text/html');

    if (editor) {
      editor.setValue(newValue)
    }

    if (aiEditor) {
      aiEditor.insert(newValue)
    }

    form.value.content = newValue
  }
})

// 更新预览
const updatePreview = () => {
  if (!previewFrame.value) return
  const iframe = previewFrame.value
  const iframeDoc = iframe.contentDocument || iframe.contentWindow.document
  iframeDoc.open()
  iframeDoc.write(form.value.content || '')
  iframeDoc.close()
}

// 统一监听activeTab和content变化
watch([activeTab, () => form.value.content], ([tab, content]) => {
  if (tab === 'html' && content) {
    nextTick(updatePreview)
  }
}, { immediate: true })

// 监听 divRef
watch(divRef, async (newValue) => {
  if (newValue) {
    await initEditor()
  }
})

// 监听 editorContainer
watch(editorContainer, (newValue) => {
  if (newValue) {
    const editor = monaco.editor.create(newValue, {
      value: form.value.content,
      language: 'html',
      folding: true,
      minimap: { enabled: false },
      wordWrap: 'on',
      automaticLayout: true,
      scrollBeyondLastLine: false
    });

    editor.onDidChangeModelContent(() => {
      form.value.content = editor.getValue();
    });
  }
})

onBeforeUnmount(() => {
  if (editorContainer.value) {
    const editor = monaco.editor.getModels()[0];
    editor.dispose()
  }
});
</script>