<template>
  <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent">
        {{ $t('template.title') }}
      </h1>
      <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
        {{ $t('template.subtitle') }}
      </p>
    </div>

    <!-- 导航链接 -->
    <div class="inline-flex rounded-md shadow-sm mb-6" role="group">
      <NuxtLink to="/config/phishing/strategy"
        class="px-4 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-l-lg hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-blue-500 dark:focus:text-white">
        <span class="flex items-center">
          <Icon name="heroicons:cog-6-tooth" class="-ml-1 mr-2 h-5 w-5" />
          {{ $t('strategy.title') }}
        </span>
      </NuxtLink>
      <NuxtLink to="/config/phishing/template"
        class="px-4 py-2 text-sm font-medium text-white bg-blue-700 border-t border-b border-gray-200 hover:bg-blue-800 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-white dark:bg-blue-600 dark:border-gray-600 dark:text-white dark:hover:text-white dark:hover:bg-blue-700 dark:focus:ring-blue-500 dark:focus:text-white">
        <span class="flex items-center">
          <Icon name="heroicons:envelope" class="-ml-1 mr-2 h-5 w-5" />
          {{ $t('template.title') }}
        </span>
      </NuxtLink>
      <NuxtLink to="/config/phishing/page"
        class="px-4 py-2 text-sm font-medium text-gray-900 bg-white border-t border-b border-gray-200 hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-blue-500 dark:focus:text-white">
        <span class="flex items-center">
          <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
          </svg>
          {{ $t('page.title') }}
        </span>
      </NuxtLink>
      <NuxtLink to="/config/phishing/task"
        class="px-4 py-2 text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-r-lg hover:bg-gray-100 hover:text-blue-700 focus:z-10 focus:ring-2 focus:ring-blue-700 focus:text-blue-700 dark:bg-gray-700 dark:border-gray-600 dark:text-white dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-blue-500 dark:focus:text-white">
        <span class="flex items-center">
          <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
              d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
          </svg>
          {{ $t('task.title') }}
        </span>
      </NuxtLink>
    </div>

    <!-- 操作按钮 -->
    <div class="flex justify-end space-x-4 mb-6">
      <NuxtLink to="/config/phishing/template/form"
        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <svg class="-ml-1 mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
        </svg>
        {{ $t('all.create') }}{{ $t('template.templates') }}
      </NuxtLink>
    </div>

    <!-- 搜索和数据表格 -->
    <div class="bg-white dark:bg-gray-800 rounded-lg shadow">
      <div class="p-4 border-b border-gray-200 dark:border-gray-700">
        <SearchFilter v-model="filters" :search-value="searchQuery" @update:search-value="val => searchQuery = val"
          :search-placeholder="$t('template.placeholder_search')" @search="handleSearch" :show-search-button="true"
          :search-button-text="$t('all.search')" />
      </div>
      <!-- 数据表格 -->
      <div class="bg-white rounded-lg shadow">
        <DataTable :columns="[
          { title: $t('table.template_name'), key: 'name', width: 200 },
          { title: $t('table.template_type'), key: 'template_type', slot: 'template_type', width: 200 },
          { title: $t('table.email_subject'), key: 'email_subject', width: 200 },
          { title: $t('table.file_name'), key: 'email_template_file', slot: 'email_template_file', width: 150 },
          { title: $t('table.create_time'), key: 'created_at', width: 150 },
          { title: $t('table.action'), key: 'actions', slot: 'actions', width: 150 }
        ]" :data="data.results" :loading="pending" :pagination="pagination" @page-change="handlePageChange">

          <!-- 模板类型 -->
          <template #template_type="{ row }">
            <span :class="[
              'inline-flex px-2 py-1 text-xs font-medium rounded-full',
              row.template_type === 'SYSTEM'
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
                : 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
            ]">
              {{ row.template_type === 'SYSTEM' ? $t('table.template_type_system') : $t('table.template_type_custom') }}
            </span>
          </template>

          <!-- 附件名称 -->
          <template #email_template_file="{ row }">
            {{ row.email_template_file[0]?.file_name }}
          </template>

          <!-- 操作列 -->
          <template #actions="{ row }">
            <div class="flex items-center space-x-2">
              <NuxtLink :to="`/config/phishing/template/form?id=${row.id}&type=copy`"
                class="p-1 text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300">
                <svg t="1740043905190" class="w-5 h-5" viewBox="0 0 1024 1024" version="1.1"
                  xmlns="http://www.w3.org/2000/svg" p-id="2822" width="1024" height="1024">
                  <path
                    d="M720 192h-544A80.096 80.096 0 0 0 96 272v608C96 924.128 131.904 960 176 960h544c44.128 0 80-35.872 80-80v-608C800 227.904 764.128 192 720 192z m16 688c0 8.8-7.2 16-16 16h-544a16 16 0 0 1-16-16v-608a16 16 0 0 1 16-16h544a16 16 0 0 1 16 16v608z"
                    fill="currentColor" p-id="2823"></path>
                  <path
                    d="M848 64h-544a32 32 0 0 0 0 64h544a16 16 0 0 1 16 16v608a32 32 0 1 0 64 0v-608C928 99.904 892.128 64 848 64z"
                    fill="currentColor" p-id="2824"></path>
                  <path
                    d="M608 360H288a32 32 0 0 0 0 64h320a32 32 0 1 0 0-64zM608 520H288a32 32 0 1 0 0 64h320a32 32 0 1 0 0-64zM480 678.656H288a32 32 0 1 0 0 64h192a32 32 0 1 0 0-64z"
                    fill="currentColor" p-id="2825"></path>
                </svg>
              </NuxtLink>
              <template v-if="row.template_type === 'CUSTOM'">
                <NuxtLink :to="`/config/phishing/template/form?id=${row.id}`"
                  class="p-1 text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                  </svg>
                </NuxtLink>
                <button @click="handleDelete(row)"
                  class="p-1 text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                  <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                      d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                  </svg>
                </button>
              </template>
            </div>
          </template>
        </DataTable>
      </div>
    </div>

    <!-- 删除确认弹窗 -->
    <ConfirmDialog :show="showDeleteConfirm" :title="`${$t('all.delete')}${$t('template.templates')}`"
      :message="deleteConfirmMessage" type="danger" @confirm="confirmDelete" @cancel="showDeleteConfirm = false" />
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { ref, computed } from 'vue'
import { useAsyncData } from '#app'
import SearchFilter from '~/components/common/SearchFilter.vue'
import DataTable from '~/components/common/DataTable.vue'
import ConfirmDialog from '~/components/common/ConfirmDialog.vue'
import { phishingApi } from '~/api/phishing'
import { debounce } from 'lodash-es'

const { t } = useI18n()
const toast = useToast()

// 状态变量
const showDeleteConfirm = ref(false)
const toDeleteData = ref(null)
const pending = ref(false)
const currentPage = ref(1)
const pageSize = ref(10)
const searchQuery = ref('')
const filters = ref({
  search: ''
})

const { data, refresh } = await useAsyncData('email-template-list', () => phishingApi.getEmailTemplateListApi({ page: currentPage.value, page_size: pageSize.value, search: filters.value.search }))
const total = computed(() => data.value?.count || 0)

// 使用防抖的搜索处理函数
const debouncedSearch = debounce((value) => {
  filters.value.search = value
  currentPage.value = 1
  refresh()
}, 300)

// 处理搜索
const handleSearch = (value) => {
  debouncedSearch(value)
}

// 分页对象
const pagination = computed(() => ({
  currentPage: currentPage.value,
  pageSize: pageSize.value,
  total: total.value
}))

// 处理分页
const handlePageChange = (page) => {
  currentPage.value = page
  refresh()
}

// 删除策略提示信息
const deleteConfirmMessage = computed(() => {
  if (!toDeleteData.value) return
  return t('template.delete_message', { name: toDeleteData.value?.name })
})

// 处理删除
const handleDelete = (row) => {
  toDeleteData.value = { ...row }
  showDeleteConfirm.value = true
}

// 确认删除
const confirmDelete = async () => {
  if (!toDeleteData.value?.id) return
  phishingApi.deleteEmailTemplateApi(toDeleteData.value.id).then(async () => {
    toast.success(t('all.delete_success'))
    await refresh()
    showDeleteConfirm.value = false
    toDeleteData.value = null
  })
}
</script>