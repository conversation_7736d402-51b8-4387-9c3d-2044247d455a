<template>
  <div class="max-w-7xl mx-auto px-4 py-8">
    <!-- 基本信息 -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-8">
      <div class="flex justify-between items-center mb-8">
        <div>
          <h1 class="text-2xl font-bold dark:text-white">
            {{ $t('exercise.report.title') }}
          </h1>
        </div>
        <div class="flex space-x-3">
          <template v-if="form?.status === 'FI'">
            <button @click="openExportModal"
              class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
              <Icon name="heroicons:arrow-down-tray" class="-ml-1 mr-2 h-5 w-5" />
              {{ $t('exercise.report.export') }}
            </button>
          </template>

          <NuxtLink to="/exercise"
            class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
            <Icon name="heroicons:arrow-left" class="-ml-1 mr-2 h-5 w-5" />
            {{ $t('all.back') }}
          </NuxtLink>
        </div>
      </div>
      <table class="w-full">
        <tbody>
          <tr>
            <td :class="['py-2 align-top', classes]">
              <div class="flex items-center space-x-2">
                <Icon name="heroicons:lock-closed" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
                <span class="text-gray-500 dark:text-gray-400">{{ $t('exercise.form.exercise_name') }}:</span>
              </div>
            </td>
            <td class="py-2 align-top dark:text-gray-200" style="width: 260px">
              <span>{{ form?.name }}</span>
            </td>
            <td :class="['py-2 align-top', classes]">
              <div class="flex items-center space-x-2">
                <Icon name="heroicons:clock" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
                <span class="text-gray-500 dark:text-gray-400">{{ $t('exercise.report.exercise_time') }}:</span>
              </div>
            </td>
            <td class="py-2 align-top dark:text-gray-200">
              <span>{{ form?.start_time }} - {{ form?.end_time }}</span>
            </td>
          </tr>

          <tr>
            <td :class="['py-2 align-top', classes]">
              <div class="flex items-center space-x-2">
                <Icon name="heroicons:clock" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
                <span class="text-gray-500 dark:text-gray-400">{{ $t('exercise.form.duration') }}:</span>
              </div>
            </td>
            <td class="py-2 align-top dark:text-gray-200" style="width: 260px">
              <span>
                {{ getDuration(form?.start_time, form?.end_time) }}
              </span>
            </td>
            <td :class="['py-2 align-top', classes]">
              <div class="flex items-center space-x-2">
                <Icon name="heroicons:building-office" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
                <span class="text-gray-500 dark:text-gray-400">{{ $t('exercise.report.exercise_unit') }}:</span>
              </div>
            </td>
            <td class="py-2 align-top dark:text-gray-200">
              <span>{{ form?.company }}</span>
            </td>
          </tr>

          <tr>
            <td :class="['py-2 align-top', classes]">
              <div class="flex items-center space-x-2">
                <Icon name="heroicons:chart-bar" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
                <span class="text-gray-500 dark:text-gray-400">{{ $t('exercise.report.exercise_status') }}:</span>
              </div>
            </td>
            <td class="py-2 align-top dark:text-gray-200" style="width: 260px">
              <span :class="getStatusStyle(form?.status)">{{ getStatusText(form?.status) }}</span>
            </td>
            <td :class="['py-2 align-top', classes]">
              <div class="flex items-center space-x-2">
                <Icon name="heroicons:cog-6-tooth" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
                <span class="text-gray-500 dark:text-gray-400">{{ $t('exercise.report.virus_name') }}:</span>
              </div>
            </td>
            <td class="py-2 align-top dark:text-gray-200">
              <span>{{ form?.virus_name }}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <!-- 演练结果统计 -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-8">
      <div class="flex items-center space-x-2 mb-6">
        <Icon name="heroicons:computer-desktop" class="w-5 h-5 text-gray-500 dark:text-gray-400" />
        <h2 class="text-xl font-semibold dark:text-white">{{ $t('exercise.report.h1') }}</h2>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
        <!-- 目标设备总数 -->
        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">
                {{ $t('exercise.report.total_number_of_targeted_devices') }}
              </p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ statistics.totalDevices }}</p>
            </div>
            <div class="p-3 bg-blue-100 rounded-full">
              <Icon name="heroicons:computer-desktop" class="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </div>

        <!-- 已感染设备数 -->
        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">
                {{ $t('exercise.report.number_of_infected_devices') }}
              </p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ statistics.infectedDevices }}</p>
            </div>
            <div class="p-3 bg-red-100 rounded-full">
              <Icon name="heroicons:exclamation-triangle" class="w-6 h-6 text-red-600" />
            </div>
          </div>
          <div class="mt-4">
            <div class="flex items-center">
              <div class="flex-1 bg-gray-200 rounded-full h-2">
                <div class="bg-red-600 h-2 rounded-full" :style="{ width: infectionRate + '%' }"></div>
              </div>
              <span class="ml-2 text-sm text-gray-500 dark:text-gray-400">{{ infectionRate }}%</span>
            </div>
          </div>
        </div>

        <!-- 钓鱼邮件统计 -->
        <div class="bg-gray-50 dark:bg-gray-700 rounded-lg p-6">
          <div class="flex items-center justify-between">
            <div>
              <p class="text-sm font-medium text-gray-500 dark:text-gray-400">
                {{ $t('exercise.report.phishing_email_click_rate') }}
              </p>
              <p class="text-2xl font-bold text-gray-900 dark:text-white">{{ clicksRate }}%</p>
            </div>
            <div class="p-3 bg-yellow-100 rounded-full">
              <Icon name="heroicons:envelope" class="w-6 h-6 text-yellow-600" />
            </div>
          </div>
          <div class="mt-2 space-y-1">
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">
                {{ $t('exercise.report.total_number_of_sent_emails') }}
              </span>
              <span class="font-medium text-gray-900 dark:text-gray-100">{{ statistics.totalEmails }}</span>
            </div>
            <div class="flex justify-between text-sm">
              <span class="text-gray-500 dark:text-gray-400">
                {{ $t('exercise.report.number_of_clicks') }}
              </span>
              <span class="font-medium text-gray-900 dark:text-gray-100">{{ statistics.clickedEmails }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 捕获数据 -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-8">
      <div class="flex items-center space-x-2 mb-6">
        <Icon name="heroicons:chart-bar-square" class="w-5 h-5 text-gray-500 dark:text-gray-400" />
        <h2 class="text-xl font-semibold dark:text-white">
          捕获数据
        </h2>
      </div>
      <div class="space-y-6">
        <CaptureDataTable :id="id" />
      </div>
    </div>

    <!-- 目标资产信息 -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 mb-8">
      <div class="flex items-center space-x-2 mb-6">
        <Icon name="heroicons:information-circle" class="w-5 h-5 text-gray-500 dark:text-gray-400" />
        <h2 class="text-xl font-semibold dark:text-white">
          {{ $t('exercise.report.h3') }}
        </h2>
      </div>
      <div class="space-y-6">
        <template v-if="targetAssetsInfectionList.length">
          <DataTable :columns="targetColumns" :data="targetAssetsInfectionList" :loading="targetLoading"
            :pagination="targetPagination" @page-change="handleTargetPageChange">
            <template #group="{ row }">
              <div class="flex items-center">
                <span>{{ row.group }}</span>
              </div>
            </template>

            <template #name="{ row }">
              <div class="flex items-center">
                <span>{{ row.name }}</span>
              </div>
            </template>

            <template #is_infected="{ row }">
              <div class="flex items-center">
                <span :class="[
                  'px-3 py-1 text-xs font-medium rounded-full',
                  (row.is_infected) ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'
                ]">{{ row.is_infected ? $t('infection.infected') : $t('infection.not_infected') }}</span>
              </div>
            </template>

            <template #asset_type="{ row }">
              <div class="flex items-center">
                <span :class="[
                  'px-3 py-1 text-xs font-medium rounded-full',
                  getTypeStyle(row.asset_type)
                ]">{{ getAssetType(row.asset_type) }}</span>
              </div>
            </template>

            <template #ip_address_v4="{ row }">
              <div class="flex items-center">
                <span>{{ row.ip_address_v4 }}</span>
              </div>
            </template>
          </DataTable>
        </template>
        <div v-else class="text-center text-gray-500 dark:text-gray-400">
          {{ $t('all.no_data') }}
        </div>
      </div>
    </div>

    <!-- 邮件发送记录 -->
    <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
      <div class="flex items-center space-x-2 mb-6">
        <Icon name="heroicons:envelope" class="w-6 h-6 text-gray-500 dark:text-gray-400" />
        <h2 class="text-xl font-semibold dark:text-white">
          {{ $t('exercise.report.h4') }}
        </h2>
      </div>

      <DataTable :columns="emailColumns" :data="paginatedEmailRecords" :pagination="emailPagination"
        @page-change="handleEmailPageChange">
        <template #recipient="{ row }">
          <div class="flex items-center">
            <span>{{ row.recipient }}</span>
          </div>
        </template>

        <template #email_subject="{ row }">
          <div class="flex items-center">
            <span>{{ row.email_subject }}</span>
          </div>
        </template>

        <template #send_time="{ row }">
          <div class="flex items-center">
            <span>{{ formatDateTime(row.send_time) }}</span>
          </div>
        </template>

        <template #is_click="{ row }">
          <span :class="[
            'px-2 inline-flex text-xs leading-5 font-semibold rounded-full',
            row.is_click ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-400' : 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-400'
          ]">
            {{ row.is_click ? $t('exercise.report.clicked') : $t('exercise.report.not_clicked') }}
          </span>
        </template>
      </DataTable>

    </div>

    <!-- 导出模态框 -->
    <ReportExportModal :is-open="showExportModal" :exercise-id="id" :exercise-name="form.name" @close="closeExportModal"
      @exported="handleExported" />
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import { exerciseApi } from '@/api/exercises'
import { infectionApi } from '~/api/infection'
import { negotiateApi } from '@/api/negotiate'
import { assetApi } from '@/api/asset'
import DataTable from '~/components/common/DataTable.vue'
import CaptureDataTable from '~/components/exercise/CaptureDataTable.vue'
import ReportExportModal from '~/components/exercise/ReportExportModal.vue'

const { locale, t } = useI18n()
const route = useRoute()
const id = route.params.id

// 导出模态框状态
const showExportModal = ref(false)

const form = ref({
  name: '',
  start_time: '',
  end_time: '',
  company: '',
  target_group_names: []
})

const classes = computed(() => {
  return {
    'w-[120px]': locale.value === 'zh',
    'w-[160px]': locale.value === 'en'
  }
})


// 添加新的计算属性
const getDuration = computed(() => {
  return (startTime, endTime) => {
    if (!startTime) return t('exercise.not_started')
    if (!endTime) return t('exercise.in_progress')

    const start = new Date(startTime)
    const end = new Date(endTime)
    const diff = end - start

    const days = Math.floor(diff / (1000 * 60 * 60 * 24))
    const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
    const minute = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
    const seconds = Math.floor((diff % (1000 * 60)) / 1000)

    return `${days} ${t('unit.days')} ${hours} ${t('unit.hours')} ${minute} ${t('unit.minutes')} ${seconds} ${t('unit.seconds')}`
  }
})

// ==================== 目标资产表格 ====================
// 目标资产表格配置
const targetColumns = [
  {
    key: 'group_name',
    title: t('table.group'),
    slot: 'group_name',
    width: 150
  },
  {
    key: 'name',
    title: t('table.asset_name'),
    slot: 'name',
    width: 200
  },
  {
    key: 'asset_type',
    title: t('table.asset_type'),
    slot: 'asset_type',
    width: 120
  },
  {
    key: 'is_infected',
    title: t('table.status'),
    slot: 'is_infected',
    width: 120
  },
  {
    key: 'ip_address_v4',
    title: t('table.ip_address'),
    slot: 'ip_address_v4',
    width: 150
  }
]
const targetAssets = ref([])
const targetLoading = ref(false)
const targetCurrentPage = ref(1)
const targetPageSize = ref(10)
const targetTotal = ref(0)

const targetPagination = computed(() => ({
  currentPage: targetCurrentPage.value,
  pageSize: targetPageSize.value,
  total: targetTotal.value
}))

// 演练结果统计
const statistics = ref({
  totalDevices: 0,
  infectedDevices: 0,
  phishingRate: 0,
  totalEmails: 0,
  clickedEmails: 0
})

// 获取资产类型名称
const getAssetType = (type) => {
  const types = {
    'EP': t('assets.terminal_equipment'),
    'SV': t('assets.server'),
    'EM': t('assets.email')
  }
  return types[type] || type
}

// 获取类型对应的样式
const getTypeStyle = (type) => {
  const styles = {
    EP: 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-400',
    SV: 'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-400',
    EM: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-400'
  }
  return styles[type] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
}

// 处理目标资产分页变化
const handleTargetPageChange = async (page) => {
  targetCurrentPage.value = page
  await getTargetAssetsInfection(page)
}



const fetchReportDetail = async () => {
  const detail = await exerciseApi.getExercisesDetailApi(id)
  const company = await negotiateApi.getNegotiationDetailApi(detail.negotiation)

  form.value = {
    ...detail,
    company: company?.company_name
  }

  await getTargetAssets(detail?.target_groups)
  await getInfectionCount()
  await getEmailAssets(form.value)
  await fetchEmailRecords(id)
}

// 获取目标资产详情
const getTargetAssets = async (value) => {
  if (value?.length) {
    // 获取所有目标资产组
    const { results: groups } = await assetApi.getAssetGroups({ asset_type: 'EP,SV', page: 1, page_size: 1000 })

    const promises = value.map(async (groupId) => {
      // 从所有组中找到匹配的组
      const group = groups.find(g => g.id === groupId)
      if (!group) return null

      // 获取该组下的资产列表,注意这里要分别获取终端和服务器资产
      const [epAssets, svAssets] = await Promise.all([
        assetApi.getAssets({ group: groupId, asset_type: 'EP', page: 1, page_size: 1000 }),
        assetApi.getAssets({ group: groupId, asset_type: 'SV', page: 1, page_size: 1000 })
      ])

      // 合并两种类型的资产
      const assets = [...epAssets.results, ...svAssets.results]

      return {
        ...group,
        assets
      }
    })
    targetAssets.value = (await Promise.all(promises)).filter(Boolean)

    // 计算所有资产组的资产总数
    statistics.value.totalDevices = targetAssets.value.reduce((total, group) => {
      return total + (group.assets?.length || 0)
    }, 0)
  }
}

// 获取感染资产数量
const getInfectionCount = async () => {
  const response = await infectionApi.getDevices({ exercise_id: id, infection_count: 0 })
  statistics.value.infectedDevices = response.count;
}

// 获取邮箱资产详情
const getEmailAssets = async (detail) => {
  if (detail?.target_asset?.length) {
    // 获取所有邮箱资产组
    const { results: groups } = await assetApi.getAssetGroups({ asset_type: 'EM', page: 1, page_size: 1000 })

    const promises = detail.target_asset.map(async (groupId) => {
      // 从所有组中找到匹配的组
      const group = groups.find(g => g.id === groupId)
      if (!group) return null

      const { results: assets } = await assetApi.getAssets({ group: groupId, asset_type: 'EM', page: 1, page_size: 1000 })
      return {
        ...group,
        assets
      }
    })
    const emailAssets = (await Promise.all(promises)).filter(Boolean)

    // 获取邮件发送总数量
    statistics.value.totalEmails += emailAssets.reduce((total, group) => {
      return total + (group.assets?.length || 0)
    }, 0)
  }
}

// 获取邮件发送记录,参数演练任务id
const fetchEmailRecords = async (id) => {
  const response = await exerciseApi.getEmailLogApi(id)
  emailRecords.value = response.results
  // 更新邮件统计信息
  statistics.value.clickedEmails = response.results.filter(record => record.is_click).length
}


const targetAssetsInfectionList = ref([])

// 获取目标资产感染详情
const getTargetAssetsInfection = async (page = 1) => {
  const response = await exerciseApi.getTargetAssetsInfectionApi(id, { page: page })
  targetAssetsInfectionList.value = response.results
  targetTotal.value = response.count
}

// 格式化时间
const formatDateTime = (dateTimeStr) => {
  return new Date(dateTimeStr).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

// 邮件记录数据
const emailRecords = ref([])
const emailCurrentPage = ref(1)
const emailPageSize = ref(10)

// 邮件列表配置
const emailColumns = [
  {
    key: 'recipient',
    title: t('table.recipient'),
    slot: 'recipient',
    width: 200
  },
  {
    key: 'email_subject',
    title: t('table.email_subject'),
    slot: 'email_subject',
    width: 250
  },
  {
    key: 'send_time',
    title: t('table.send_time'),
    slot: 'send_time',
    width: 150
  },
  {
    key: 'is_click',
    title: t('table.click_status'),
    slot: 'is_click',
    width: 100
  }
]

// 邮件分页配置
const emailPagination = computed(() => ({
  currentPage: emailCurrentPage.value,
  pageSize: emailPageSize.value,
  total: emailRecords.value.length
}))

// 分页后的邮件记录
const paginatedEmailRecords = computed(() => {
  const start = (emailCurrentPage.value - 1) * emailPageSize.value
  const end = start + emailPageSize.value
  return emailRecords.value.slice(start, end)
})

const handleEmailPageChange = (page) => {
  emailCurrentPage.value = page
}

const getStatusStyle = (status) => {
  const styles = {
    'PE': 'bg-blue-100 text-blue-800 dark:bg-blue-900/50 dark:text-blue-400',
    'RU': 'bg-emerald-100 text-emerald-800 dark:bg-emerald-900/50 dark:text-emerald-400',
    'FI': 'bg-purple-100 text-purple-800 dark:bg-purple-900/50 dark:text-purple-400'
  }
  return styles[status] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
}

const getStatusText = (status) => {
  const texts = {
    'PE': t('exercise.not_started'),
    'RU': t('exercise.in_progress'),
    'FI': t('exercise.completed')
  }
  return texts[status] || '未知状态'
}

// 设备感染率
const infectionRate = computed(() => {
  if (!statistics.value.totalDevices) return 0
  return Math.round((statistics.value.infectedDevices / statistics.value.totalDevices) * 100)
})

// 邮件点击率
const clicksRate = computed(() => {
  if (!statistics.value.totalEmails) return 0
  return ((statistics.value.clickedEmails / statistics.value.totalEmails) * 100).toFixed(2)
})

// 导出相关方法
const openExportModal = () => {
  showExportModal.value = true
}

const closeExportModal = () => {
  showExportModal.value = false
}

const handleExported = () => {
  // 导出成功后的处理
  console.log('报告导出成功')
}

onMounted(async () => {
  await fetchReportDetail()
  await getTargetAssetsInfection()
})
</script>