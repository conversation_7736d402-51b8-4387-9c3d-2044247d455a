<template>
  <div class="p-6 bg-gray-50 dark:bg-gray-900 min-h-screen">
    <!-- 页面标题 -->
    <div class="mb-8">
      <h1 class="text-3xl font-bold bg-gradient-to-r from-blue-600 to-blue-400 bg-clip-text text-transparent">
        {{ $t('assets.title') }}
      </h1>
      <p class="mt-2 text-sm text-gray-500 dark:text-gray-400">
        {{ $t('assets.subtitle') }}
      </p>
    </div>

    <!-- 操作按钮 -->
    <div class="flex justify-end space-x-4 mb-6">
      <NuxtLink to="/assets/groups"
        class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <Icon name="heroicons:users" class="-ml-1 mr-2 h-5 w-5 text-gray-500" />
        {{ $t('assets.asset_group_management') }}
      </NuxtLink>
      <button @click="showImportModal = true"
        class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <Icon name="heroicons:cloud-arrow-up" class="-ml-1 mr-2 h-5 w-5 text-gray-500" />
        {{ $t('all.import_multiple') }}
      </button>
      <button @click="showAssetForm = true"
        class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
        <Icon name="heroicons:plus" class="-ml-1 mr-2 h-5 w-5" />
        {{ $t('all.create') }}{{ $t('assets.asset') }}
      </button>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-6">
      <StatCard :title="$t('assets.total_assets')" :value="number.results.total || 0" color="blue">
        <template #icon>
          <Icon name="heroicons:server" class="w-6 h-6" />
        </template>
      </StatCard>

      <StatCard :title="$t('assets.terminal_equipment')" :value="number.results.EP || 0" color="green">
        <template #icon>
          <Icon name="heroicons:computer-desktop" class="w-6 h-6" />
        </template>
      </StatCard>

      <StatCard :title="$t('assets.server')" :value="number.results.SV || 0" color="red">
        <template #icon>
          <Icon name="heroicons:server" class="w-6 h-6" />
        </template>
      </StatCard>

      <StatCard :title="$t('assets.email')" :value="number.results.EM || 0" color="yellow">
        <template #icon>
          <Icon name="heroicons:envelope" class="w-6 h-6" />
        </template>
      </StatCard>
    </div>

    <!-- 数据表格卡片 -->
    <div class="bg-white dark:bg-gray-800 shadow-md rounded-lg p-4 mb-4">
      <!-- 标签 -->
      <Tabs :tabs="tabsItems" v-model="activeTabs">
        <!-- 搜索和筛选 -->
        <div class="p-4 border-b border-gray-200 dark:border-gray-700">
          <SearchFilter v-model="filters" :search-value="searchQuery" @update:search-value="val => searchQuery = val"
            :search-placeholder="$t('assets.placeholder_search')" @search="handleSearch" :show-search-button="true"
            :search-button-text="$t('all.search')" :selects="selects" :show-delete-button="true"
            @delete-all="handleDeleteAll" />
        </div>

        <!-- 加载状态 -->
        <div v-if="pending" class="flex justify-center items-center h-64">
          <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
        </div>

        <!-- 错误提示 -->
        <div v-else-if="error" class="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
          <div class="flex">
            <div class="flex-shrink-0">
              <Icon name="heroicons:x-circle" class="h-5 w-5 text-red-400" />
            </div>
            <div class="ml-3">
              <p class="text-sm text-red-700">{{ error }}</p>
            </div>
          </div>
        </div>

        <!-- 数据表格 -->
        <div v-else class="bg-white dark:bg-gray-800 rounded-lg shadow">
          <div v-if="activeTabs === 'SV' || activeTabs === 'EP'">
            <DataTable :columns="[
              { title: $t('table.asset_name'), key: 'name', width: 200 },
              { title: $t('table.asset_type'), key: 'asset_type', slot: 'asset_type', width: 120 },
              { title: $t('table.ip_address'), key: 'ip_address_v4', width: 150 },
              { title: $t('table.mac_address'), key: 'mac_address', width: 150 },
              { title: $t('table.user_group'), key: 'username', width: 120 },
              { title: $t('table.action'), key: 'actions', slot: 'actions', width: 100 }
            ]" :data="filteredData" :loading="pending" :pagination="pagination" @page-change="handlePageChange"
              :row-selection="rowSelection" @select="() => { rowSelection.selectedRowKeys = $event }">
              <!-- 资产类型列 -->
              <template #asset_type="{ row }">
                <span :class="[
                  'px-3 py-1 text-xs font-medium rounded-full',
                  getTypeStyle(row.asset_type)
                ]">
                  {{ TYPE_MAP[row.asset_type] || '-' }}
                </span>
              </template>

              <!-- 操作列 -->
              <template #actions="{ row }">
                <div class="flex items-center space-x-2">
                  <button @click="handleEdit(row)"
                    class="p-1 text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button @click="handleDelete(row)"
                    class="p-1 text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                    <Icon name="heroicons:trash" class="w-5 h-5" />
                  </button>
                  <button @click="handleView(row)"
                    class="p-1 text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300">
                    <Icon name="heroicons:eye" class="w-5 h-5" />
                  </button>
                </div>
              </template>
            </DataTable>
          </div>

          <div v-else>
            <DataTable :columns="[
              { title: $t('table.asset_type'), key: 'asset_type', slot: 'asset_type', width: 120 },
              { title: $t('table.user_group'), key: 'username', width: 120 },
              { title: $t('table.email'), key: 'email', width: 120 },
              { title: $t('table.action'), key: 'actions', slot: 'actions', width: 100 }
            ]" :data="filteredData" :loading="pending" :pagination="pagination" @page-change="handlePageChange"
              :row-selection="rowSelection" @select="() => { rowSelection.selectedRowKeys = $event }">
              <!-- 资产类型列 -->
              <template #asset_type="{ row }">
                <span :class="[
                  'px-3 py-1 text-xs font-medium rounded-full',
                  getTypeStyle(row.asset_type)
                ]">
                  {{ TYPE_MAP[row.asset_type] || '-' }}
                </span>
              </template>

              <!-- 操作列 -->
              <template #actions="{ row }">
                <div class="flex items-center space-x-2">
                  <button @click="handleEdit(row)"
                    class="p-1 text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                        d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                  </button>
                  <button @click="handleDelete(row)"
                    class="p-1 text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                    <Icon name="heroicons:trash" class="w-5 h-5" />
                  </button>
                  <button @click="handleView(row)"
                    class="p-1 text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300">
                    <Icon name="heroicons:eye" class="w-5 h-5" />
                  </button>
                </div>
              </template>
            </DataTable>
          </div>
        </div>
      </Tabs>
    </div>

    <!-- 资产表单弹窗 -->
    <AssetFormModal v-if="showAssetForm" :asset="currentAsset" @close="closeAssetForm" @submit="handleFormSubmit" />

    <!-- 导入模态框 -->
    <AssetImportModal v-if="showImportModal" @close="closeImportModal" @import-success="handleImportSuccess" />

    <!-- 删除确认弹窗 -->
    <ConfirmDialog :show="showDeleteConfirm" :title="`${$t('all.delete')}${$t('assets.asset')}`"
      :message="deleteConfirmMessage" type="danger" @confirm="confirmDelete" @cancel="showDeleteConfirm = false" />

    <!-- 删除多个确认弹窗 -->
    <ConfirmDialog :show="showDeleteAllConfirm" :title="`${$t('all.delete')}${$t('assets.asset')}`"
      :message="deleteAllConfirmMessage" type="danger" @confirm="confirmDelete"
      @cancel="showDeleteAllConfirm = false" />

    <!-- 资产详情弹窗 -->
    <AssetDetailModal v-if="currentViewAsset" v-model="showDetailModal" :asset="currentViewAsset"
      @close="closeDetailModal" />
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import StatCard from '~/components/StatCard.vue'
import Tabs from '~/components/common/Tabs.vue'
import SearchFilter from '~/components/common/SearchFilter.vue'
import DataTable from '~/components/common/DataTable.vue'
import AssetFormModal from '~/components/assets/AssetFormModal.vue'
import AssetImportModal from '~/components/assets/AssetImportModal.vue'
import ConfirmDialog from '~/components/common/ConfirmDialog.vue'
import AssetDetailModal from '~/components/assets/AssetDetailModal.vue'
import { assetApi } from '~/api/asset'
import { debounce } from 'lodash-es'
import { useToast } from '~/composables/useToast'
import { useAsyncData, useLazyAsyncData } from '#app'

const toast = useToast()
const { t } = useI18n()
const showAssetForm = ref(false)
const showImportModal = ref(false)
const showDetailModal = ref(false)
const currentAsset = ref(null)
const currentViewAsset = ref(null)
const activeTabs = ref('SV')
const isEdit = ref(false)
const rowSelection = ref({
  selectedRowKeys: [],
  onChange: (selectedRowKeys) => {
    rowSelection.value.selectedRowKeys = selectedRowKeys
  }
})

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)

// 搜索和筛选
const searchQuery = ref('')
const filters = ref({
  asset_type: ''
})

// 下拉选项
const selects = ref([])

// 资产类型映射
const TYPE_MAP = {
  EP: t('assets.terminal_equipment'),
  SV: t('assets.server'),
  EM: t('assets.email')
}

// 标签列表
const tabsItems = [
  { label: t('assets.server'), value: 'SV', key: 'SV' },
  { label: t('assets.terminal_equipment'), value: 'EP', key: 'EP' },
  { label: t('assets.email'), value: 'EM', key: 'EM' }
]

// 获取类型对应的样式
const getTypeStyle = (type) => {
  const styles = {
    EP: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300',
    SV: 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300',
    EM: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300'
  }
  return styles[type] || 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
}

// 使用防抖的搜索处理函数
const debouncedSearch = debounce((value) => {
  searchQuery.value = value
  currentPage.value = 1
  refresh()
}, 300)

// 优化后的数据获取逻辑
const { data, pending, error, refresh } = await useLazyAsyncData(
  'assets',
  async () => {
    try {
      const params = {
        page: currentPage.value,
        page_size: pageSize.value,
        search: searchQuery.value,
        asset_type: activeTabs.value
      }
      const response = await assetApi.getAssets(params)
      return response
    } catch (err) {
      console.error('Failed to fetch assets:', err)
      return { count: 0, results: [] }
    }
  },
  {
    server: false,
    lazy: true
  }
)

// 获取资产数量
const { data: number, refresh: getAssetsNumber } = await useAsyncData(
  'getAssetsNumber', async () => {
    const response = await assetApi.getAssetsNumberApi()
    return response
  }
)

// 优化计算属性
const assetsByType = computed(() => {
  const results = data.value?.results || []
  return {
    EP: results.filter(asset => asset?.asset_type === 'EP'),
    SV: results.filter(asset => asset?.asset_type === 'SV'),
    EM: results.filter(asset => asset?.asset_type === 'EM')
  }
})

const filteredData = computed(() => assetsByType.value[activeTabs.value] || [])
const totalCount = computed(() => data.value?.count || 0)
const terminalCount = computed(() => assetsByType.value.EP.length)
const serverCount = computed(() => assetsByType.value.SV.length)
const emailCount = computed(() => assetsByType.value.EM.length)

// 分页对象
const pagination = computed(() => ({
  currentPage: currentPage.value,
  pageSize: pageSize.value,
  total: totalCount.value
}))

// 处理分页
const handlePageChange = (page) => {
  currentPage.value = page
  refresh()
}

// 处理搜索
const handleSearch = (value) => {
  debouncedSearch(value)
}

// 优化监听器
watch([filters, activeTabs], debounce(() => {
  currentPage.value = 1
  refresh()
}, 300), { deep: true })

// 处理编辑
const handleEdit = (asset) => {
  currentAsset.value = asset
  isEdit.value = true
  showAssetForm.value = true
}

// 删除相关的状态
const showDeleteConfirm = ref(false)
const showDeleteAllConfirm = ref(false)
const assetToDelete = ref(null)

// 删除确认消息
const deleteConfirmMessage = computed(() => {
  if (!assetToDelete.value) return ''
  return t('assets.delete_message', { name: assetToDelete.value.name || assetToDelete.value.email })
})

// 批量删除确认消息
const deleteAllConfirmMessage = computed(() => {
  return t('all.delete_all_message')
})

// 处理删除
const handleDelete = (asset) => {
  assetToDelete.value = { ...asset }
  showDeleteConfirm.value = true
}

// 确认删除
const confirmDelete = async () => {
  if (!assetToDelete.value?.id && rowSelection.value.selectedRowKeys.length === 0) return

  // 单个删除
  if (showDeleteConfirm.value) {
    try {
      await assetApi.deleteAsset(assetToDelete.value.id)
      await refresh()
      toast.success(t('all.delete_success'))
      rowSelection.value.selectedRowKeys = []
    } catch (error) {
      console.error('Failed to delete asset:', error)
      toast.error(t('all.delete_failed'))
    } finally {
      showDeleteConfirm.value = false
      assetToDelete.value = null
    }
  }

  // 多个删除
  if (showDeleteAllConfirm.value) {
    try {
      await assetApi.deleteAllAssets({ ids: rowSelection.value.selectedRowKeys.join(',') })
      await refresh()
      toast.success(t('all.delete_success'))
      rowSelection.value.selectedRowKeys = []
    } catch (error) {
      console.error('Failed to delete asset:', error)
      toast.error(t('all.delete_failed'))
    } finally {
      showDeleteAllConfirm.value = false
    }
  }

}

// 确认删除资产
const handleDeleteAll = async () => {
  if (rowSelection.value.selectedRowKeys.length === 0) {
    toast.error(t('all.select_one'))
    return
  }

  showDeleteAllConfirm.value = true
}

// 关闭资产表单
const closeAssetForm = () => {
  showAssetForm.value = false
  currentAsset.value = null
  isEdit.value = false
}

// 处理表单提交
const handleFormSubmit = async (formData) => {
  const params = {
    ...formData,
    ip_address_v4: formData.ip_address_v4 === null ? '' : formData.ip_address_v4,
    ip_address_v6: formData.ip_address_v6 === null ? '' : formData.ip_address_v6
  }

  try {
    if (isEdit.value) {
      await assetApi.updateAsset(currentAsset.value.id, params)
    } else {
      await assetApi.createAsset(params)
    }
    await refresh()
    await getAssetsNumber()
    closeAssetForm()
  } catch (error) {
    console.error('保存资产失败:', error)
  }
}

// 关闭导入模态框
const closeImportModal = () => {
  showImportModal.value = false
}

// 处理导入成功
const handleImportSuccess = async () => {
  // 刷新资产列表
  await fetchAssets()
  // 刷新资产数量统计
  await fetchAssetsNumber()
}

// 处理查看
const handleView = (asset) => {
  currentViewAsset.value = asset
  showDetailModal.value = true
}

// 关闭详情弹窗
const closeDetailModal = () => {
  showDetailModal.value = false
  currentViewAsset.value = null
}
</script>