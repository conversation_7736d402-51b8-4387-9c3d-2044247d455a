<template>
  <label class="inline-flex items-center relative">
    <input 
      class="peer hidden" 
      type="checkbox"
      :checked="isDark"
      @change="toggleTheme"
    />
    <div
      class="relative w-[80px] h-[41px] bg-white/80 backdrop-blur-sm border border-gray-100 peer-checked:bg-zinc-800/90 peer-checked:border-zinc-700 rounded-full after:absolute after:content-[''] after:w-[31px] after:h-[31px] after:bg-gradient-to-r from-amber-400 to-orange-400 peer-checked:after:from-blue-400 peer-checked:after:to-indigo-400 after:rounded-full after:top-[5px] after:left-[5px] active:after:w-[37px] peer-checked:after:left-[75px] peer-checked:after:translate-x-[-100%] shadow-inner hover:shadow-lg duration-300 after:duration-300 after:shadow-lg"
    ></div>
    <!-- 太阳图标 -->
    <Icon name="heroicons:sun"
      class="fill-amber-500 peer-checked:opacity-60 peer-checked:fill-blue-300 absolute w-5 h-5 left-[8px] drop-shadow-sm" />
    <!-- 月亮图标 -->
    <Icon name="heroicons:moon"
      class="fill-amber-500 opacity-60 peer-checked:opacity-90 peer-checked:fill-blue-300 absolute w-5 h-5 right-[8px] drop-shadow-sm" />
  </label>
</template>

<script setup>
import { ref, onMounted } from 'vue'

const isDark = ref(false)

// 切换主题
const toggleTheme = () => {
  isDark.value = !isDark.value
  updateTheme()
}

// 更新主题
const updateTheme = () => {
  // 更新 HTML 的 class
  if (isDark.value) {
    document.documentElement.classList.add('dark')
  } else {
    document.documentElement.classList.remove('dark')
  }
  // 保存到本地存储
  localStorage.setItem('theme', isDark.value ? 'dark' : 'light')
}

// 初始化主题
const initTheme = () => {
  // 优先使用本地存储的主题
  const savedTheme = localStorage.getItem('theme')
  if (savedTheme) {
    isDark.value = savedTheme === 'dark'
  } else {
    // 否则使用系统主题
    isDark.value = window.matchMedia('(prefers-color-scheme: dark)').matches
  }
  updateTheme()
}

onMounted(() => {
  initTheme()
})
</script>
