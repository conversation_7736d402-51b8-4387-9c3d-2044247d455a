from django.db import models
from apps.users.models import User
from config.models import BaseModel
from apps.family.models import VirusFamily


class Virus(BaseModel):
    """病毒样本模型"""

    family = models.ForeignKey(verbose_name="病毒家族", to="family.VirusFamily", on_delete=models.SET_NULL,
                               null=True, blank=True, related_name="virus")
    name = models.CharField(verbose_name="名称", max_length=200, null=True, blank=True)
    suffix = models.CharField("病毒后缀", max_length=300, null=True, blank=True)
    encryptor = models.CharField(verbose_name="加密器", max_length=1000, null=True, blank=True)
    encryptor_name = models.CharField(verbose_name="加密器名称", max_length=1000, null=True, blank=True)
    ransom_note_name = models.CharField(verbose_name="勒索信名称", max_length=300, null=True, blank=True)
    wallpaper = models.Cha<PERSON><PERSON><PERSON>(verbose_name="壁纸", max_length=1000, null=True, blank=True)
    source_code = models.TextField(verbose_name="勒索信内容", null=True, blank=True)
    encrypto_key = models.CharField(verbose_name="对称加密密钥", max_length=100, null=True, blank=True)
    encrypto_iv = models.CharField(verbose_name="对称加密向量", max_length=100, null=True, blank=True)
    target_dir = models.TextField(verbose_name="target_dir", null=True, blank=True)
    encrypted_file_types = models.CharField("加密的文件类型", max_length=300, null=True, blank=True)

    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name="virus",
        verbose_name="创建者",
    )

    class Meta:
        db_table = 'ls_virus'
        verbose_name = "病毒样本"
        verbose_name_plural = verbose_name
        ordering = ["-created_at"]

    def __str__(self):
        return self.name


class RansomTemplate(BaseModel):
    """勒索信模板"""

    name = models.CharField("模板名称", max_length=100)
    content = models.TextField("勒索信内容")
    language = models.CharField("语言", max_length=50, default="中文")
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name="created_templates",
        verbose_name="创建者",
    )

    class Meta:
        verbose_name = "勒索信模板"
        verbose_name_plural = verbose_name
        ordering = ["-created_at"]

    def __str__(self):
        return self.name


class NegotiationModel(BaseModel):
    """
    谈判配置
    """
    n_id = models.CharField(verbose_name="谈判配置手动填入的ID", max_length=100,unique=True)
    platform_name = models.CharField(verbose_name="平台名称", max_length=200, null=True, blank=True)
    company_name = models.CharField(verbose_name="公司名称", max_length=200, null=True, blank=True)
    official_website = models.CharField(verbose_name="官网链接", max_length=300, null=True, blank=True)
    index_show_count = models.IntegerField(verbose_name="首页展示数", null=True, blank=True)
    company_introduction = models.TextField(verbose_name="公司介绍", null=True, blank=True)
    company_logo = models.FileField(verbose_name="公司logo", upload_to="company_logo/", null=True, blank=True)
    company_valuation = models.CharField(verbose_name="公司估值", max_length=20, null=True, blank=True)
    stolen_data_volume = models.CharField(verbose_name="窃取数据量", max_length=100, null=True, blank=True)
    usdt_ransom_amount = models.CharField(verbose_name="USDT赎金", max_length=100, null=True, blank=True)
    btc_ransom_amount = models.CharField(verbose_name="BTC赎金", max_length=100, null=True, blank=True)
    btc_address = models.CharField(verbose_name="BTC地址", max_length=100, null=True, blank=True)
    usdt_address = models.CharField(verbose_name="USDT地址", max_length=100, null=True, blank=True)
    deadline = models.DateTimeField(verbose_name="截止日期", null=True, blank=True)
    enable_auto_reply = models.BooleanField(verbose_name="是否开启自动回复", default=True)

    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name="negotiations",
        verbose_name="创建者",
    )

    class Meta:
        db_table = 'ls_negotiation'
        verbose_name = "谈判配置"
        verbose_name_plural = verbose_name
        ordering = ["-created_at"]
