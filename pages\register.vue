<template>
  <div class="min-h-screen flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8 relative"
    style="background: linear-gradient(135deg, #f0f4f8 0%, #e2e8f0 50%, #f8fafc 100%)">
    <!-- Back to Home Link -->
    <div class="absolute top-4 left-4">
      <NuxtLink to="/login"
        class="flex items-center text-gray-700 hover:text-gray-900 transition-colors backdrop-blur-sm bg-white/80 px-3 py-2 rounded-lg shadow-sm">
        <Icon name="heroicons:arrow-left" class="w-5 h-5 mr-2" />
        {{ $t('all.back') }}
      </NuxtLink>
    </div>

    <div
      class="max-w-2xl w-full space-y-8 bg-white/95 backdrop-blur-lg p-8 rounded-xl shadow-2xl border border-white/20">
      <div>
        <h2 class="text-center text-2xl font-bold tracking-tight text-gray-900">
          {{ settings?.systemName || ($t('home.first.title_1') + ' ' + $t('home.first.title_2')) }}
        </h2>
        <p class="mt-2 text-center text-sm text-gray-600">
          {{ $t('register.subtitle') }}
        </p>
      </div>

      <div class="mb-4 border-b border-gray-200">
        <ul class="flex flex-wrap -mb-px text-sm font-medium text-center" role="tablist">
          <li class="mr-2" role="presentation">
            <button class="inline-block p-4 border-b-2 rounded-t-lg text-blue-600 border-blue-600" type="button"
              role="tab">
              {{ $t('register.register_1') }}
            </button>
          </li>
        </ul>
      </div>

      <!-- 账号密码登录表单 -->
      <form class="mt-8 space-y-6" @submit.prevent="handleAccountSubmit">
        <Alert v-if="error" type="error" :message="error" class="mb-4" />
        <Alert v-if="success" type="success" :message="success" class="mb-4" />

        <div class="grid grid-cols-2 gap-6">
          <div>
            <label for="username" class="block mb-2 text-sm font-medium text-gray-900">
              {{ $t('table.username') }}
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Icon name="heroicons:user" class="w-4 h-4 text-gray-500" />
              </div>
              <input id="username" v-model="accountForm.username" type="text" required
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5"
                :placeholder="$t('users.placeholder_username')">
            </div>
          </div>

          <div>
            <label for="phone" class="block mb-2 text-sm font-medium text-gray-900">
              {{ $t('table.phone') }}
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Icon name="heroicons:phone" class="w-4 h-4 text-gray-500" />
              </div>
              <input id="phone" v-model="accountForm.phone" type="tel" required
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5"
                :placeholder="$t('users.placeholder_phone')">
            </div>
          </div>

          <div>
            <label for="code" class="block mb-2 text-sm font-medium text-gray-900">
              {{ $t('login.code') }}
            </label>
            <div class="relative flex space-x-2">
              <div class="relative flex-1">
                <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <Icon name="heroicons:key" class="w-4 h-4 text-gray-500" />
                </div>
                <input id="code" v-model="accountForm.code" type="text" required
                  class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5"
                  :placeholder="$t('login.placeholder_code')">
              </div>
              <button type="button" :disabled="countdown > 0"
                class="px-4 py-2 text-sm font-medium text-blue-700 bg-blue-100 rounded-lg hover:bg-blue-200 focus:ring-4 focus:outline-none focus:ring-blue-300 disabled:opacity-50 disabled:cursor-not-allowed whitespace-nowrap"
                @click="sendCode">
                {{ countdown > 0 ? $t('login.retry_code', { second: countdown }) : $t('login.get_code') }}
              </button>
            </div>
          </div>

          <div>
            <label for="password" class="block mb-2 text-sm font-medium text-gray-900">
              {{ $t('table.password') }}
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Icon name="heroicons:lock-closed" class="w-4 h-4 text-gray-500" />
              </div>
              <input id="password" v-model="accountForm.password" :type="showPassword ? 'text' : 'password'" required
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 pr-10 p-2.5"
                :placeholder="$t('users.placeholder_password')">
              <button type="button" class="absolute inset-y-0 right-0 flex items-center pr-3"
                @click="showPassword = !showPassword">
                <Icon v-if="showPassword" name="heroicons:eye" class="w-4 h-4 text-gray-500 hover:text-gray-700" />
                <Icon v-else name="heroicons:eye-slash" class="w-4 h-4 text-gray-500 hover:text-gray-700" />
              </button>
            </div>
          </div>

          <div>
            <label for="confirm_password" class="block mb-2 text-sm font-medium text-gray-900">
              {{ $t('register.confirm_password') }}
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Icon name="heroicons:lock-closed" class="w-4 h-4 text-gray-500" />
              </div>
              <input id="confirm_password" v-model="accountForm.confirm_password"
                :type="showConfirmPassword ? 'text' : 'password'" required
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 pr-10 p-2.5"
                :placeholder="$t('register.placeholder_confirm_password')">
              <button type="button" class="absolute inset-y-0 right-0 flex items-center pr-3"
                @click="showConfirmPassword = !showConfirmPassword">
                <Icon v-if="showConfirmPassword" name="heroicons:eye"
                  class="w-4 h-4 text-gray-500 hover:text-gray-700" />
                <Icon v-else name="heroicons:eye-slash" class="w-4 h-4 text-gray-500 hover:text-gray-700" />
              </button>
            </div>
          </div>

          <div>
            <label for="invitation_code" class="block mb-2 text-sm font-medium text-gray-900">
              {{ $t('register.invitation_code') }}
            </label>
            <div class="relative">
              <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                <Icon name="heroicons:code-bracket" class="w-4 h-4 text-gray-500" />
              </div>
              <input id="invitation_code" v-model="accountForm.invitation_code" type="text"
                class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5"
                :placeholder="$t('register.placeholder_invitation_code')">
            </div>
          </div>
        </div>

        <div>
          <label for="company_name" class="block mb-2 text-sm font-medium text-gray-900">
            {{ $t('register.company_name') }}
          </label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
              <Icon name="heroicons:building-office" class="w-4 h-4 text-gray-500" />
            </div>
            <input id="company_name" v-model="accountForm.company_name" type="text"
              class="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-blue-500 focus:border-blue-500 block w-full pl-10 p-2.5"
              :placeholder="$t('register.placeholder_company_name')">
          </div>
        </div>

        <div>
          <button type="submit" :disabled="loading"
            class="w-full flex justify-center py-2.5 px-5 text-sm font-medium text-white bg-blue-700 rounded-lg border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 disabled:opacity-50 disabled:cursor-not-allowed">
            <LoadingSpinner v-if="loading" class="mr-2" />
            {{ loading ? $t('register.registering') : $t('register.register') }}
          </button>
        </div>

        <div class="text-sm text-gray-600 text-center">
          {{ $t('register.login') }}
          <NuxtLink to="/login" class="text-blue-600 hover:underline">
            {{ $t('register.click_to_login') }}
          </NuxtLink>
        </div>
      </form>
    </div>
  </div>
</template>

<script setup>
definePageMeta({
  layout: 'empty'
})

import { useI18n } from '#imports'
import { useAuthStore } from '~/stores/auth'
import { useSystemSettings } from '~/composables/useSystemSettings'
import Alert from '~/components/common/Alert.vue'
import LoadingSpinner from '~/components/common/LoadingSpinner.vue'
import { useToast } from '~/composables/useToast'

const { t } = useI18n()
const toast = useToast()
const authStore = useAuthStore()
const router = useRouter()
const { settings } = useSystemSettings()

// 设置页面标题
const pageTitle = computed(() => {
  const systemName = settings.value?.systemName
  if (systemName) {
    return `${t('register.register')} - ${systemName}`
  }
  return `${t('register.register')} - ${t('header.title')}`
})

useHead({
  title: pageTitle
})

const loading = ref(false)
const error = ref('')
const success = ref('')
const countdown = ref(0)
const showPassword = ref(false)
const showConfirmPassword = ref(false)

// 账号密码登录表单
const accountForm = ref({
  username: '',
  phone: '',
  code: '',
  password: '',
  confirm_password: '',
  invitation_code: '',
  company_name: ''
})

const handleAccountSubmit = async () => {
  loading.value = true
  error.value = ''
  success.value = ''

  if (accountForm.value.password !== accountForm.value.confirm_password) {
    error.value = t('register.error_3') // 密码不一致
    loading.value = false
    return
  }

  try {
    console.log('Submitting credentials:', accountForm.value) // 调试日志
    const result = await authStore.register(accountForm.value)

    console.log('Login result:', result) // 调试日志
    if (result.success) {
      toast.success(t('register.success_1'))
      router.push('/login')
    } else {
      // 根据错误类型显示不同的错误消息
      if (result.error === 'auth_error') {
        error.value = t('login.error_1') // 用户名或密码错误
      } else if (result.error === 'network_error') {
        error.value = result.message || t('login.error_3') // 网络错误
      } else {
        error.value = result.message || t('login.error_2') // 其他错误
      }
    }
  } catch (e) {
    console.error('Login error:', e?.response?.data || e) // 增强错误日志
    error.value = t('login.error_2') // 未知错误
  } finally {
    loading.value = false
  }
}

const sendCode = async () => {
  if (countdown.value > 0) return

  // 验证手机号格式
  const phoneRegex = /^1[3-9]\d{9}$/
  if (!phoneRegex.test(accountForm.value.phone)) {
    error.value = t('login.error_4')
    success.value = ''
    return
  }

  error.value = ''
  success.value = ''
  try {
    const isSuccess = await authStore.sendVerificationCode(accountForm.value.phone)
    if (isSuccess) {
      // 开始倒计时
      countdown.value = 60
      const timer = setInterval(() => {
        countdown.value--
        if (countdown.value <= 0) {
          clearInterval(timer)
        }
      }, 1000)
      success.value = t('login.success_1')
    } else {
      error.value = t('login.error_5')
    }
  } catch (e) {
    error.value = t('login.error_5')
  }
}

onMounted(() => {
  // 如果已经认证，直接跳转到仪表盘
  // 避免重复的认证检查，因为 plugins/auth.ts 已经处理了
  if (authStore.isAuthenticated) {
    router.push('/dashboard')
  }
})
</script>