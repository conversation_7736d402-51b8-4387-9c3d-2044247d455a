from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from apps.infection.models import DeviceCommand


class Command(BaseCommand):
    help = '清理旧的任务记录'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='清理多少天前的任务，默认7天',
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='预览模式，不实际删除',
        )
        parser.add_argument(
            '--fix-status',
            action='store_true',
            help='修复旧的布尔状态为新的字符串状态',
        )
        parser.add_argument(
            '--cleanup-all-commands',
            action='store_true',
            help='清理所有"all"命令记录',
        )

    def handle(self, *args, **options):
        days = options['days']
        dry_run = options['dry_run']
        fix_status = options['fix_status']
        cleanup_all_commands = options['cleanup_all_commands']

        # 计算时间阈值
        threshold_time = timezone.now() - timedelta(days=days)

        if fix_status:
            self.fix_old_status_format()

        if cleanup_all_commands:
            self.cleanup_all_commands(dry_run)
        
        # 查找旧任务
        old_tasks = DeviceCommand.objects.filter(created_at__lt=threshold_time)
        
        # 按状态分组统计
        status_counts = {}
        for task in old_tasks:
            status = task.status
            status_counts[status] = status_counts.get(status, 0) + 1
        
        self.stdout.write(f"找到 {old_tasks.count()} 个超过 {days} 天的旧任务:")
        for status, count in status_counts.items():
            self.stdout.write(f"  状态 '{status}': {count} 个")
        
        if dry_run:
            self.stdout.write(self.style.WARNING("这是预览模式，没有实际删除任务"))
        else:
            if old_tasks.exists():
                deleted_count = old_tasks.count()
                old_tasks.delete()
                self.stdout.write(
                    self.style.SUCCESS(f"已删除 {deleted_count} 个旧任务")
                )
            else:
                self.stdout.write("没有需要删除的旧任务")

    def fix_old_status_format(self):
        """修复旧的布尔状态格式"""
        self.stdout.write("正在修复旧的状态格式...")
        
        # 修复 status=True 的任务
        true_tasks = DeviceCommand.objects.filter(status=True)
        if true_tasks.exists():
            updated = true_tasks.update(status='SUCCESS')
            self.stdout.write(f"  将 {updated} 个 status=True 的任务更新为 SUCCESS")
        
        # 修复 status=False 的任务
        false_tasks = DeviceCommand.objects.filter(status=False)
        if false_tasks.exists():
            updated = false_tasks.update(status='FAILED')
            self.stdout.write(f"  将 {updated} 个 status=False 的任务更新为 FAILED")
        
        # 修复字符串 'true' 和 'false'
        str_true_tasks = DeviceCommand.objects.filter(status='true')
        if str_true_tasks.exists():
            updated = str_true_tasks.update(status='SUCCESS')
            self.stdout.write(f"  将 {updated} 个 status='true' 的任务更新为 SUCCESS")
        
        str_false_tasks = DeviceCommand.objects.filter(status='false')
        if str_false_tasks.exists():
            updated = str_false_tasks.update(status='FAILED')
            self.stdout.write(f"  将 {updated} 个 status='false' 的任务更新为 FAILED")
        
        self.stdout.write(self.style.SUCCESS("状态格式修复完成"))

    def cleanup_all_commands(self, dry_run=False):
        """清理所有"all"命令记录"""
        self.stdout.write("正在清理'all'命令记录...")

        # 查找所有"all"命令记录
        all_commands = DeviceCommand.objects.filter(command='all')
        total_count = all_commands.count()

        if total_count == 0:
            self.stdout.write("没有找到'all'命令记录")
            return

        # 按状态统计
        status_counts = {}
        for task in all_commands:
            status = task.status
            status_counts[status] = status_counts.get(status, 0) + 1

        self.stdout.write(f"找到 {total_count} 个'all'命令记录:")
        for status, count in status_counts.items():
            self.stdout.write(f"  状态 '{status}': {count} 个")

        if dry_run:
            self.stdout.write(self.style.WARNING("这是预览模式，没有实际删除记录"))
        else:
            deleted_count = all_commands.delete()[0]
            self.stdout.write(
                self.style.SUCCESS(f"已删除 {deleted_count} 个'all'命令记录")
            )
