import { useAuthStore } from '~/stores/auth'
import { useStorage } from '~/composables/useStorage'

export default defineNuxtPlugin(() => {
  const getStorage = () => {
    if (process.client) {
      return window.localStorage
    }
    return {
      getItem: () => null,
      setItem: () => null,
      removeItem: () => null
    }
  }

  return {
    provide: {
      storage: getStorage()
    }
  }
})
