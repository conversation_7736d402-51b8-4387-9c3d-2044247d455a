from django.db.models import Q
from django.http import HttpResponse
import requests
from django.shortcuts import get_object_or_404
from drf_spectacular.utils import extend_schema_view, extend_schema
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.viewsets import GenericViewSet

from extensions import email_utils, queryset_utils
from . import models
from rest_framework import viewsets, permissions, status, filters, mixins
from .serializers import StrategySerializer, EmailTemplateFileSerializer
from . import serializers
from drf_spectacular.utils import OpenApiParameter


@extend_schema_view(
    list=extend_schema(
        summary="获取邮件策略列表",
        description="获取所有邮件策略信息",
        tags=["邮件策略"],
        parameters=[
            OpenApiParameter(
                name='search',
                description='搜索策略名称',
                type=str,
                required=False
            )
        ]
    ),
    create=extend_schema(
        summary="创建邮件策略",
        description="创建新的邮件策略",
        tags=["邮件策略"]
    ),
    retrieve=extend_schema(
        summary="获取邮件策略详情",
        description="获取指定邮件策略的详细信息",
        tags=["邮件策略"]
    ),
    update=extend_schema(
        summary="更新邮件策略",
        description="更新指定邮件策略的信息",
        tags=["邮件策略"]
    ),
    destroy=extend_schema(
        summary="删除邮件策略",
        description="删除指定邮件策略",
        tags=["邮件策略"]
    ),
    partial_update=extend_schema(
        summary="部分更新邮件策略",
        description="部分更新指定邮件策略的信息",
        tags=["邮件策略"]
    )
)
class StrategyViewSet(queryset_utils.UserOwnedModelViewSet):
    """
    邮件策略
    """
    queryset = models.StrategyModel.objects.all().order_by("-id")
    serializer_class = StrategySerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter]
    search_fields = ['name']

    @extend_schema(
        summary="测试邮件发送",
        description="测试邮件服务器配置是否正确",
        tags=["邮件策略"],
        request={
            'application/json': {
                'type': 'object',
                'properties': {
                    'email_server_address': {'type': 'string', 'description': '邮件服务器地址'},
                    'email_server_account': {'type': 'string', 'description': '邮件服务器账号'},
                    'email_server_pwd': {'type': 'string', 'description': '邮件服务器密码'},
                    'target_email': {'type': 'string', 'description': '目标邮箱地址'},
                    'is_ignore_certificate_errors': {'type': 'boolean', 'description': '是否忽略证书错误'},
                    'mail_headers': {'type': 'object', 'description': '邮件头信息'},
                    'port': {'type': 'integer', 'description': '端口号'}
                },
                'required': ['email_server_address', 'email_server_account', 'email_server_pwd', 'target_email']
            }
        },
        responses={
            200: {
                'type': 'object',
                'properties': {
                    'detail': {'type': 'string', 'description': '成功响应信息'}
                }
            },
            400: {
                'type': 'object',
                'properties': {
                    'detail': {'type': 'string', 'description': '错误信息'}
                }
            }
        }
    )
    @action(detail=False, methods=['post'], url_path="test-email")
    def test_email(self, request) -> HttpResponse:
        data = request.data
        email_server_address = data.get("email_server_address", None)
        email_server_account = data.get("email_server_account", None)
        email_server_pwd = data.get("email_server_pwd", None)
        target_email = data.get("target_email", None)
        is_ignore_certificate_errors = data.get("is_ignore_certificate_errors", None)
        mail_headers = data.get("mail_headers", None)
        port = data.get("port", None)
        flag, detail = email_utils.send_email(email_server_address, email_server_account,
                                              email_server_pwd, '测试', '测试', receivers=[target_email],
                                              ignore_cert_errors=is_ignore_certificate_errors,
                                              email_headers=mail_headers,
                                              port=port)
        if not flag:
            return Response({"detail": detail}, status=status.HTTP_400_BAD_REQUEST)
        return Response({"detail": "ok"}, status=status.HTTP_200_OK)


@extend_schema_view(
    list=extend_schema(
        summary="获取邮件模板列表",
        description="获取所有邮件模板",
        tags=["邮件模板"],
        parameters=[
            OpenApiParameter(
                name='name',
                description='搜索名称',
                type=str,
                required=False
            )
        ]
    ),
    create=extend_schema(
        summary="创建邮件模板",
        description="创建新的邮件模板",
        tags=["邮件模板"]
    ),
    retrieve=extend_schema(
        summary="获取邮件模板详情",
        description="获取指定邮件模板的详细信息",
        tags=["邮件模板"]
    ),
    update=extend_schema(
        summary="更新邮件模板",
        description="更新指定邮件模板的信息",
        tags=["邮件模板"]
    ),
    destroy=extend_schema(
        summary="删除邮件模板",
        description="删除指定邮件模板",
        tags=["邮件模板"]
    ),
    partial_update=extend_schema(
        summary="部分更新邮件模板",
        description="部分更新指定邮件模板的信息",
        tags=["邮件模板"]
    )
)
class EmailTemplateViewSet(queryset_utils.UserOwnedModelViewSet):
    """
    邮件模板
    """
    queryset = models.EmailTemplateModel.objects.all().order_by("-id")
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter]
    search_fields = ['name']

    def get_serializer_class(self):
        if self.action == 'list':
            return serializers.EmailTemplateListSerializer
        return serializers.EmailTemplateDetailSerializer


    def list(self, request, *args, **kwargs):
        qs = (
            models.EmailTemplateModel.objects.
            filter(Q(created_by=request.user) | Q(template_type='SYSTEM')).order_by("-id")
        )
        queryset = self.filter_queryset(qs)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        request.data.pop("template_type", None)
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        request.data.pop("template_type", None)
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        return Response(serializer.data)

    def retrieve(self, request, *args, **kwargs):
        lookup_url_kwarg = self.lookup_url_kwarg or self.lookup_field
        # 系统模板
        template_kwargs = {self.lookup_field: self.kwargs[lookup_url_kwarg]}
        template_kwargs.update({"template_type": "SYSTEM"})
        # 自己创建的
        filter_kwargs = {self.lookup_field: self.kwargs[lookup_url_kwarg]}
        filter_kwargs.update({"created_by": request.user})
        instance = get_object_or_404(self.queryset, Q(**template_kwargs) | Q(**filter_kwargs))
        serializer = self.get_serializer(instance)
        return Response(serializer.data)


@extend_schema_view(
    list=extend_schema(
        summary="获取邮件模板文件列表",
        description="获取所有邮件模板关联的文件列表",
        tags=["邮件模板文件"]
    ),
    create=extend_schema(
        summary="上传邮件模板文件",
        description="为邮件模板上传新的关联文件",
        tags=["邮件模板文件"]
    ),
    retrieve=extend_schema(
        summary="获取邮件模板文件详情",
        description="获取指定邮件模板文件的详细信息",
        tags=["邮件模板文件"]
    ),
    update=extend_schema(
        summary="更新邮件模板文件",
        description="更新指定邮件模板的关联文件",
        tags=["邮件模板文件"]
    ),
    destroy=extend_schema(
        summary="删除邮件模板文件",
        description="删除指定的邮件模板关联文件",
        tags=["邮件模板文件"]
    ),
    partial_update=extend_schema(
        summary="部分更新邮件模板文件",
        description="部分更新指定邮件模板文件的信息",
        tags=["邮件模板文件"]
    )
)
class EmailTemplateFileViewSet(viewsets.ModelViewSet):
    """
    邮件模板文件
    """
    queryset = models.EmailTemplateFileModel.objects.all().order_by("-id")
    serializer_class = EmailTemplateFileSerializer
    permission_classes = [permissions.IsAuthenticated]


@extend_schema_view(
    list=extend_schema(
        summary="获取钓鱼页面列表",
        description="获取所有钓鱼页面信息列表",
        tags=["钓鱼页面"],
        parameters=[
            OpenApiParameter(
                name="search",
                description="搜索关键词(页面名称)",
                type=str,
                required=False
            )
        ],
        responses={200: serializers.PhishingPageSerializer(many=True)}
    ),
    create=extend_schema(
        summary="创建钓鱼页面",
        description="创建新的钓鱼页面",
        tags=["钓鱼页面"],
        request=serializers.PhishingPageSerializer,
        responses={
            201: serializers.PhishingPageSerializer,
            400: {"type": "object", "properties": {"error": {"type": "string"}}}
        }
    ),
    retrieve=extend_schema(
        summary="获取钓鱼页面详情",
        description="获取指定钓鱼页面的详细信息",
        tags=["钓鱼页面"],
        responses={
            200: serializers.PhishingPageSerializer,
            404: {"type": "object", "properties": {"error": {"type": "string"}}}
        }
    ),
    update=extend_schema(
        summary="更新钓鱼页面",
        description="更新指定钓鱼页面的信息",
        tags=["钓鱼页面"],
        request=serializers.PhishingPageSerializer,
        responses={
            200: serializers.PhishingPageSerializer,
            400: {"type": "object", "properties": {"error": {"type": "string"}}}
        }
    ),
    destroy=extend_schema(
        summary="删除钓鱼页面",
        description="删除指定钓鱼页面",
        tags=["钓鱼页面"],
        responses={
            204: None,
            404: {"type": "object", "properties": {"error": {"type": "string"}}}
        }
    ),
    partial_update=extend_schema(
        summary="部分更新钓鱼页面",
        description="部分更新指定钓鱼页面的信息",
        tags=["钓鱼页面"],
        request=serializers.PhishingPageSerializer,
        responses={
            200: serializers.PhishingPageSerializer,
            400: {"type": "object", "properties": {"error": {"type": "string"}}},
            404: {"type": "object", "properties": {"error": {"type": "string"}}}
        }
    )
)
class PhishingPageViewSet(queryset_utils.UserOwnedModelViewSet):
    """
    钓鱼页面
    """
    queryset = models.PhishingPageModel.objects.all().order_by("-id")
    serializer_class = serializers.PhishingPageSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter]
    search_fields = ['name']


    def list(self, request, *args, **kwargs):
        qs = (
            models.PhishingPageModel.objects.
            filter(Q(created_by=request.user) | Q(template_type='SYSTEM')).order_by("-id").order_by("-id")
        )
        queryset = self.filter_queryset(qs)

        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def create(self, request, *args, **kwargs):
        request.data.pop("template_type", None)
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        return Response(serializer.data, status=status.HTTP_201_CREATED, headers=headers)

    def update(self, request, *args, **kwargs):
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        request.data.pop("template_type", None)
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)

        if getattr(instance, '_prefetched_objects_cache', None):
            # If 'prefetch_related' has been applied to a queryset, we need to
            # forcibly invalidate the prefetch cache on the instance.
            instance._prefetched_objects_cache = {}

        return Response(serializer.data)


@extend_schema_view(
    list=extend_schema(
        summary="克隆网站内容",
        description="获取指定URL的网站内容",
        tags=["克隆网站"],
        parameters=[
            OpenApiParameter(
                name="url",
                description="要克隆的网站URL",
                type=str,
                required=True,
                location=OpenApiParameter.QUERY
            )
        ],
        responses={
            200: {
                "type": "object",
                "properties": {
                    "detail": {
                        "type": "string",
                        "description": "网站HTML内容"
                    }
                }
            },
            400: {
                "type": "object",
                "properties": {
                    "detail": {
                        "type": "string",
                        "description": "错误信息"
                    }
                }
            }
        }
    )
)
class RequestUrlViewSet(viewsets.ViewSet):
    """
    克隆网站
    """

    def list(self, request, *args, **kwargs):
        url = request.query_params.get("url", None)
        if not url:
            return Response({"detail": "请输入克隆网站"}, status=status.HTTP_400_BAD_REQUEST)
        headers = {
            "accept": "*/*",
            "accept-language": "zh-CN,zh;q=0.9",
            "cache-control": "no-cache",
            "pragma": "no-cache",
            "sec-ch-ua": "\"Google Chrome\";v=\"131\", \"Chromium\";v=\"131\", \"Not_A Brand\";v=\"24\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "sec-fetch-dest": "script",
            "sec-fetch-mode": "no-cors",
            "sec-fetch-site": "same-site",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) "
                          "Chrome/********* Safari/537.36"
        }
        response = requests.get(url, headers=headers, verify=False)
        return Response({"detail": response.content.decode('utf8')})


class ParserEmlViewSet(viewsets.ViewSet):
    """
    解析eml
    """
    permission_classes = []

    def create(self, request, *args, **kwargs):
        eml_file = request.FILES.get('file')
        html_content = email_utils.parse_eml_file(eml_file)
        return Response({"content": html_content})


@extend_schema_view(
    list=extend_schema(
        summary="获取邮件任务列表",
        description="获取所有邮件任务",
        tags=["邮件任务"]
    ),
    create=extend_schema(
        summary="创建邮件任务",
        description="创建新的邮件任务",
        tags=["邮件任务"]
    ),
    retrieve=extend_schema(
        summary="获取邮件任务详情",
        description="获取指定邮件任务的详细信息",
        tags=["邮件任务"]
    ),
    update=extend_schema(
        summary="更新邮件任务",
        description="更新指定邮件任务的信息",
        tags=["邮件任务"]
    ),
    destroy=extend_schema(
        summary="删除邮件任务",
        description="删除指定邮件任务",
        tags=["邮件任务"]
    ),
    partial_update=extend_schema(
        summary="部分更新邮件任务",
        description="部分更新指定邮件任务的信息",
        tags=["邮件任务"]
    )
)
class EmailTaskViewSet(queryset_utils.UserOwnedModelViewSet):
    """
    邮件任务
    """
    queryset = models.EmailTakModel.objects.all()
    serializer_class = serializers.EmailTaskSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter]
    search_fields = ['name']


class FormDataViewSet(mixins.ListModelMixin,
                      GenericViewSet):
    """
    邮件任务
    """
    queryset = models.EmailLog.objects.all()
    serializer_class = serializers.EmailTaskSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [filters.SearchFilter]
    search_fields = ['name']

    def list(self, request, *args, **kwargs):
        exercise_id = kwargs.get('exercise_id')
        email_logs = models.EmailLog.objects.filter(exercise_id=exercise_id).prefetch_related("form_submits")
        results = []
        for email in email_logs:
            form_data = email.form_submits.all().order_by("created_at")
            row_data = []
            for form in form_data:
                row_data.append({
                    "status": form.status,
                    "datetime": form.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                    "data": form.for_data,
                    "is_click": email.is_click,
                    "browser_info": form.browser_info,
                    "os_info": form.os_info,
                })
            results.append({
                "id": email.id,
                "exercise_create_time": email.exercise.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "send_email_time": email.created_at.strftime("%Y-%m-%d %H:%M:%S"),
                "email": email.recipient,
                "is_click": email.is_click,
                "timeline": row_data
            })
        return Response(results)
