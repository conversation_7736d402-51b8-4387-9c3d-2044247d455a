<template>
  <div class="space-y-6 mt-6">
    <!-- 导航菜单 -->
    <nav class="space-y-1">
      <ul class="space-y-4">
        <li>
          <NuxtLink to="/dashboard" class="flex items-center p-3 group rounded-xl transition-all duration-200"
            :class="{ 'bg-gradient-to-r from-blue-500/10 to-blue-600/10 dark:from-blue-500/20 dark:to-blue-600/20': $route.path === '/dashboard' }">
            <div
              class="p-2 mr-3 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg group-hover:scale-110 transition-transform duration-200">
              <Icon name="heroicons:home" class="w-5 h-5 text-white" />
            </div>
            <span class="font-medium text-gray-900 dark:text-gray-100"
              :class="{ 'text-blue-600 dark:text-blue-400': $route.path.startsWith('/dashboard') }">
              {{ $t('menu.dashboard') }}
            </span>
          </NuxtLink>
        </li>

        <template v-if="userInfo?.is_superuser">
          <li>
            <NuxtLink to="/users" class="flex items-center p-3 group rounded-xl transition-all duration-200"
              :class="{ 'bg-gradient-to-r from-purple-500/10 to-purple-600/10 dark:from-purple-500/20 dark:to-purple-600/20': $route.path === '/users' }">
              <div
                class="p-2 mr-3 bg-gradient-to-br from-purple-500 to-purple-600 rounded-lg group-hover:scale-110 transition-transform duration-200">
                <Icon name="heroicons:users" class="w-5 h-5 text-white" />
              </div>
              <span class="font-medium text-gray-900 dark:text-gray-100"
                :class="{ 'text-purple-600 dark:text-purple-400': $route.path === '/users' }">
                {{ $t('menu.users') }}
              </span>
            </NuxtLink>
          </li>
        </template>

        <li>
          <NuxtLink to="/assets"
            :class="['flex items-center w-full p-3 group rounded-xl transition-all duration-200',
              $route.path.startsWith('/assets') ? 'bg-gradient-to-r from-gray-500/10 to-gray-600/10 dark:from-gray-500/20 dark:to-gray-600/20' : '']">
            <div
              class="p-2 mr-3 bg-gradient-to-br from-gray-500 to-gray-600 rounded-lg group-hover:scale-110 transition-transform duration-200">
              <Icon name="heroicons:server-stack" class="w-5 h-5 text-white" />
            </div>
            <span class="flex-1 font-medium text-gray-900 dark:text-gray-100 text-left"
              :class="{ 'text-gray-600 dark:text-gray-400': $route.path.startsWith('/assets') }">
              {{ $t('menu.assets') }}
            </span>
          </NuxtLink>
        </li>

        <!-- 配置菜单(带子菜单) -->
        <li>
          <button @click="toggleConfig" type="button"
            :class="['flex items-center w-full p-3 group rounded-xl transition-all duration-200',
              { 'bg-gradient-to-r from-amber-500/10 to-amber-600/10 dark:from-amber-500/20 dark:to-amber-600/20': $route.path.startsWith('/config') }]">
            <div
              class="p-2 mr-3 bg-gradient-to-br from-amber-500 to-amber-600 rounded-lg group-hover:scale-110 transition-transform duration-200">
              <Icon name="heroicons:exclamation-triangle" class="w-5 h-5 text-white" />
            </div>
            <span class="flex-1 font-medium text-gray-900 dark:text-gray-100 text-left"
              :class="{ 'text-amber-600 dark:text-amber-400': $route.path.startsWith('/config') }">
              {{ $t('menu.config') }}
            </span>
            <Icon name="heroicons:chevron-down"
              class="w-5 h-5 text-gray-500 dark:text-gray-400 transition-transform duration-200"
              :class="{ 'rotate-180': isConfigOpen }" />
          </button>

          <!-- 子菜单 -->
          <div v-show="isConfigOpen" class="mt-2 space-y-3">
            <NuxtLink to="/config/virus"
              :class="['flex items-center pl-14 py-3 text-sm font-medium text-gray-900 rounded-lg dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700',
                $route.path.startsWith('/config/virus') ? 'bg-amber-50 text-blue-600 dark:bg-amber-900/20 dark:text-blue-400' : '']">
              {{ $t('menu.virus') }}
            </NuxtLink>
            <NuxtLink to="/config/phishing/strategy"
              :class="['flex items-center pl-14 py-3 text-sm font-medium text-gray-900 rounded-lg dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700',
                $route.path.startsWith('/config/phishing') ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400' : '']">
              {{ $t('menu.phishing') }}
            </NuxtLink>
            <NuxtLink to="/config/negotiate" class=""
              :class="['flex items-center pl-14 py-3 text-sm font-medium text-gray-900 rounded-lg dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700',
                $route.path.startsWith('/config/negotiate') ? 'bg-blue-50 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400' : '']">
              {{ $t('menu.negotiation') }}
            </NuxtLink>
          </div>
        </li>

        <li>
          <NuxtLink to="/exercise"
            :class="['flex items-center p-3 group rounded-xl transition-all duration-200',
              $route.path.startsWith('/exercise') ? 'bg-gradient-to-r from-cyan-500/10 to-cyan-600/10 dark:from-cyan-500/20 dark:to-cyan-600/20' : '']">
            <div
              class="p-2 mr-3 bg-gradient-to-br from-cyan-500 to-cyan-600 rounded-lg group-hover:scale-110 transition-transform duration-200">
              <Icon name="heroicons:arrow-path" class="w-5 h-5 text-white" />
            </div>
            <span class="font-medium text-gray-900 dark:text-gray-100"
              :class="{ 'text-cyan-600 dark:text-cyan-400': $route.path.startsWith('/exercise') }">
              {{ $t('menu.exercise') }}
            </span>
          </NuxtLink>
        </li>

        <li>
          <NuxtLink to="/family" class=""
            :class="['flex items-center p-3 group rounded-xl transition-all duration-200',
              $route.path.startsWith('/family') ? 'bg-gradient-to-r from-rose-500/10 to-rose-600/10 dark:from-rose-500/20 dark:to-rose-600/20' : '']">
            <div
              class="p-2 mr-3 bg-gradient-to-br from-rose-500 to-rose-600 rounded-lg group-hover:scale-110 transition-transform duration-200">
              <Icon name="heroicons:fire" class="w-5 h-5 text-white" />
            </div>
            <span class="font-medium text-gray-900 dark:text-gray-100"
              :class="{ 'text-rose-600 dark:text-rose-400': $route.path.startsWith('/family') }">
              {{ $t('menu.family') }}
            </span>
          </NuxtLink>
        </li>
      </ul>
    </nav>


  </div>
</template>

<script setup>
import { useAuthStore } from '~/stores/auth'

const authStore = useAuthStore()
const isConfigOpen = ref(false)
const userInfo = computed(() => authStore.user)

// 切换配置菜单的展开/折叠状态
const toggleConfig = () => {
  isConfigOpen.value = !isConfigOpen.value
}
</script>

<style scoped lang="postcss">
.nav-link {
  @apply flex items-center p-3 text-gray-900 rounded-xl transition-all duration-200 dark:text-white hover:bg-gray-100 dark:hover:bg-gray-700;
}

.nav-icon-wrapper {
  @apply flex items-center justify-center w-10 h-10 rounded-lg bg-gray-100 transition-all duration-200 group-hover:bg-blue-500 dark:bg-gray-800 dark:group-hover:bg-blue-600;
}

.nav-icon {
  @apply w-5 h-5 text-gray-500 transition-colors duration-200 group-hover:text-white dark:text-gray-400 dark:group-hover:text-white;
}

.nav-text {
  @apply ml-4 text-sm font-medium;
}

.router-link-active {
  @apply bg-blue-50 dark:bg-blue-900/20;
}

.router-link-active .nav-icon-wrapper {
  @apply bg-blue-500 dark:bg-blue-600;
}

.router-link-active .nav-icon {
  @apply text-white;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar-track {
  @apply bg-transparent rounded-full;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-200/60 rounded-full hover:bg-gray-300/80 dark:bg-gray-700/60 dark:hover:bg-gray-600/80 transition-colors duration-200;
}
</style>