<template>
  <div class="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8 pb-0 mb-8 relative overflow-hidden">
    <!-- Windows Logo Background -->
    <div v-if="isWindowsSystem"
      class="absolute bottom-0 right-0 transform translate-x-8 translate-y-8 rotate-12 opacity-[0.03] dark:opacity-[0.04]">
      <Icon name="simple-icons:windows" class="w-64 h-64 text-blue-500" />
    </div>

    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-2xl font-bold dark:text-white">{{ $t('infection.device_infection_details') }}</h1>
        <div class="flex items-center space-x-2 mt-2">
          <p class="text-gray-500 dark:text-gray-400">{{ $t('infection.device_id') }}: {{ deviceId }}</p>
          <div class="flex items-center space-x-1.5">
            <div :class="[
              'w-2 h-2 rounded-full',
              isOnline ? 'bg-green-500' : 'bg-red-500'
            ]"></div>
            <span :class="[
              'text-sm',
              isOnline ? 'text-green-500' : 'text-red-500'
            ]">{{ isOnline ? $t('all.online') : $t('all.offline') }}</span>
          </div>
        </div>
      </div>
      <Button type="outline" @click="router.back()">
        {{ $t('all.back') }}
        <template #icon>
          <Icon name="heroicons:arrow-left" class="w-4 h-4" />
        </template>
      </Button>
    </div>

    <!-- 基本信息表格 -->
    <div class="mb-12">
      <table class="w-full">
        <tbody>
          <tr>
            <td :class="['py-2 align-top', classes]">
              <div class="flex items-center space-x-2">
                <Icon name="heroicons:clipboard-document-list" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
                <span class="text-gray-500 dark:text-gray-400">{{ $t('infection.hostname') }}:</span>
              </div>
            </td>
            <td class="py-2 align-top dark:text-gray-200" style="width: 260px">
              <span>{{ currentInfection?.hostname }}</span>
            </td>
            <td :class="['py-2 align-top', classes]">
              <div class="flex items-center space-x-2">
                <Icon name="heroicons:user" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
                <span class="text-gray-500 dark:text-gray-400">{{ $t('table.username') }}:</span>
              </div>
            </td>
            <td class="py-2 align-top dark:text-gray-200">
              <span>{{ currentInfection?.username }}</span>
            </td>
          </tr>
          <tr>
            <td :class="['py-2 align-top', classes]">
              <div class="flex items-center space-x-2">
                <Icon name="heroicons:server-stack" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
                <span class="text-gray-500 dark:text-gray-400">{{ $t('table.ip_address') }}:</span>
              </div>
            </td>
            <td class="py-2 align-top dark:text-gray-200" style="width: 260px">
              <span>{{ currentInfection?.ip_address }}</span>
            </td>
            <td :class="['py-2 align-top', classes]">
              <div class="flex items-center space-x-2">
                <Icon name="heroicons:folder" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
                <span class="text-gray-500 dark:text-gray-400">{{ $t('infection.program_path') }}:</span>
              </div>
            </td>
            <td class="py-2 align-top dark:text-gray-200" colspan="3">
              <code class="font-mono text-sm">{{ currentInfection?.exec_path }}</code>
            </td>
          </tr>
          <tr>
            <td class="py-2 align-top">
              <div class="flex items-center space-x-2">
                <Icon name="heroicons:computer-desktop" class="w-4 h-4 text-gray-500 dark:text-gray-400" />
                <span class="text-gray-500 dark:text-gray-400">{{ $t('infection.system_version') }}:</span>
              </div>
            </td>
            <td class="py-2 align-top dark:text-gray-200" colspan="3">
              <span>{{ currentInfection?.system_version || "Microsoft Windows 11 专业版" }}</span>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from '#imports'
import Button from '~/components/common/Button.vue'

const { locale } = useI18n()
const router = useRouter()
const props = defineProps({
  deviceId: {
    type: String,
    required: true
  },
  isOnline: {
    type: Boolean,
    default: false
  },
  currentInfection: {
    type: Object,
    default: () => ({})
  }
})

// 判断是否为Windows系统
const isWindowsSystem = computed(() => {
  const systemVersion = props.currentInfection?.system_version || "Microsoft Windows 11 专业版"
  return systemVersion.toLowerCase().includes('windows')
})

const classes = computed(() => {
  return {
    'w-28': locale.value === 'zh',
    'w-40': locale.value === 'en'
  }
})
</script>

<style scoped>
.overflow-hidden {
  overflow: hidden;
}
</style>