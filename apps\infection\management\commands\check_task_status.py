from django.core.management.base import BaseCommand
from django.utils import timezone
from datetime import timedelta
from apps.infection.models import DeviceCommand


class Command(BaseCommand):
    help = '检查和清理任务状态'

    def add_arguments(self, parser):
        parser.add_argument(
            '--fix',
            action='store_true',
            help='修复卡住的任务状态',
        )
        parser.add_argument(
            '--timeout',
            type=int,
            default=300,
            help='任务超时时间（秒），默认300秒',
        )

    def handle(self, *args, **options):
        fix_tasks = options['fix']
        timeout_seconds = options['timeout']
        
        # 计算超时时间点
        timeout_time = timezone.now() - timedelta(seconds=timeout_seconds)
        
        # 查找所有状态的任务
        pending_tasks = DeviceCommand.objects.filter(status='PENDING')
        in_progress_tasks = DeviceCommand.objects.filter(status='IN_PROGRESS')
        success_tasks = DeviceCommand.objects.filter(status='SUCCESS')
        failed_tasks = DeviceCommand.objects.filter(status='FAILED')
        
        # 查找超时的IN_PROGRESS任务
        timeout_tasks = in_progress_tasks.filter(updated_at__lt=timeout_time)
        
        self.stdout.write(f"任务状态统计:")
        self.stdout.write(f"  待执行 (PENDING): {pending_tasks.count()}")
        self.stdout.write(f"  执行中 (IN_PROGRESS): {in_progress_tasks.count()}")
        self.stdout.write(f"  执行成功 (SUCCESS): {success_tasks.count()}")
        self.stdout.write(f"  执行失败 (FAILED): {failed_tasks.count()}")
        self.stdout.write(f"  超时任务 (>{timeout_seconds}秒): {timeout_tasks.count()}")
        
        if timeout_tasks.exists():
            self.stdout.write(f"\n超时任务详情:")
            for task in timeout_tasks:
                self.stdout.write(
                    f"  ID: {task.pk}, 命令: {task.command}, "
                    f"设备: {task.device.device_id if task.device else 'N/A'}, "
                    f"更新时间: {task.updated_at}"
                )
        
        # 查找重复的restore_wallpaper任务
        restore_tasks = DeviceCommand.objects.filter(
            command='restore_wallpaper',
            status__in=['PENDING', 'IN_PROGRESS']
        ).order_by('device', 'created_at')
        
        if restore_tasks.exists():
            self.stdout.write(f"\n待处理的restore_wallpaper任务:")
            for task in restore_tasks:
                self.stdout.write(
                    f"  ID: {task.pk}, 状态: {task.status}, "
                    f"设备: {task.device.device_id if task.device else 'N/A'}, "
                    f"创建时间: {task.created_at}"
                )
        
        if fix_tasks:
            if timeout_tasks.exists():
                # 将超时任务标记为失败
                updated_count = timeout_tasks.update(
                    status='FAILED',
                    response={'status': 'failed', 'message': '任务执行超时', 'timeout': True}
                )
                self.stdout.write(
                    self.style.SUCCESS(f"已将 {updated_count} 个超时任务标记为失败")
                )
            else:
                self.stdout.write("没有需要修复的超时任务")
        else:
            if timeout_tasks.exists():
                self.stdout.write(
                    self.style.WARNING(f"使用 --fix 参数来修复 {timeout_tasks.count()} 个超时任务")
                )
