<template>
  <section id="services"
    class="relative py-24 sm:py-32 bg-gradient-to-br from-[#0A1628] via-[#0F2543] to-[#1A3A6A] overflow-hidden">
    <!-- 背景装饰 -->
    <div class="absolute inset-0">
      <div class="absolute inset-0 bg-[url('/grid.svg')] bg-center opacity-10"></div>
      <div class="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-blue-500/5"></div>
    </div>

    <div class="relative mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
      <div class="mx-auto max-w-2xl lg:text-center mb-16">
        <span
          class="inline-flex items-center px-4 py-1.5 rounded-full text-xs font-medium bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg">
          {{ $t('home.third.tag') }}
        </span>
        <h2 class="mt-6 text-3xl font-bold tracking-tight text-white sm:text-4xl">
          {{ $t('home.third.title') }}
        </h2>
        <p class="mt-4 text-lg text-gray-300">
          {{ $t('home.third.description') }}
        </p>
      </div>

      <!-- 服务阶段 -->
      <div class="relative">
        <!-- 时间轴线 -->
        <div
          class="absolute left-6 -translate-x-1/2 top-0 bottom-0 w-px bg-gradient-to-b from-blue-500/30 via-blue-500/30 to-transparent">
        </div>

        <!-- 准备阶段 -->
        <div class="relative mb-20">
          <!-- 时间轴圆点 -->
          <div
            class="absolute left-6 -translate-x-1/2 w-5 h-5 rounded-full bg-gradient-to-r from-blue-500 to-blue-600 shadow-lg shadow-blue-500/50 ring-4 ring-[#0A1628]">
          </div>

          <div class="ml-16">
            <h3 class="text-2xl font-bold text-white mb-8">
              {{ $t('home.third.preparation_phase.title') }}
            </h3>

            <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <!-- 演练方案定制 -->
              <div
                class="group relative overflow-hidden rounded-2xl bg-white/5 hover:bg-white/10 backdrop-blur-lg p-6 transition-all duration-300">
                <div
                  class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                </div>
                <div class="relative">
                  <div class="flex items-center mb-3">
                    <Icon name="heroicons:clipboard-document-list" class="w-6 h-6 min-w-6 text-blue-400 mr-2" />
                    <h4 class="text-lg font-semibold text-white">
                      {{ $t('home.third.preparation_phase.card_1') }}
                    </h4>
                  </div>
                  <p class="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                    {{ $t('home.third.preparation_phase.description_1') }}
                  </p>
                </div>
              </div>

              <!-- 需求确认 -->
              <div
                class="group relative overflow-hidden rounded-2xl bg-white/5 hover:bg-white/10 backdrop-blur-lg p-6 transition-all duration-300">
                <div
                  class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                </div>
                <div class="relative">
                  <div class="flex items-center mb-3">
                    <Icon name="heroicons:check-circle" class="w-6 h-6 min-w-6 text-blue-400 mr-2" />
                    <h4 class="text-lg font-semibold text-white">
                      {{ $t('home.third.preparation_phase.card_2') }}
                    </h4>
                  </div>
                  <p class="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                    {{ $t('home.third.preparation_phase.description_2') }}
                  </p>
                </div>
              </div>

              <!-- 演练项目设置 -->
              <div
                class="group relative overflow-hidden rounded-2xl bg-white/5 hover:bg-white/10 backdrop-blur-lg p-6 transition-all duration-300">
                <div
                  class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                </div>
                <div class="relative">
                  <div class="flex items-center mb-3">
                    <Icon name="heroicons:cog-6-tooth" class="w-6 h-6 min-w-6 text-blue-400 mr-2" />
                    <h4 class="text-lg font-semibold text-white">
                      {{ $t('home.third.preparation_phase.card_3') }}
                    </h4>
                  </div>
                  <p class="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                    {{ $t('home.third.preparation_phase.description_3') }}
                  </p>
                </div>
              </div>

              <!-- 应急响应小组建立 -->
              <div
                class="group relative overflow-hidden rounded-2xl bg-white/5 hover:bg-white/10 backdrop-blur-lg p-6 transition-all duration-300">
                <div
                  class="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                </div>
                <div class="relative">
                  <div class="flex items-center mb-3">
                    <Icon name="heroicons:user-group" class="w-6 h-6 min-w-6 text-blue-400 mr-2" />
                    <h4 class="text-lg font-semibold text-white">
                      {{ $t('home.third.preparation_phase.card_4') }}
                    </h4>
                  </div>
                  <p class="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                    {{ $t('home.third.preparation_phase.description_4') }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 实施阶段 -->
        <div class="relative mb-20">
          <div
            class="absolute left-6 -translate-x-1/2 w-5 h-5 rounded-full bg-gradient-to-r from-purple-500 to-purple-600 shadow-lg shadow-purple-500/50 ring-4 ring-[#0A1628]">
          </div>

          <div class="ml-16">
            <h3 class="text-2xl font-bold text-white mb-8">
              {{ $t('home.third.implementation_phase.title') }}
            </h3>

            <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <!-- 攻击触达 -->
              <div
                class="group relative overflow-hidden rounded-2xl bg-white/5 hover:bg-white/10 backdrop-blur-lg p-6 transition-all duration-300">
                <div
                  class="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                </div>
                <div class="relative">
                  <div class="flex items-center mb-3">
                    <Icon name="heroicons:bolt" class="w-6 h-6 min-w-6 text-purple-400 mr-2" />
                    <h4 class="text-lg font-semibold text-white">
                      {{ $t('home.third.implementation_phase.card_1') }}
                    </h4>
                  </div>
                  <p class="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                    {{ $t('home.third.implementation_phase.description_1') }}
                  </p>
                </div>
              </div>

              <!-- 感染扩散 -->
              <div
                class="group relative overflow-hidden rounded-2xl bg-white/5 hover:bg-white/10 backdrop-blur-lg p-6 transition-all duration-300">
                <div
                  class="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                </div>
                <div class="relative">
                  <div class="flex items-center mb-3">
                    <Icon name="heroicons:globe-alt" class="w-6 h-6 min-w-6 text-purple-400 mr-2" />
                    <h4 class="text-lg font-semibold text-white">
                      {{ $t('home.third.implementation_phase.card_2') }}
                    </h4>
                  </div>
                  <p class="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                    {{ $t('home.third.implementation_phase.description_2') }}
                  </p>
                </div>
              </div>

              <!-- 应急响应处置 -->
              <div
                class="group relative overflow-hidden rounded-2xl bg-white/5 hover:bg-white/10 backdrop-blur-lg p-6 transition-all duration-300">
                <div
                  class="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                </div>
                <div class="relative">
                  <div class="flex items-center mb-3">
                    <Icon name="heroicons:exclamation-triangle" class="w-6 h-6 min-w-6 text-purple-400 mr-2" />
                    <h4 class="text-lg font-semibold text-white">
                      {{ $t('home.third.implementation_phase.card_3') }}
                    </h4>
                  </div>
                  <p class="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                    {{ $t('home.third.implementation_phase.description_3') }}
                  </p>
                </div>
              </div>

              <!-- 一键恢复/下线 -->
              <div
                class="group relative overflow-hidden rounded-2xl bg-white/5 hover:bg-white/10 backdrop-blur-lg p-6 transition-all duration-300">
                <div
                  class="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                </div>
                <div class="relative">
                  <div class="flex items-center mb-3">
                    <Icon name="heroicons:arrow-path" class="w-6 h-6 min-w-6 text-purple-400 mr-2" />
                    <h4 class="text-lg font-semibold text-white">
                      {{ $t('home.third.implementation_phase.card_4') }}
                    </h4>
                  </div>
                  <p class="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                    {{ $t('home.third.implementation_phase.description_4') }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 总结阶段 -->
        <div class="relative">
          <div
            class="absolute left-6 -translate-x-1/2 w-5 h-5 rounded-full bg-gradient-to-r from-indigo-500 to-indigo-600 shadow-lg shadow-indigo-500/50 ring-4 ring-[#0A1628]">
          </div>

          <div class="ml-16">
            <h3 class="text-2xl font-bold text-white mb-8">
              {{ $t('home.third.summary_phase.title') }}
            </h3>

            <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-4">
              <!-- 总结报告导出 -->
              <div
                class="group relative overflow-hidden rounded-2xl bg-white/5 hover:bg-white/10 backdrop-blur-lg p-6 transition-all duration-300">
                <div
                  class="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                </div>
                <div class="relative">
                  <div class="flex items-center mb-3">
                    <Icon name="heroicons:chart-bar" class="w-6 h-6 min-w-6 text-indigo-400 mr-2" />
                    <h4 class="text-lg font-semibold text-white">
                      {{ $t('home.third.summary_phase.card_1') }}
                    </h4>
                  </div>
                  <p class="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                    {{ $t('home.third.summary_phase.description_1') }}
                  </p>
                </div>
              </div>

              <!-- 人员意识培训 -->
              <div
                class="group relative overflow-hidden rounded-2xl bg-white/5 hover:bg-white/10 backdrop-blur-lg p-6 transition-all duration-300">
                <div
                  class="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                </div>
                <div class="relative">
                  <div class="flex items-center mb-3">
                    <Icon name="heroicons:book-open" class="w-6 h-6 min-w-6 text-indigo-400 mr-2" />
                    <h4 class="text-lg font-semibold text-white">
                      {{ $t('home.third.summary_phase.card_2') }}
                    </h4>
                  </div>
                  <p class="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                    {{ $t('home.third.summary_phase.description_2') }}
                  </p>
                </div>
              </div>

              <!-- 安全建设建议 -->
              <div
                class="group relative overflow-hidden rounded-2xl bg-white/5 hover:bg-white/10 backdrop-blur-lg p-6 transition-all duration-300">
                <div
                  class="absolute inset-0 bg-gradient-to-br from-indigo-500/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity">
                </div>
                <div class="relative">
                  <div class="flex items-center mb-3">
                    <Icon name="heroicons:shield-check" class="w-6 h-6 min-w-6 text-indigo-400 mr-2" />
                    <h4 class="text-lg font-semibold text-white">
                      {{ $t('home.third.summary_phase.card_3') }}
                    </h4>
                  </div>
                  <p class="text-gray-400 text-sm leading-relaxed group-hover:text-gray-300 transition-colors">
                    {{ $t('home.third.summary_phase.description_3') }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
</script>